# Development Environment Configuration for Real-Time Workers

# Application Environment
NODE_ENV=development
VUE_APP_ENV=development

# Feature Flags
VUE_APP_ENABLE_PRODUCTION_FEATURES=true
VUE_APP_ENABLE_METRICS=true
VUE_APP_ENABLE_ALERTS=true
VUE_APP_ENABLE_HEALTH_CHECK=true
VUE_APP_ENABLE_AUTO_REFRESH=true

# Refresh Intervals (milliseconds) - Faster for development
VUE_APP_DEFAULT_REFRESH_INTERVAL=10000
VUE_APP_FAST_REFRESH_INTERVAL=5000
VUE_APP_HEALTH_CHECK_INTERVAL=30000

# Performance Settings
VUE_APP_MAX_CONCURRENT_REQUESTS=5
VUE_APP_REQUEST_TIMEOUT=15000
VUE_APP_RETRY_ATTEMPTS=2

# Alert Configuration
VUE_APP_MAX_ALERTS_DISPLAY=10
VUE_APP_ALERT_CHECK_INTERVAL=10000

# Metrics Configuration
VUE_APP_CPU_WARNING_THRESHOLD=60
VUE_APP_CPU_CRITICAL_THRESHOLD=80
VUE_APP_MEMORY_WARNING_THRESHOLD=70
VUE_APP_MEMORY_CRITICAL_THRESHOLD=85
VUE_APP_ERROR_RATE_WARNING_THRESHOLD=1
VUE_APP_ERROR_RATE_CRITICAL_THRESHOLD=3

# Logging
VUE_APP_LOG_LEVEL=debug
VUE_APP_ENABLE_DEBUG_LOGGING=true

# Development Features
VUE_APP_ENABLE_MOCK_DATA=true
VUE_APP_ENABLE_TEST_ACTIONS=true
