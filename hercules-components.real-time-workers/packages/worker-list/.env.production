# Production Environment Configuration for Real-Time Workers

# Application Environment
NODE_ENV=production
VUE_APP_ENV=production

# Feature Flags
VUE_APP_ENABLE_PRODUCTION_FEATURES=true
VUE_APP_ENABLE_METRICS=true
VUE_APP_ENABLE_ALERTS=true
VUE_APP_ENABLE_HEALTH_CHECK=true
VUE_APP_ENABLE_AUTO_REFRESH=true

# Refresh Intervals (milliseconds)
VUE_APP_DEFAULT_REFRESH_INTERVAL=30000
VUE_APP_FAST_REFRESH_INTERVAL=10000
VUE_APP_HEALTH_CHECK_INTERVAL=60000

# Performance Settings
VUE_APP_MAX_CONCURRENT_REQUESTS=10
VUE_APP_REQUEST_TIMEOUT=30000
VUE_APP_RETRY_ATTEMPTS=3

# Alert Configuration
VUE_APP_MAX_ALERTS_DISPLAY=5
VUE_APP_ALERT_CHECK_INTERVAL=15000

# Metrics Configuration
VUE_APP_CPU_WARNING_THRESHOLD=70
VUE_APP_CPU_CRITICAL_THRESHOLD=90
VUE_APP_MEMORY_WARNING_THRESHOLD=80
VUE_APP_MEMORY_CRITICAL_THRESHOLD=95
VUE_APP_ERROR_RATE_WARNING_THRESHOLD=2
VUE_APP_ERROR_RATE_CRITICAL_THRESHOLD=5

# Logging
VUE_APP_LOG_LEVEL=warn
VUE_APP_ENABLE_DEBUG_LOGGING=false

# Security
VUE_APP_ENABLE_MOCK_DATA=false
VUE_APP_ENABLE_TEST_ACTIONS=false
