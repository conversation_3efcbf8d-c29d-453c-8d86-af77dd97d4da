# Real-Time Worker List Component

## Overview

The Real-Time Worker List component provides a comprehensive dashboard for monitoring and managing real-time workers in the EOD (End of Day) processing system. It displays worker status, performance metrics, and provides functionality to reprocess failed postings.

## Features

- **Real-time Worker Monitoring**: Display worker status, success/failure counts, and queue information
- **Search and Filtering**: Search workers by name and sort by failed postings count
- **Reprocess Functionality**: Reprocess failed postings using `startWorkflowProcessV2`
- **Status Indicators**: Visual indicators for worker health (HEALTHY, SLOW, UNHEALTHY)
- **Interactive Cards**: Hover tooltips and action buttons for each worker

## Configuration

### Required Props

```typescript
interface WorkerListProps {
  tenantId: string | number;     // Tenant identifier
  coaid: string;                 // COA identifier
  cbuid: string;                 // CBU identifier (timestamp)
  atalantaServiceUrl: string;    // Service URL for worker data
}
```

### Optional Props for Reprocessing

```typescript
interface ReprocessProps {
  auraAppBaseUrl?: string;       // Aura app base URL for reprocessing
  coaCode?: string;              // COA code for reprocessing
  phaseId?: string;              // Phase ID (default: 'ACTIVE')
  pdaCode?: string;              // PDA code for reprocessing
  rdCode?: string;               // RD code (default: 'RQDINZZ0326')
}
```

## Usage

### Basic Usage

```vue
<template>
  <WorkerList
    :tenantId="600309"
    coaid="COAUS001007"
    cbuid="1640995200000"
    atalantaServiceUrl="https://your-service-url.com"
  />
</template>
```

### With Reprocess Functionality

```vue
<template>
  <WorkerList
    :tenantId="600309"
    coaid="COAUS001007"
    cbuid="1640995200000"
    atalantaServiceUrl="https://your-service-url.com"
    auraAppBaseUrl="https://aura-app-url.com"
    coaCode="COAUS001007"
    phaseId="ACTIVE"
    pdaCode="PDA001"
    rdCode="RQDINZZ0326"
  />
</template>
```

## Reprocess Functionality

The component includes a reprocess feature that allows users to reprocess failed postings for individual workers. When a worker has failed postings, a reprocess button appears in the worker card.

### How Reprocessing Works

1. **User Action**: User clicks the reprocess button (refresh icon) on a worker card with failed postings
2. **Validation**: Component validates that required reprocess props are provided
3. **API Call**: Calls `startWorkflowProcessV2` with the following payload structure:
   - `auraAppBaseUrl`: Base URL for the Aura application
   - `coaCode`: Chart of Accounts code
   - `cbuId`: Current Business Unit identifier
   - `phaseId`: Processing phase (default: 'ACTIVE')
   - `pdaCode`: PDA code for the reprocess operation
   - `rdCode`: Request Definition code (default: 'RQDINZZ0326')
   - `tenantId`: Tenant identifier
   - `workerCode`: Specific worker code to reprocess

### Payload Structure

The reprocess function creates a workflow payload following the Rhea service pattern:

```typescript
{
  request: {
    tenantId: string,
    requestType: 'BR' // Business Request
  },
  payload: {
    auraAppBaseUrl: { value: string, type: 'String' },
    coaCode: { value: string, type: 'String' },
    cbuId: { value: string, type: 'String' },
    phaseId: { value: string, type: 'String' },
    pdaCode: { value: string, type: 'String' },
    rdCode: { value: string, type: 'String' },
    tenantId: { value: string, type: 'String' },
    workerCode: { value: string, type: 'String' },
    processKey: { value: string, type: 'String' },
    actionType: { value: 'Reprocess Worker', type: 'String' },
    requestedBy: { value: string, type: 'String' },
    requestedTime: { value: string, type: 'String' },
    isRetry: { value: true, type: 'Boolean' }
  }
}
```

## API Integration

The component integrates with the following services:

1. **Worker Data**: Fetches real-time worker information from the Atalanta service
2. **Reprocess**: Uses the Rhea service to submit reprocess requests via `startWorkflowProcessV2`

## Error Handling

- Validates required props before making reprocess requests
- Provides console logging for debugging
- Gracefully handles API failures
- Refreshes worker data after successful reprocess requests

## Development

### Running the Component

```bash
npm run serve
```

### Building for Production

```bash
npm run build
```

### Testing

```bash
npm run test:unit
```

## Contact

For questions, bugs, or setup issues, please contact the development team.