/**
 * Unit tests for WorkerList Vue Component with Production Features
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import { nextTick } from 'vue';
import WorkerList from '../../src/worker-list/WorkerList.vue';
import type { EnhancedRealTimeWorker } from '../../src/worker-list/types';

// Mock the services
vi.mock('../../src/worker-list/services/Services', () => ({
  getRealTimeWorkerData: vi.fn(),
  getEnhancedRealTimeWorkerData: vi.fn(),
  performProductionHealthCheck: vi.fn(),
  restartProductionWorker: vi.fn(),
  acknowledgeWorkerAlert: vi.fn()
}));

// Mock Zeta GDS components
vi.mock('@zeta-gds/components', () => ({
  ZText: { template: '<span><slot /></span>' },
  ZInput: { template: '<input />' },
  ZButton: { template: '<button><slot /></button>' },
  ZResult: { template: '<div><slot /></div>' },
  ZCard: { template: '<div class="z-card"><slot /></div>' },
  ZDivider: { template: '<div class="divider"></div>' },
  ZTag: { template: '<span class="tag"><slot /></span>' },
  ZIcon: { template: '<i><slot /></i>' }
}));

// Mock Hercules component
vi.mock('@hercules/component-core', () => ({
  HerculesBusinessComponentRoot: { template: '<div><slot /></div>' }
}));

// Mock icons
vi.mock('@vicons/ionicons5', () => ({
  Ellipse: { template: '<svg></svg>' },
  AlertCircle: { template: '<svg></svg>' }
}));

describe('WorkerList Component', () => {
  const defaultProps = {
    tenantId: '600309',
    coaid: 'COAUS001007',
    cbuid: '1640995200000',
    atalantaServiceUrl: 'https://test.example.com'
  };

  const mockWorkerData = {
    data: {
      tenantID: 600309,
      totalPostings: 1000000,
      realTimeWorkers: [
        {
          code: 'RTWUS0010',
          name: 'Test Worker 1',
          successfullPostingsCount: 95000,
          failedPostingsCount: 100,
          pendingPostingsCount: 50,
          status: 'HEALTHY',
          clusterName: 'test-cluster',
          description: 'Test worker',
          dlqProcessingStatus: 'INACTIVE'
        },
        {
          code: 'RTWUS0011',
          name: 'Test Worker 2',
          successfullPostingsCount: 80000,
          failedPostingsCount: 500,
          pendingPostingsCount: 100,
          status: 'SLOW',
          clusterName: 'test-cluster',
          description: 'Slow worker',
          dlqProcessingStatus: 'ACTIVE'
        }
      ] as EnhancedRealTimeWorker[]
    }
  };

  let wrapper: VueWrapper<any>;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Functionality', () => {
    it('should render component with default props', async () => {
      const { getRealTimeWorkerData } = await import('../../src/worker-list/services/Services');
      vi.mocked(getRealTimeWorkerData).mockResolvedValue(mockWorkerData);

      wrapper = mount(WorkerList, {
        props: defaultProps
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.find('.header-row').exists()).toBe(true);
      expect(wrapper.find('.total-postings').exists()).toBe(true);
    });

    it('should display search input and sort button', () => {
      wrapper = mount(WorkerList, {
        props: defaultProps
      });

      expect(wrapper.find('input').exists()).toBe(true);
      expect(wrapper.find('button').exists()).toBe(true);
    });

    it('should fetch worker data on mount', async () => {
      const { getRealTimeWorkerData } = await import('../../src/worker-list/services/Services');
      const mockFetch = vi.mocked(getRealTimeWorkerData).mockResolvedValue(mockWorkerData);

      wrapper = mount(WorkerList, {
        props: defaultProps
      });

      await nextTick();

      expect(mockFetch).toHaveBeenCalledWith({
        tenantId: '600309',
        coaid: 'COAUS001007',
        cbuid: '1640995200000',
        atlantaUrl: 'https://test.example.com',
        includeMetrics: true,
        includeAlerts: true
      });
    });
  });

  describe('Production Features', () => {
    const productionProps = {
      ...defaultProps,
      enableProductionFeatures: true,
      enableMetrics: true,
      enableAlerts: true,
      enableHealthCheck: true
    };

    it('should use enhanced data fetching when production features enabled', async () => {
      const { getEnhancedRealTimeWorkerData } = await import('../../src/worker-list/services/Services');
      const mockEnhancedFetch = vi.mocked(getEnhancedRealTimeWorkerData).mockResolvedValue(mockWorkerData);

      wrapper = mount(WorkerList, {
        props: productionProps
      });

      await nextTick();

      expect(mockEnhancedFetch).toHaveBeenCalledWith({
        tenantId: '600309',
        coaid: 'COAUS001007',
        cbuid: '1640995200000',
        atlantaUrl: 'https://test.example.com',
        includeMetrics: true,
        includeAlerts: true
      });
    });

    it('should perform health check when enabled', async () => {
      const { getEnhancedRealTimeWorkerData, performProductionHealthCheck } = await import('../../src/worker-list/services/Services');

      vi.mocked(getEnhancedRealTimeWorkerData).mockResolvedValue(mockWorkerData);
      const mockHealthCheck = vi.mocked(performProductionHealthCheck).mockResolvedValue({
        overallHealth: 'HEALTHY',
        totalWorkers: 2,
        healthyWorkers: 2,
        unhealthyWorkers: 0,
        criticalAlerts: 0,
        recommendations: []
      });

      wrapper = mount(WorkerList, {
        props: productionProps
      });

      await nextTick();

      expect(mockHealthCheck).toHaveBeenCalledWith({
        tenantId: '600309',
        coaid: 'COAUS001007',
        cbuid: '1640995200000',
        atlantaUrl: 'https://test.example.com'
      });
    });

    it('should display health status bar when health data available', async () => {
      const { getEnhancedRealTimeWorkerData, performProductionHealthCheck } = await import('../../src/worker-list/services/Services');

      vi.mocked(getEnhancedRealTimeWorkerData).mockResolvedValue(mockWorkerData);
      vi.mocked(performProductionHealthCheck).mockResolvedValue({
        overallHealth: 'DEGRADED',
        totalWorkers: 2,
        healthyWorkers: 1,
        unhealthyWorkers: 1,
        criticalAlerts: 0,
        recommendations: ['1 workers need attention']
      });

      wrapper = mount(WorkerList, {
        props: productionProps
      });

      await nextTick();
      await nextTick(); // Wait for health check to complete

      expect(wrapper.find('.health-status-bar').exists()).toBe(true);
      expect(wrapper.find('.health-degraded').exists()).toBe(true);
    });
  });

  describe('Worker Actions', () => {
    it('should handle worker restart action', async () => {
      const { restartProductionWorker } = await import('../../src/worker-list/services/Services');
      const mockRestart = vi.mocked(restartProductionWorker).mockResolvedValue(true);

      wrapper = mount(WorkerList, {
        props: {
          ...defaultProps,
          enableProductionFeatures: true
        }
      });

      const vm = wrapper.vm;
      const result = await vm.handleWorkerRestart('RTWUS0010', 'Test restart');

      expect(mockRestart).toHaveBeenCalledWith({
        tenantId: '600309',
        coaid: 'COAUS001007',
        cbuid: '1640995200000',
        atlantaUrl: 'https://test.example.com',
        workerCode: 'RTWUS0010',
        reason: 'Test restart'
      });
      expect(result).toBe(true);
    });

    it('should handle alert acknowledgment', async () => {
      const { acknowledgeWorkerAlert } = await import('../../src/worker-list/services/Services');
      const mockAcknowledge = vi.mocked(acknowledgeWorkerAlert).mockResolvedValue(true);

      wrapper = mount(WorkerList, {
        props: {
          ...defaultProps,
          enableProductionFeatures: true
        }
      });

      const vm = wrapper.vm;
      const result = await vm.handleAlertAcknowledge('RTWUS0010', 'alert-001');

      expect(mockAcknowledge).toHaveBeenCalledWith({
        tenantId: '600309',
        coaid: 'COAUS001007',
        cbuid: '1640995200000',
        atlantaUrl: 'https://test.example.com',
        workerCode: 'RTWUS0010',
        alertId: 'alert-001'
      });
      expect(result).toBe(true);
    });
  });

  describe('Search and Filtering', () => {
    beforeEach(async () => {
      const { getRealTimeWorkerData } = await import('../../src/worker-list/services/Services');
      vi.mocked(getRealTimeWorkerData).mockResolvedValue(mockWorkerData);

      wrapper = mount(WorkerList, {
        props: defaultProps
      });

      await nextTick();
    });

    it('should filter workers based on search query', async () => {
      const searchInput = wrapper.find('input');
      await searchInput.setValue('Test Worker 1');

      const vm = wrapper.vm;
      expect(vm.filteredAndSortedWorkers).toHaveLength(1);
      expect(vm.filteredAndSortedWorkers[0].name).toBe('Test Worker 1');
    });

    it('should sort workers by failed count', async () => {
      const sortButton = wrapper.find('button');
      await sortButton.trigger('click');

      const vm = wrapper.vm;
      expect(vm.sortOrder).toBe('desc');

      // Worker 2 has more failed postings (500) than Worker 1 (100)
      expect(vm.filteredAndSortedWorkers[0].name).toBe('Test Worker 2');
    });
  });

  describe('Error Handling', () => {
    it('should display error message when data fetch fails', async () => {
      const { getRealTimeWorkerData } = await import('../../src/worker-list/services/Services');
      vi.mocked(getRealTimeWorkerData).mockRejectedValue(new Error('API Error'));

      wrapper = mount(WorkerList, {
        props: defaultProps
      });

      await nextTick();

      const vm = wrapper.vm;
      expect(vm.error).toBe('API Error');
      expect(wrapper.find('.result-container').exists()).toBe(true);
    });

    it('should handle health check failures gracefully', async () => {
      const { getEnhancedRealTimeWorkerData, performProductionHealthCheck } = await import('../../src/worker-list/services/Services');

      vi.mocked(getEnhancedRealTimeWorkerData).mockResolvedValue(mockWorkerData);
      vi.mocked(performProductionHealthCheck).mockRejectedValue(new Error('Health check failed'));

      wrapper = mount(WorkerList, {
        props: {
          ...defaultProps,
          enableProductionFeatures: true,
          enableHealthCheck: true
        }
      });

      await nextTick();

      // Should not crash and should still display worker data
      const vm = wrapper.vm;
      expect(vm.workers).toHaveLength(2);
      expect(vm.error).toBeNull();
    });
  });

  describe('Auto-refresh', () => {
    it('should setup auto-refresh when interval is provided', async () => {
      vi.useFakeTimers();

      const { getRealTimeWorkerData } = await import('../../src/worker-list/services/Services');
      const mockFetch = vi.mocked(getRealTimeWorkerData).mockResolvedValue(mockWorkerData);

      wrapper = mount(WorkerList, {
        props: {
          ...defaultProps,
          autoRefreshInterval: 5000
        }
      });

      await nextTick();

      // Initial call
      expect(mockFetch).toHaveBeenCalledTimes(1);

      // Fast-forward time
      vi.advanceTimersByTime(5000);
      await nextTick();

      // Should have been called again
      expect(mockFetch).toHaveBeenCalledTimes(2);

      vi.useRealTimers();
    });
  });
});
