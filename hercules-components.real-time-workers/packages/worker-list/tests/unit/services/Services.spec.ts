/**
 * Unit tests for Production Worker Services
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  getRealTimeWorkerData,
  getEnhancedRealTimeWorkerData,
  getProductionWorkerMetrics,
  getProductionWorkerAlerts,
  acknowledgeWorkerAlert,
  restartProductionWorker,
  performProductionHealthCheck,
  getWorkerLogs
} from '../../../src/worker-list/services/Services';
import type {
  ProductionWorkerMetrics,
  ProductionWorkerAlert,
  EnhancedRealTimeWorker,
  ProductionHealthCheck
} from '../../../src/worker-list/types';

// Mock the service manager
vi.mock('../../../src/worker-list/services/workerListManager.service', () => ({
  auraAppServiceManager: () => () => ({
    executeRequest: vi.fn()
  })
}));

describe('Production Worker Services', () => {
  const mockPayload = {
    tenantId: '600309',
    coaid: 'COAUS001007',
    cbuid: '1640995200000', // 2022-01-01 timestamp
    atlantaUrl: 'https://test.example.com'
  };

  const mockWorkerData = {
    data: {
      tenantID: 600309,
      coaCode: 'COAUS001007',
      cbu: '2022-01-01',
      phase: 'ACTIVE',
      totalPostings: 1000000,
      realTimeWorkers: [
        {
          code: 'RTWUS0010',
          name: 'Test Worker 1',
          successfullPostingsCount: 95000,
          failedPostingsCount: 100,
          pendingPostingsCount: 50,
          status: 'HEALTHY',
          clusterName: 'test-cluster',
          description: 'Test worker description',
          dlqProcessingStatus: 'INACTIVE'
        }
      ]
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getRealTimeWorkerData', () => {
    it('should fetch basic worker data successfully', async () => {
      const mockExecuteRequest = vi.fn().mockResolvedValue(mockWorkerData);
      vi.doMock('../../../src/worker-list/services/workerListManager.service', () => ({
        auraAppServiceManager: () => () => ({
          executeRequest: mockExecuteRequest
        })
      }));

      const result = await getRealTimeWorkerData(mockPayload);

      expect(mockExecuteRequest).toHaveBeenCalledWith({
        serviceName: 'AURA_APP_SERVICE',
        path: '/pda-manager/tenants/600309/coas/COAUS001007/cbus/2022-01-01/phases/ACTIVE/realTimeWorkers',
        method: 'GET',
        tenantId: '600309'
      });
      expect(result).toEqual(mockWorkerData);
    });

    it('should handle API errors gracefully', async () => {
      const mockExecuteRequest = vi.fn().mockRejectedValue(new Error('API Error'));
      vi.doMock('../../../src/worker-list/services/workerListManager.service', () => ({
        auraAppServiceManager: () => () => ({
          executeRequest: mockExecuteRequest
        })
      }));

      await expect(getRealTimeWorkerData(mockPayload)).rejects.toThrow('API Error');
    });
  });

  describe('getProductionWorkerMetrics', () => {
    const mockMetrics: ProductionWorkerMetrics = {
      cpuUsage: 45.5,
      memoryUsage: 67.2,
      throughputPerSecond: 150,
      errorRate: 0.5,
      lastHeartbeat: '2024-01-01T12:00:00Z',
      uptime: 86400
    };

    it('should fetch worker metrics successfully', async () => {
      const mockExecuteRequest = vi.fn().mockResolvedValue({ data: mockMetrics });
      vi.doMock('../../../src/worker-list/services/workerListManager.service', () => ({
        auraAppServiceManager: () => () => ({
          executeRequest: mockExecuteRequest
        })
      }));

      const result = await getProductionWorkerMetrics({
        ...mockPayload,
        workerCode: 'RTWUS0010'
      });

      expect(result).toEqual(mockMetrics);
    });

    it('should return default metrics on error', async () => {
      const mockExecuteRequest = vi.fn().mockRejectedValue(new Error('Metrics unavailable'));
      vi.doMock('../../../src/worker-list/services/workerListManager.service', () => ({
        auraAppServiceManager: () => () => ({
          executeRequest: mockExecuteRequest
        })
      }));

      const result = await getProductionWorkerMetrics({
        ...mockPayload,
        workerCode: 'RTWUS0010'
      });

      expect(result.cpuUsage).toBe(0);
      expect(result.memoryUsage).toBe(0);
      expect(result.throughputPerSecond).toBe(0);
    });
  });

  describe('getProductionWorkerAlerts', () => {
    const mockAlerts: ProductionWorkerAlert[] = [
      {
        id: 'alert-001',
        severity: 'HIGH',
        message: 'High CPU usage detected',
        timestamp: '2024-01-01T12:00:00Z',
        acknowledged: false,
        category: 'PERFORMANCE'
      },
      {
        id: 'alert-002',
        severity: 'MEDIUM',
        message: 'Memory usage above threshold',
        timestamp: '2024-01-01T11:30:00Z',
        acknowledged: true,
        category: 'RESOURCE'
      }
    ];

    it('should fetch worker alerts successfully', async () => {
      const mockExecuteRequest = vi.fn().mockResolvedValue({ data: mockAlerts });
      vi.doMock('../../../src/worker-list/services/workerListManager.service', () => ({
        auraAppServiceManager: () => () => ({
          executeRequest: mockExecuteRequest
        })
      }));

      const result = await getProductionWorkerAlerts({
        ...mockPayload,
        workerCode: 'RTWUS0010'
      });

      expect(result).toEqual(mockAlerts);
      expect(result).toHaveLength(2);
      expect(result[0].severity).toBe('HIGH');
    });

    it('should return empty array on error', async () => {
      const mockExecuteRequest = vi.fn().mockRejectedValue(new Error('Alerts unavailable'));
      vi.doMock('../../../src/worker-list/services/workerListManager.service', () => ({
        auraAppServiceManager: () => () => ({
          executeRequest: mockExecuteRequest
        })
      }));

      const result = await getProductionWorkerAlerts({
        ...mockPayload,
        workerCode: 'RTWUS0010'
      });

      expect(result).toEqual([]);
    });
  });

  describe('acknowledgeWorkerAlert', () => {
    it('should acknowledge alert successfully', async () => {
      const mockExecuteRequest = vi.fn().mockResolvedValue({ success: true });
      vi.doMock('../../../src/worker-list/services/workerListManager.service', () => ({
        auraAppServiceManager: () => () => ({
          executeRequest: mockExecuteRequest
        })
      }));

      const result = await acknowledgeWorkerAlert({
        ...mockPayload,
        workerCode: 'RTWUS0010',
        alertId: 'alert-001'
      });

      expect(result).toBe(true);
      expect(mockExecuteRequest).toHaveBeenCalledWith({
        serviceName: 'AURA_APP_SERVICE',
        path: '/pda-manager/tenants/600309/coas/COAUS001007/cbus/2022-01-01/workers/RTWUS0010/alerts/alert-001/acknowledge',
        method: 'POST',
        tenantId: '600309'
      });
    });

    it('should return false on error', async () => {
      const mockExecuteRequest = vi.fn().mockRejectedValue(new Error('Acknowledge failed'));
      vi.doMock('../../../src/worker-list/services/workerListManager.service', () => ({
        auraAppServiceManager: () => () => ({
          executeRequest: mockExecuteRequest
        })
      }));

      const result = await acknowledgeWorkerAlert({
        ...mockPayload,
        workerCode: 'RTWUS0010',
        alertId: 'alert-001'
      });

      expect(result).toBe(false);
    });
  });

  describe('restartProductionWorker', () => {
    it('should restart worker successfully', async () => {
      const mockExecuteRequest = vi.fn().mockResolvedValue({ success: true });
      vi.doMock('../../../src/worker-list/services/workerListManager.service', () => ({
        auraAppServiceManager: () => () => ({
          executeRequest: mockExecuteRequest
        })
      }));

      const result = await restartProductionWorker({
        ...mockPayload,
        workerCode: 'RTWUS0010',
        reason: 'Manual restart for testing'
      });

      expect(result).toBe(true);
      expect(mockExecuteRequest).toHaveBeenCalledWith({
        serviceName: 'AURA_APP_SERVICE',
        path: '/pda-manager/tenants/600309/coas/COAUS001007/cbus/2022-01-01/workers/RTWUS0010/restart',
        method: 'POST',
        tenantId: '600309',
        data: { reason: 'Manual restart for testing' }
      });
    });

    it('should use default reason when none provided', async () => {
      const mockExecuteRequest = vi.fn().mockResolvedValue({ success: true });
      vi.doMock('../../../src/worker-list/services/workerListManager.service', () => ({
        auraAppServiceManager: () => () => ({
          executeRequest: mockExecuteRequest
        })
      }));

      await restartProductionWorker({
        ...mockPayload,
        workerCode: 'RTWUS0010'
      });

      expect(mockExecuteRequest).toHaveBeenCalledWith(
        expect.objectContaining({
          data: { reason: 'Manual restart from production dashboard' }
        })
      );
    });
  });

  describe('performProductionHealthCheck', () => {
    it('should perform health check and return status', async () => {
      const mockEnhancedData = {
        ...mockWorkerData,
        data: {
          ...mockWorkerData.data,
          realTimeWorkers: [
            {
              ...mockWorkerData.data.realTimeWorkers[0],
              status: 'HEALTHY',
              alerts: []
            },
            {
              ...mockWorkerData.data.realTimeWorkers[0],
              code: 'RTWUS0011',
              name: 'Test Worker 2',
              status: 'UNHEALTHY',
              alerts: [
                {
                  id: 'alert-critical',
                  severity: 'CRITICAL',
                  message: 'Critical error',
                  timestamp: '2024-01-01T12:00:00Z',
                  acknowledged: false
                }
              ]
            }
          ]
        }
      };

      // Mock the enhanced data fetching
      vi.doMock('./Services', async () => {
        const actual = await vi.importActual('./Services');
        return {
          ...actual,
          getEnhancedRealTimeWorkerData: vi.fn().mockResolvedValue(mockEnhancedData)
        };
      });

      const result = await performProductionHealthCheck(mockPayload);

      expect(result.overallHealth).toBe('CRITICAL');
      expect(result.totalWorkers).toBe(2);
      expect(result.healthyWorkers).toBe(1);
      expect(result.unhealthyWorkers).toBe(1);
      expect(result.criticalAlerts).toBe(1);
      expect(result.recommendations).toContain('1 critical alerts require immediate attention');
    });
  });
});
