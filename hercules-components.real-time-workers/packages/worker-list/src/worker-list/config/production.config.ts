/**
 * Production Configuration for Real-Time Workers
 * Enhanced configuration for production monitoring and management
 */

import type { ProductionWorkerConfig } from '../types';

/**
 * Default production worker configuration
 */
export const DEFAULT_PRODUCTION_CONFIG: ProductionWorkerConfig = {
  maxRetries: 3,
  timeoutMs: 30000, // 30 seconds
  batchSize: 100,
  enableDetailedLogging: true,
  alertThresholds: {
    errorRatePercent: 5, // Alert if error rate > 5%
    memoryUsagePercent: 85, // Alert if memory usage > 85%
    cpuUsagePercent: 80, // Alert if CPU usage > 80%
    responseTimeMs: 5000, // Alert if response time > 5 seconds
  },
  autoScaling: {
    enabled: true,
    minInstances: 2,
    maxInstances: 10,
    targetCpuPercent: 70,
  },
};

/**
 * Production environment settings
 */
export const PRODUCTION_SETTINGS = {
  // Refresh intervals (in milliseconds)
  DEFAULT_REFRESH_INTERVAL: 30000, // 30 seconds
  FAST_REFRESH_INTERVAL: 10000, // 10 seconds for critical situations
  SLOW_REFRESH_INTERVAL: 60000, // 1 minute for stable systems
  
  // Monitoring thresholds
  HEALTH_CHECK_INTERVAL: 60000, // 1 minute
  ALERT_CHECK_INTERVAL: 15000, // 15 seconds
  METRICS_RETENTION_HOURS: 24, // Keep metrics for 24 hours
  
  // Performance settings
  MAX_CONCURRENT_REQUESTS: 10,
  REQUEST_TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
  
  // UI settings
  MAX_ALERTS_DISPLAY: 5,
  MAX_LOG_ENTRIES: 100,
  CARD_ANIMATION_DURATION: 300, // milliseconds
  
  // Feature flags
  ENABLE_REAL_TIME_UPDATES: true,
  ENABLE_ADVANCED_METRICS: true,
  ENABLE_PREDICTIVE_ALERTS: false, // Future feature
  ENABLE_AUTO_REMEDIATION: false, // Future feature
} as const;

/**
 * Alert severity configuration
 */
export const ALERT_SEVERITY_CONFIG = {
  CRITICAL: {
    color: '#ef4444',
    backgroundColor: '#fef2f2',
    borderColor: '#ef4444',
    priority: 1,
    autoAcknowledge: false,
    escalationTimeMinutes: 5,
  },
  HIGH: {
    color: '#f59e0b',
    backgroundColor: '#fffbeb',
    borderColor: '#f59e0b',
    priority: 2,
    autoAcknowledge: false,
    escalationTimeMinutes: 15,
  },
  MEDIUM: {
    color: '#3b82f6',
    backgroundColor: '#f0f9ff',
    borderColor: '#3b82f6',
    priority: 3,
    autoAcknowledge: true,
    escalationTimeMinutes: 60,
  },
  LOW: {
    color: '#6b7280',
    backgroundColor: '#f9fafb',
    borderColor: '#6b7280',
    priority: 4,
    autoAcknowledge: true,
    escalationTimeMinutes: 240,
  },
} as const;

/**
 * Worker status configuration
 */
export const WORKER_STATUS_CONFIG = {
  HEALTHY: {
    color: '#10b981',
    backgroundColor: '#f0fdf4',
    icon: 'checkmark-circle',
    description: 'Worker is operating normally',
  },
  SLOW: {
    color: '#f59e0b',
    backgroundColor: '#fffbeb',
    icon: 'warning',
    description: 'Worker is responding slowly',
  },
  UNHEALTHY: {
    color: '#ef4444',
    backgroundColor: '#fef2f2',
    icon: 'alert-circle',
    description: 'Worker has encountered errors',
  },
  CRITICAL: {
    color: '#dc2626',
    backgroundColor: '#fef2f2',
    icon: 'close-circle',
    description: 'Worker is in critical state',
  },
} as const;

/**
 * Metrics configuration
 */
export const METRICS_CONFIG = {
  // CPU thresholds
  CPU_WARNING_THRESHOLD: 70,
  CPU_CRITICAL_THRESHOLD: 90,
  
  // Memory thresholds
  MEMORY_WARNING_THRESHOLD: 80,
  MEMORY_CRITICAL_THRESHOLD: 95,
  
  // Throughput thresholds (per second)
  THROUGHPUT_LOW_THRESHOLD: 10,
  THROUGHPUT_NORMAL_THRESHOLD: 50,
  
  // Error rate thresholds (percentage)
  ERROR_RATE_WARNING_THRESHOLD: 2,
  ERROR_RATE_CRITICAL_THRESHOLD: 5,
  
  // Response time thresholds (milliseconds)
  RESPONSE_TIME_WARNING_THRESHOLD: 2000,
  RESPONSE_TIME_CRITICAL_THRESHOLD: 5000,
} as const;

/**
 * Production deployment configuration
 */
export const DEPLOYMENT_CONFIG = {
  // Environment detection
  PRODUCTION_DOMAINS: [
    'eodcenter.tachyonsuite.hbctxdom.com',
    'eodcenter.production.zetaapps.in',
  ],
  
  // Feature toggles based on environment
  DEVELOPMENT_FEATURES: {
    enableMockData: true,
    enableDebugMode: true,
    enableTestActions: true,
  },
  
  STAGING_FEATURES: {
    enableMockData: false,
    enableDebugMode: true,
    enableTestActions: true,
  },
  
  PRODUCTION_FEATURES: {
    enableMockData: false,
    enableDebugMode: false,
    enableTestActions: false,
  },
} as const;

/**
 * API endpoint configuration
 */
export const API_CONFIG = {
  // Base paths
  WORKER_BASE_PATH: '/pda-manager/tenants/{tenantId}/coas/{coaid}/cbus/{cbuid}',
  METRICS_PATH: '/workers/{workerCode}/metrics',
  ALERTS_PATH: '/workers/{workerCode}/alerts',
  LOGS_PATH: '/workers/{workerCode}/logs',
  ACTIONS_PATH: '/workers/{workerCode}/actions',
  
  // Request configuration
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  
  // Retry configuration
  RETRY_STATUS_CODES: [408, 429, 500, 502, 503, 504],
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY_MS: 1000,
} as const;

/**
 * Utility function to get environment-specific configuration
 */
export function getEnvironmentConfig() {
  const hostname = typeof window !== 'undefined' ? window.location.hostname : '';
  const isProduction = DEPLOYMENT_CONFIG.PRODUCTION_DOMAINS.some(domain => 
    hostname.includes(domain)
  );
  
  if (isProduction) {
    return {
      environment: 'PRODUCTION' as const,
      features: DEPLOYMENT_CONFIG.PRODUCTION_FEATURES,
      refreshInterval: PRODUCTION_SETTINGS.DEFAULT_REFRESH_INTERVAL,
    };
  }
  
  const isStaging = hostname.includes('staging') || hostname.includes('uat');
  if (isStaging) {
    return {
      environment: 'STAGING' as const,
      features: DEPLOYMENT_CONFIG.STAGING_FEATURES,
      refreshInterval: PRODUCTION_SETTINGS.FAST_REFRESH_INTERVAL,
    };
  }
  
  return {
    environment: 'DEVELOPMENT' as const,
    features: DEPLOYMENT_CONFIG.DEVELOPMENT_FEATURES,
    refreshInterval: PRODUCTION_SETTINGS.FAST_REFRESH_INTERVAL,
  };
}

/**
 * Utility function to get alert configuration by severity
 */
export function getAlertConfig(severity: keyof typeof ALERT_SEVERITY_CONFIG) {
  return ALERT_SEVERITY_CONFIG[severity];
}

/**
 * Utility function to get worker status configuration
 */
export function getWorkerStatusConfig(status: keyof typeof WORKER_STATUS_CONFIG) {
  return WORKER_STATUS_CONFIG[status];
}

/**
 * Utility function to determine if metrics are in warning/critical state
 */
export function getMetricStatus(metricType: string, value: number) {
  switch (metricType) {
    case 'cpu':
      if (value >= METRICS_CONFIG.CPU_CRITICAL_THRESHOLD) return 'critical';
      if (value >= METRICS_CONFIG.CPU_WARNING_THRESHOLD) return 'warning';
      return 'normal';
    
    case 'memory':
      if (value >= METRICS_CONFIG.MEMORY_CRITICAL_THRESHOLD) return 'critical';
      if (value >= METRICS_CONFIG.MEMORY_WARNING_THRESHOLD) return 'warning';
      return 'normal';
    
    case 'errorRate':
      if (value >= METRICS_CONFIG.ERROR_RATE_CRITICAL_THRESHOLD) return 'critical';
      if (value >= METRICS_CONFIG.ERROR_RATE_WARNING_THRESHOLD) return 'warning';
      return 'normal';
    
    case 'responseTime':
      if (value >= METRICS_CONFIG.RESPONSE_TIME_CRITICAL_THRESHOLD) return 'critical';
      if (value >= METRICS_CONFIG.RESPONSE_TIME_WARNING_THRESHOLD) return 'warning';
      return 'normal';
    
    default:
      return 'normal';
  }
}
