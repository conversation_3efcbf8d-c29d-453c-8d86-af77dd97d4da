import { createApp } from 'vue';
import GDS from '@zeta-gds/components';
import WorkerList from './WorkerList.vue';

createApp(WorkerList, {
    tenantId: '600309',
    coaid: 'COAUS001007',
    cbuid: '1640995200000', // 2022-01-01 timestamp
    atalantaServiceUrl: 'https://test.example.com',
    // Reprocess configuration
    auraAppBaseUrl: 'https://aura.example.com'
})
    .use(GDS)
    .mount('#demo');
