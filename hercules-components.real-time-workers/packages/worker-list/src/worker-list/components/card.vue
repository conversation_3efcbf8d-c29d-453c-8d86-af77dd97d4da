<template>
  <z-card :class="{ 'highlighted-card': highlighted }" class="worker-card" role="region" aria-label="Worker card">
    <div class="card-header">
      <div class="name-section">
        <z-text strong tag="h3" class="worker-name">
          {{ name }}
        </z-text>
   <z-icon v-if="status === 'UNHEALTHY'" color="#F04F6D" :size="20">
        <AlertCircle  />
   </z-icon>
   

      </div>
      <z-tag :bordered="false"
       v-if="status === 'SLOW'"  class="status-tag">
      <template  #icon>
        <z-icon color="#F04F6D"  :size="12">
          <Ellipse />
        </z-icon>
      </template>
        Slow
      </z-tag>
    </div>
    <div class="card-content">
      <div class="stats-left">
    <div class="posting-stats-line">
      <z-text class="success-count" strong>{{ successCount }}</z-text>
      <z-text class="posting-total" strong>/ {{ formatCount(totalPostings) }} postings</z-text>
    </div>
        <z-text class="success-label">Successfully processed</z-text>
      </div>
      <z-divider vertical class="divider" />
      <div class="stats-right">
        <div class="failed-line">
      <z-text class="failed-count" strong>{{ formatCount(failedCount) }}</z-text>
          <z-text class="failed-label">Failed</z-text>
        </div>
        <div class="queue-line">
      <z-text class="queue-count" strong>{{ formatCount(queueCount) }} </z-text>
      <z-text class="queue-label">In queue</z-text>
        </div>
      </div>
    </div>

    <!-- Production Features Section -->
    <div v-if="enableProductionFeatures" class="production-section">
      <!-- Metrics Display -->
      <div v-if="metrics" class="metrics-section">
        <div class="metrics-row">
          <div class="metric-item">
            <z-text class="metric-label">CPU</z-text>
            <z-text class="metric-value" :class="{ 'metric-warning': metrics.cpuUsage > 80 }">
              {{ metrics.cpuUsage.toFixed(1) }}%
            </z-text>
          </div>
          <div class="metric-item">
            <z-text class="metric-label">Memory</z-text>
            <z-text class="metric-value" :class="{ 'metric-warning': metrics.memoryUsage > 80 }">
              {{ metrics.memoryUsage.toFixed(1) }}%
            </z-text>
          </div>
          <div class="metric-item">
            <z-text class="metric-label">Throughput</z-text>
            <z-text class="metric-value">{{ metrics.throughputPerSecond }}/s</z-text>
          </div>
        </div>
      </div>

      <!-- Alerts Display -->
      <div v-if="alerts && alerts.length > 0" class="alerts-section">
        <div v-for="alert in alerts.slice(0, 2)" :key="alert.id" class="alert-item" :class="`alert-${alert.severity.toLowerCase()}`">
          <div class="alert-content">
            <z-text class="alert-message">{{ alert.message }}</z-text>
            <z-text class="alert-time">{{ new Date(alert.timestamp).toLocaleTimeString() }}</z-text>
          </div>
          <z-button
            v-if="!alert.acknowledged && onAcknowledgeAlert"
            size="small"
            variant="text"
            @click="onAcknowledgeAlert(alert.id)"
            class="ack-button"
          >
            Ack
          </z-button>
        </div>
        <z-text v-if="alerts.length > 2" class="more-alerts">
          +{{ alerts.length - 2 }} more alerts
        </z-text>
      </div>

      <!-- Production Actions -->
      <div class="production-actions">
        <z-button
          v-if="onRestart && (status === 'UNHEALTHY' || status === 'SLOW')"
          size="small"
          variant="outlined"
          @click="onRestart()"
          class="restart-button"
        >
          Restart Worker
        </z-button>
      </div>
    </div>
  </z-card>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { ZCard, ZText, ZDivider, ZTag ,ZIcon } from '@zeta-gds/components';
import { Ellipse, AlertCircle } from '@vicons/ionicons5'

export default defineComponent({
  name: 'WorkerCard',
  components: {
    ZCard,
    ZText,
    ZDivider,
    ZTag,
    ZIcon,
    Ellipse,
    AlertCircle,
  },
  props: {
    name: {
      type: String,
      required: true,
    },
    successCount: {
      type: Number,
      required: true,
    },
    totalPostings: {
      type: Number,
      required: true,
    },
    failedCount: {
      type: Number,
      required: true,
    },
    queueCount: {
      type: Number,
      required: true,
    },
    highlighted: {
      type: Boolean,
      default: false,
    },
    status: {
      type: String,
      required: true,
    },
    // Production-specific props
    metrics: {
      type: Object,
      default: null,
    },
    alerts: {
      type: Array,
      default: () => [],
    },
    enableProductionFeatures: {
      type: Boolean,
      default: false,
    },
    onRestart: {
      type: Function,
      default: null,
    },
    onAcknowledgeAlert: {
      type: Function,
      default: null,
    },
  },
  methods: {
    formatCount(value: number): string {
      if (value >= 1000) {
    // divide by 1,000 and keep one decimal place
    const formattedValue = (value / 1000).toFixed(1);
    // if it’s something like “1.0”, drop the “.0”
    const compact = formattedValue.replace(/\.0$/, '');
    return `${compact}k`;
  }
      return value.toString();
    }
  }
});
</script>

<style scoped lang="scss">
.worker-card {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background-color: white;
  border-radius: 4px;
  box-shadow: none;
  border: 1px solid #e0e0e0;
}

.highlighted-card {
  background-color: #f8d7da; /* light pink */
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
}

.name-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.alert-icon {
  color: #d32f2f;
  width: 1.25rem;
  height: 1.25rem;
}

// .status-tag {
//   font-size: 0.75rem;
//   padding: 0.25rem 0.5rem;
// }

.worker-name {
  color: #0a1f44; /* dark blue */
  font-weight: 600;
  font-size: 1rem;
  margin: 0;
  border-bottom: 1px dashed rgb(224, 224, 224); /* dashed underline */
  // padding-bottom: 0.25rem;
}

.card-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stats-left,
.stats-right {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.posting-stats-line {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.failed-line,
.queue-line {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.success-count {
  color: #2e7d32; /* green */
  font-weight: 700;
  font-size: 1.25rem;
  // white-space: nowrap;
}

.posting-total {
  color:  #434B79;
  font-weight: 600;
  font-size: 0.875rem;
}

.success-label {
  color: #4a4a4a;
  font-size: 0.75rem;
}

.failed-count {
  color: #b71c1c; /* red */
  font-weight: 700;
  font-size: 1.25rem;
  // white-space: nowrap;
}

.failed-label {
  color: #a0a0a0;
  font-size: 0.75rem;
}

.queue-count {
  color: #434B79; /* dark blue */
  font-weight: 700;
  font-size: 1.25rem;
  // white-space: nowrap;
}

.queue-label {
  color: #a0a0a0; /* same as failed text color */
  font-size: 0.75rem;
}

.divider {
  height: 3rem;
  border-left: 1px solid #d0d0d0;
}

/* Production Features Styles */
.production-section {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e5e7eb;
}

.metrics-section {
  margin-bottom: 0.5rem;
}

.metrics-row {
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.metric-label {
  font-size: 0.625rem;
  color: #6b7280;
  text-transform: uppercase;
  font-weight: 500;
}

.metric-value {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
}

.metric-warning {
  color: #f59e0b !important;
}

.alerts-section {
  margin-bottom: 0.5rem;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0.5rem;
  margin-bottom: 0.25rem;
  border-radius: 4px;
  font-size: 0.75rem;
}

.alert-critical {
  background-color: #fef2f2;
  border-left: 3px solid #ef4444;
}

.alert-high {
  background-color: #fffbeb;
  border-left: 3px solid #f59e0b;
}

.alert-medium {
  background-color: #f0f9ff;
  border-left: 3px solid #3b82f6;
}

.alert-low {
  background-color: #f9fafb;
  border-left: 3px solid #6b7280;
}

.alert-content {
  flex: 1;
}

.alert-message {
  font-size: 0.75rem;
  color: #374151;
  display: block;
}

.alert-time {
  font-size: 0.625rem;
  color: #6b7280;
}

.ack-button {
  font-size: 0.625rem;
  padding: 0.125rem 0.375rem;
}

.more-alerts {
  font-size: 0.625rem;
  color: #6b7280;
  font-style: italic;
  text-align: center;
  margin-top: 0.25rem;
}

.production-actions {
  display: flex;
  justify-content: center;
}

.restart-button {
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
}
</style>
