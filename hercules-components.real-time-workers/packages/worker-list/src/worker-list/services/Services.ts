import {auraAppServiceManager} from './workerListManager.service';
import dayjs from 'dayjs';
import utc   from 'dayjs/plugin/utc';
import type {
    ProductionWorkerMetrics,
    ProductionWorkerAlert,
    EnhancedRealTimeWorker,
    ProductionHealthCheck,
    WorkerActionResult,
    WorkerLogEntry,
    BaseServicePayload,
    EnhancedWorkerDataPayload,
    WorkerMetricsPayload,
    WorkerActionPayload,
    AlertActionPayload,
    WorkerLogsPayload
} from '../types';

dayjs.extend(utc);
const auraAppService = auraAppServiceManager();

export const getRealTimeWorkerData = async (
    payload: BaseServicePayload,
) => {

    const {tenantId, coaid, cbuid, atlantaUrl} = payload;

    // Format cbuid to YYYY-MM-DD format
    const formattedCbuid = dayjs(Number(cbuid)).format("YYYY-MM-DD");

    const phase = 'ACTIVE'; // phase will be always ACTIVE for real time workers
    const requestBody = {
        serviceName: 'AURA_APP_SERVICE',
        path: `/pda-manager/tenants/${tenantId}/coas/${coaid}/cbus/${formattedCbuid}/phases/${phase}/realTimeWorkers`,
        method: 'GET',
        tenantId: tenantId,
    };
    const response = await auraAppService(atlantaUrl).executeRequest(requestBody);

    return response;
};

// Production-specific service methods
export const getProductionWorkerMetrics = async (
    payload: WorkerMetricsPayload
): Promise<ProductionWorkerMetrics> => {
    const { tenantId, coaid, cbuid, atlantaUrl, workerCode } = payload;
    const formattedCbuid = dayjs(Number(cbuid)).format("YYYY-MM-DD");

    const requestBody = {
        serviceName: 'AURA_APP_SERVICE',
        path: `/pda-manager/tenants/${tenantId}/coas/${coaid}/cbus/${formattedCbuid}/workers/${workerCode}/metrics`,
        method: 'GET',
        tenantId: tenantId,
    };

    try {
        const response = await auraAppService(atlantaUrl).executeRequest(requestBody);
        return response.data || {
            cpuUsage: 0,
            memoryUsage: 0,
            throughputPerSecond: 0,
            errorRate: 0,
            lastHeartbeat: new Date().toISOString(),
            uptime: 0
        };
    } catch (error) {
        console.warn(`Failed to fetch metrics for worker ${workerCode}:`, error);
        return {
            cpuUsage: 0,
            memoryUsage: 0,
            throughputPerSecond: 0,
            errorRate: 0,
            lastHeartbeat: new Date().toISOString(),
            uptime: 0
        };
    }
};

export const getProductionWorkerAlerts = async (
    payload: WorkerMetricsPayload
): Promise<ProductionWorkerAlert[]> => {
    const { tenantId, coaid, cbuid, atlantaUrl, workerCode } = payload;
    const formattedCbuid = dayjs(Number(cbuid)).format("YYYY-MM-DD");

    const requestBody = {
        serviceName: 'AURA_APP_SERVICE',
        path: `/pda-manager/tenants/${tenantId}/coas/${coaid}/cbus/${formattedCbuid}/workers/${workerCode}/alerts`,
        method: 'GET',
        tenantId: tenantId,
    };

    try {
        const response = await auraAppService(atlantaUrl).executeRequest(requestBody);
        return response.data || [];
    } catch (error) {
        console.warn(`Failed to fetch alerts for worker ${workerCode}:`, error);
        return [];
    }
};

export const acknowledgeWorkerAlert = async (
    payload: AlertActionPayload
): Promise<boolean> => {
    const { tenantId, coaid, cbuid, atlantaUrl, workerCode, alertId } = payload;
    const formattedCbuid = dayjs(Number(cbuid)).format("YYYY-MM-DD");

    const requestBody = {
        serviceName: 'AURA_APP_SERVICE',
        path: `/pda-manager/tenants/${tenantId}/coas/${coaid}/cbus/${formattedCbuid}/workers/${workerCode}/alerts/${alertId}/acknowledge`,
        method: 'POST',
        tenantId: tenantId,
    };

    try {
        await auraAppService(atlantaUrl).executeRequest(requestBody);
        return true;
    } catch (error) {
        console.error(`Failed to acknowledge alert ${alertId} for worker ${workerCode}:`, error);
        return false;
    }
};

export const restartProductionWorker = async (
    payload: WorkerActionPayload
): Promise<boolean> => {
    const { tenantId, coaid, cbuid, atlantaUrl, workerCode, reason } = payload;
    const formattedCbuid = dayjs(Number(cbuid)).format("YYYY-MM-DD");

    const requestBody = {
        serviceName: 'AURA_APP_SERVICE',
        path: `/pda-manager/tenants/${tenantId}/coas/${coaid}/cbus/${formattedCbuid}/workers/${workerCode}/restart`,
        method: 'POST',
        tenantId: tenantId,
        data: { reason: reason || 'Manual restart from production dashboard' }
    };

    try {
        await auraAppService(atlantaUrl).executeRequest(requestBody);
        return true;
    } catch (error) {
        console.error(`Failed to restart worker ${workerCode}:`, error);
        return false;
    }
};

export const getWorkerLogs = async (
    payload: WorkerLogsPayload
): Promise<WorkerLogEntry[]> => {
    const { tenantId, coaid, cbuid, atlantaUrl, workerCode, limit = 100 } = payload;
    const formattedCbuid = dayjs(Number(cbuid)).format("YYYY-MM-DD");

    const requestBody = {
        serviceName: 'AURA_APP_SERVICE',
        path: `/pda-manager/tenants/${tenantId}/coas/${coaid}/cbus/${formattedCbuid}/workers/${workerCode}/logs?limit=${limit}`,
        method: 'GET',
        tenantId: tenantId,
    };

    try {
        const response = await auraAppService(atlantaUrl).executeRequest(requestBody);
        return response.data?.logs || [];
    } catch (error) {
        console.warn(`Failed to fetch logs for worker ${workerCode}:`, error);
        return [];
    }
};

// Enhanced production worker data fetching
export const getEnhancedRealTimeWorkerData = async (
    payload: EnhancedWorkerDataPayload
) => {
    const { tenantId, coaid, cbuid, atlantaUrl, includeMetrics = true, includeAlerts = true } = payload;

    try {
        // Get basic worker data
        const basicResponse = await getRealTimeWorkerData(payload);

        if (!basicResponse.data?.realTimeWorkers) {
            return basicResponse;
        }

        // Enhance each worker with production data
        const enhancedWorkers = await Promise.all(
            basicResponse.data.realTimeWorkers.map(async (worker: any): Promise<EnhancedRealTimeWorker> => {
                const enhancedWorker: EnhancedRealTimeWorker = {
                    ...worker,
                    environment: process.env.NODE_ENV === 'production' ? 'PRODUCTION' : 'DEVELOPMENT',
                    version: '1.0.0', // This should come from deployment info
                    lastDeployment: new Date().toISOString(), // This should come from deployment info
                };

                // Fetch metrics if requested
                if (includeMetrics) {
                    try {
                        enhancedWorker.metrics = await getProductionWorkerMetrics({
                            tenantId, coaid, cbuid, atlantaUrl, workerCode: worker.code
                        });
                    } catch (error) {
                        console.warn(`Failed to fetch metrics for worker ${worker.code}`);
                    }
                }

                // Fetch alerts if requested
                if (includeAlerts) {
                    try {
                        enhancedWorker.alerts = await getProductionWorkerAlerts({
                            tenantId, coaid, cbuid, atlantaUrl, workerCode: worker.code
                        });
                    } catch (error) {
                        console.warn(`Failed to fetch alerts for worker ${worker.code}`);
                    }
                }

                return enhancedWorker;
            })
        );

        return {
            ...basicResponse,
            data: {
                ...basicResponse.data,
                realTimeWorkers: enhancedWorkers
            }
        };
    } catch (error) {
        console.error('Failed to fetch enhanced worker data:', error);
        // Fallback to basic data
        return getRealTimeWorkerData(payload);
    }
};

// Production health check
export const performProductionHealthCheck = async (
    payload: BaseServicePayload
): Promise<ProductionHealthCheck> => {
    try {
        const response = await getEnhancedRealTimeWorkerData(payload);
        const workers = response.data?.realTimeWorkers || [];

        const healthyWorkers = workers.filter(w => w.status === 'HEALTHY').length;
        const unhealthyWorkers = workers.filter(w => w.status === 'UNHEALTHY').length;
        const criticalAlerts = workers.reduce((count, w) =>
            count + (w.alerts?.filter(a => a.severity === 'CRITICAL').length || 0), 0
        );

        let overallHealth: 'HEALTHY' | 'DEGRADED' | 'CRITICAL' = 'HEALTHY';
        const recommendations: string[] = [];

        if (criticalAlerts > 0) {
            overallHealth = 'CRITICAL';
            recommendations.push(`${criticalAlerts} critical alerts require immediate attention`);
        } else if (unhealthyWorkers > workers.length * 0.3) {
            overallHealth = 'CRITICAL';
            recommendations.push('More than 30% of workers are unhealthy');
        } else if (unhealthyWorkers > 0) {
            overallHealth = 'DEGRADED';
            recommendations.push(`${unhealthyWorkers} workers need attention`);
        }

        // Add performance recommendations
        const slowWorkers = workers.filter(w => w.status === 'SLOW').length;
        if (slowWorkers > 0) {
            recommendations.push(`${slowWorkers} workers are running slowly - consider scaling`);
        }

        return {
            overallHealth,
            totalWorkers: workers.length,
            healthyWorkers,
            unhealthyWorkers,
            criticalAlerts,
            recommendations
        };
    } catch (error) {
        console.error('Failed to perform health check:', error);
        return {
            overallHealth: 'CRITICAL',
            totalWorkers: 0,
            healthyWorkers: 0,
            unhealthyWorkers: 0,
            criticalAlerts: 0,
            recommendations: ['Health check failed - system may be unavailable']
        };
    }
};