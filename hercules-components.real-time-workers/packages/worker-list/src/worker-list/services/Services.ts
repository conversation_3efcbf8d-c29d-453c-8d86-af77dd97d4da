import {auraAppServiceManager} from './workerListManager.service';
import { RheaService } from '@zeta/service-clients';
import { getAuthToken } from '@hercules/context';
import { RheaTypes } from '@zeta/service-clients/lib/rhea/types';
import dayjs from 'dayjs';
import utc   from 'dayjs/plugin/utc';

dayjs.extend(utc);
const auraAppService = auraAppServiceManager();

// Rhea service manager for workflow processes (matching RunEodProcessModal pattern)
const rheaServiceManager = () => {
    let instance: RheaService;
    const resolveAuthToken = () => {
        return { Authorization: 'Bearer ' + getAuthToken() };
    };
    return function (baseURL: string) {
        instance = instance || new RheaService({ baseURL, resolveAuthToken });
        return instance;
    };
};

const rheaService = rheaServiceManager();

// Request type constants (matching RunEodProcessModal)
export enum REQUEST_TYPE {
    SR = 'SR',
    BR = 'BR',
    DEFAULT = 'serviceRequest',
}


export const getRealTimeWorkerData = async (
    payload: any,
) => {

    const {tenantId, coaid, cbuid, atlantaUrl} = payload;
    
    // Format cbuid to YYYY-MM-DD format
    const formattedCbuid = dayjs(Number(cbuid)).format("YYYY-MM-DD");
   
    const phase = 'ACTIVE'; // phase will be always ACTIVE for real time workers
    const requestBody = {
        serviceName: 'AURA_APP_SERVICE',
        path: `/pda-manager/tenants/${tenantId}/coas/${coaid}/cbus/${formattedCbuid}/phases/${phase}/realTimeWorkers`,
        method: 'GET',
        tenantId: tenantId,
    };
    const response = await auraAppService(atlantaUrl).executeRequest(requestBody);

    return response;
};

// startWorkflowProcessV2 function (matching RunEodProcessModal implementation)
export const startWorkflowProcessV2 = async (
    baseUrl: string,
    params: RheaTypes.SubmitRequestParams,
    payload: RheaTypes.SubmitRequestPayload,
) => {
    const { data: response } = await rheaService(baseUrl).submitBusinessRequest(params, payload);
    return response;
};

// Reprocess worker using startWorkflowProcessV2 (matching RunEodProcessModal pattern)
export const reprocessWorker = async (payload: {
    rheaRequestUrl: string;
    auraAppBaseUrl: string;
    coaCode: string;
    cbuId: string;
    phaseId: string;
    pdaCode: string;
    rdCode?: string;
    tenantId: string;
    workerCode: string;
    herculesServiceUrl: string;
    appUrl: string;
}) => {
    const {
        appUrl,
        herculesServiceUrl,
        rheaRequestUrl,
        auraAppBaseUrl,
        coaCode,
        cbuId,
        phaseId,
        pdaCode,
        rdCode = "RQDINZZ0326",
        tenantId,
        workerCode,

    } = payload;

    try {
        // Prepare request parameters (matching RunEodProcessModal pattern)
        const request: RheaTypes.SubmitRequestParams = {
            tenantId: tenantId,
            requestType: REQUEST_TYPE.BR // Business Request type
        };

        // Create workflow payload following RunEodProcessModal createWorkflowPayload pattern
        const processKey = `Reprocess_Worker_${workerCode}_${dayjs().format('YYYYMMDD')}_${dayjs().format('YYYYMMDD HH:mm:ss')}`;
        const requestedTime = dayjs().toISOString();

        const workflowPayload: RheaTypes.SubmitRequestPayload = {
            initiatorForm: {
                appUrl: {
                    value: appUrl,
                    type: 'String'
                },
                herculesServiceUrl: {
                    value: herculesServiceUrl,
                    type: 'String'
                },
                auraAppBaseUrl: {
                    value: auraAppBaseUrl,
                    type: 'String'
                },
                coaCode: {
                    value: coaCode,
                    type: 'String'
                },
                cbuId: {
                    value: cbuId,
                    type: 'String'
                },
                phaseId: {
                    value: phaseId,
                    type: 'String'
                },
                pdaCode: {
                    value: pdaCode,
                    type: 'String'
                },
                tenantId: {
                    value: tenantId,
                    type: 'String'
                },
                workerCode: {
                    value: workerCode,
                    type: 'String'
                },
                processKey: {
                    value: processKey,
                    type: 'String'
                },
                actionType: {
                    value: 'Reprocess Worker',
                    type: 'String'
                },
                requestedBy: {
                    value: 'Real-time Worker Dashboard',
                    type: 'String'
                },
                requestedTime: {
                    value: requestedTime,
                    type: 'String'
                },
                isRetry: {
                    value: true,
                    type: 'Boolean'
                },
                taskSummary: {
                    value: `Reprocessing worker ${workerCode} for COA ${coaCode}`,
                    type: 'String'
                }
            },
            rdCode: rdCode
        };

        // Call startWorkflowProcessV2 exactly like RunEodProcessModal
        const { requestId = '' } = await startWorkflowProcessV2(
            rheaRequestUrl,
            request,
            workflowPayload
        );

        return {
            success: true,
            requestId: requestId,
            message: `Worker ${workerCode} reprocess request submitted successfully`
        };
    } catch (error) {
        console.error(`Failed to reprocess worker ${workerCode}:`, error);
        return {
            success: false,
            error: (error as Error).message,
            message: `Failed to submit reprocess request for worker ${workerCode}`
        };
    }
};