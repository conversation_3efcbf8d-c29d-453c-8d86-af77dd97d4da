import {auraAppServiceManager} from './workerListManager.service';
import { RequestExecutorService } from '@zeta/service-clients';
import { getAuthToken } from '@hercules/context';
import dayjs from 'dayjs';
import utc   from 'dayjs/plugin/utc';

dayjs.extend(utc);
const auraAppService = auraAppServiceManager();

// Rhea service manager for workflow processes
const rheaServiceManager = () => {
    let instance: any;
    const resolveAuthToken = () => {
        return { Authorization: 'Bearer ' + getAuthToken() };
    };
    return function (baseURL: string) {
        instance =
            instance ||
            new RequestExecutorService({
                baseURL: baseURL,
                resolveAuthToken,
            });
        return instance;
    };
};

const rheaService = rheaServiceManager();


export const getRealTimeWorkerData = async (
    payload: any,
) => {

    const {tenantId, coaid, cbuid, atlantaUrl} = payload;
    
    // Format cbuid to YYYY-MM-DD format
    const formattedCbuid = dayjs(Number(cbuid)).format("YYYY-MM-DD");
   
    const phase = 'ACTIVE'; // phase will be always ACTIVE for real time workers
    const requestBody = {
        serviceName: 'AURA_APP_SERVICE',
        path: `/pda-manager/tenants/${tenantId}/coas/${coaid}/cbus/${formattedCbuid}/phases/${phase}/realTimeWorkers`,
        method: 'GET',
        tenantId: tenantId,
    };
    const response = await auraAppService(atlantaUrl).executeRequest(requestBody);

    return response;
};

// Reprocess worker using startWorkflowProcessV2
export const reprocessWorker = async (payload: {
    rheaRequestUrl: string;
    auraAppBaseUrl: string;
    coaCode: string;
    cbuId: string;
    phaseId: string;
    pdaCode: string;
    rdCode?: string;
    tenantId: string;
    workerCode: string;
}) => {
    const {
        rheaRequestUrl,
        auraAppBaseUrl,
        coaCode,
        cbuId,
        phaseId,
        pdaCode,
        rdCode = "RQDINZZ0326",
        tenantId,
        workerCode
    } = payload;

    // Prepare request parameters
    const request = {
        tenantId: tenantId,
        requestType: 'BR' // Business Request type
    };

    // Prepare workflow payload following the pattern from RunEodProcessModal
    const workflowPayload = {
        auraAppBaseUrl: {
            value: auraAppBaseUrl,
            type: 'String'
        },
        coaCode: {
            value: coaCode,
            type: 'String'
        },
        cbuId: {
            value: cbuId,
            type: 'String'
        },
        phaseId: {
            value: phaseId,
            type: 'String'
        },
        pdaCode: {
            value: pdaCode,
            type: 'String'
        },
        rdCode: {
            value: rdCode,
            type: 'String'
        },
        tenantId: {
            value: tenantId,
            type: 'String'
        },
        workerCode: {
            value: workerCode,
            type: 'String'
        },
        processKey: {
            value: `Reprocess_Worker_${workerCode}_${dayjs().format('YYYYMMDD_HHmmss')}`,
            type: 'String'
        },
        actionType: {
            value: 'Reprocess Worker',
            type: 'String'
        },
        requestedBy: {
            value: 'Real-time Worker Dashboard',
            type: 'String'
        },
        requestedTime: {
            value: dayjs().toISOString(),
            type: 'String'
        },
        isRetry: {
            value: true,
            type: 'Boolean'
        }
    };

    try {
        const response = await rheaService(rheaRequestUrl).executeRequest({
            serviceName: 'RHEA_SERVICE',
            path: '/business-requests',
            method: 'POST',
            tenantId: tenantId,
            data: {
                request,
                payload: workflowPayload
            }
        });

        return {
            success: true,
            requestId: response.data?.requestId || '',
            message: `Worker ${workerCode} reprocess request submitted successfully`
        };
    } catch (error) {
        console.error(`Failed to reprocess worker ${workerCode}:`, error);
        return {
            success: false,
            error: (error as Error).message,
            message: `Failed to submit reprocess request for worker ${workerCode}`
        };
    }
};