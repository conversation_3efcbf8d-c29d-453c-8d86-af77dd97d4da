<template>
  <HerculesBusinessComponentRoot style="margin:2rem">
    <div class="header-row">
      <z-input
        v-model="searchQuery"
        placeholder="Search Worker"
        aria-label="Search Worker"
        clearable
        class="search-input"
        round
      />
      <z-button
        variant="outlined"
        @click="toggleSortOrder"
        :aria-pressed="sortOrder !== 'default'"
        aria-label="Toggle Failed postings sort order "
        round
      >
        Failed postings
        <span class="sort-icon" v-if="sortOrder === 'asc'">↑</span>
        <span class="sort-icon" v-else-if="sortOrder === 'desc'">↓</span>
      </z-button>
    </div>

    <!-- Production Health Status Bar -->
    <div v-if="isProductionMode && healthStatus" class="health-status-bar" :class="`health-${healthStatus.overallHealth.toLowerCase()}`">
      <div class="health-info">
        <z-text strong>System Health: </z-text>
        <z-text class="health-status" :class="`status-${healthStatus.overallHealth.toLowerCase()}`">
          {{ healthStatus.overallHealth }}
        </z-text>
        <z-text class="health-details">
          {{ healthStatus.healthyWorkers }}/{{ healthStatus.totalWorkers }} workers healthy
        </z-text>
        <z-text v-if="healthStatus.criticalAlerts > 0" class="critical-alerts">
          {{ healthStatus.criticalAlerts }} critical alerts
        </z-text>
      </div>
      <div class="last-refresh">
        <z-text class="refresh-time">Last updated: {{ lastRefresh.toLocaleTimeString() }}</z-text>
      </div>
    </div>

    <div class="total-postings">
      <z-text strong>Total postings received: </z-text>
      <z-text class="posting-count" tag="span">{{ totalPostings }}</z-text>
    </div>

    <div v-if="error" class="result-container">
      <z-result
        status="error"
        :title="error"
        description="Please try again later or contact support if the problem persists."
      />
    </div>
    <div v-else-if="!loading && (!workers.length || !filteredAndSortedWorkers.length)" class="result-container">
      <z-result
        status="info"
        :title="!workers.length ? 'No workers found' : 'No matching workers'"
        :description="!workers.length ? 'There are currently no workers available.' : 'Try adjusting your search criteria.'"
      />
    </div>
    <div v-else class="cards-grid" role="list">
      <CardComponent
        v-for="worker in filteredAndSortedWorkers"
        :key="worker.id"
        :name="worker.name"
        :successCount="worker.successCount"
        :totalPostings="worker.totalPostings"
        :failedCount="worker.failedCount"
        :queueCount="worker.queueCount"
        :highlighted="worker.highlighted"
        :status="worker.status"
        :metrics="worker.metrics"
        :alerts="worker.alerts"
        :enableProductionFeatures="isProductionMode"
        :onRestart="() => handleWorkerRestart(worker.code || worker.id)"
        :onAcknowledgeAlert="(alertId: string) => handleAlertAcknowledge(worker.code || worker.id, alertId)"
        role="listitem"
      />
    </div>

  </HerculesBusinessComponentRoot>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue';
import { HerculesBusinessComponentRoot } from '@hercules/component-core';
import { ZText, ZInput, ZButton, ZResult } from '@zeta-gds/components';
import CardComponent from './components/card.vue';
import { getRealTimeWorkerData, getEnhancedRealTimeWorkerData, performProductionHealthCheck, restartProductionWorker, acknowledgeWorkerAlert } from './services/Services';

export default defineComponent({
  name: 'WorkerList',
  components: {
    HerculesBusinessComponentRoot,
    ZText,
    ZInput,
    ZButton,
    CardComponent,
    ZResult
  },
  props: {
    /**
     * The tenant identifier (string or number) for fetching worker data.
     */
    tenantId: {
      type: [String, Number],
      required: true
    },
    /**
     * The COA (Chart of Accounts) identifier as a string used in the API endpoint.
     */
    coaid: {
      type: String,
      required: true
    },
    /**
     * The CBU (Cost Business Unit) identifier as a string used in the API endpoint.
     */
    cbuid: {
      type: String,
      required: true
    },
    /**
     * The service URL for the API endpoint
     */
     atalantaServiceUrl: {
      type: String,
      required: true
    },
    /**
     * Enable production-specific features like metrics and alerts
     */
    enableProductionFeatures: {
      type: Boolean,
      default: false,
    },
    /**
     * Enable worker metrics collection
     */
    enableMetrics: {
      type: Boolean,
      default: true,
    },
    /**
     * Enable worker alerts monitoring
     */
    enableAlerts: {
      type: Boolean,
      default: true,
    },
    /**
     * Auto-refresh interval in milliseconds
     */
    autoRefreshInterval: {
      type: Number,
      default: 30000, // 30 seconds
    },
    /**
     * Enable health check functionality
     */
    enableHealthCheck: {
      type: Boolean,
      default: true,
    },
  },
  setup(props) {
    const searchQuery = ref<string>('');
    const sortOrder = ref<'default' | 'asc' | 'desc'>('default');
    const workers = ref<any[]>([]);
    const totalPostings = ref<number>(0);
    const loading = ref<boolean>(false);
    const error = ref<string | null>(null);

    // Production-specific reactive state
    const healthStatus = ref<any>(null);
    const refreshTimer = ref<NodeJS.Timeout | null>(null);
    const lastRefresh = ref<Date>(new Date());
    const isProductionMode = computed(() => props.enableProductionFeatures);

    async function fetchWorkers() {
      loading.value = true;
      error.value = null;
      try {
        const payload = {
          tenantId: String(props.tenantId),
          coaid: props.coaid,
          cbuid: props.cbuid,
          atlantaUrl: props.atalantaServiceUrl,
          includeMetrics: props.enableMetrics,
          includeAlerts: props.enableAlerts
        };

        // Use enhanced data fetching if production features are enabled
        let response;
        if (isProductionMode.value) {
          response = await getEnhancedRealTimeWorkerData(payload);
        } else {
          response = await getRealTimeWorkerData(payload);
        }

        let { data } = response;
       data = {
  "tenantID": 600309,
  "coaCode": "COAUS001007",
  "cbu": "2007-10-20",
  "phase": "ACTIVE",
  "totalPostings": **********,
  "realTimeWorkers": [
    {
      "code": "RTWUS0010",
      "name": "Coupon Processing Worker1",
      "successfullPostingsCount": 89849,
      "failedPostingsCount": 100,
      "pendingPostingsCount": 100,
      "status": "HEALTHY",
      "clusterName": "aura",
      "description": "real time worker to rebuild transaction summaries for a ledger",
      "dlqProcessingStatus": "INACTIVE"
    },
    {
      "code": "RTWUS0011",
      "name": "Transaction Processing Worker2",
      "successfullPostingsCount": 50849,
      "failedPostingsCount": 5,
      "pendingPostingsCount": 200,
      "status": "SLOW",
      "clusterName": "aura",
      "description": "real time worker to process transactions for the ledger",
      "dlqProcessingStatus": "ACTIVE"
    },
    {
      "code": "RTWUS0012",
      "name": "Coupon Processing Worker3",
      "successfullPostingsCount": 89849,
      "failedPostingsCount": 100,
      "pendingPostingsCount": 100,
      "status": "HEALTHY",
      "clusterName": "aura",
      "description": "real time worker to rebuild transaction summaries for a ledger",
      "dlqProcessingStatus": "INACTIVE"
    },
    {
      "code": "RTWUS0013",
      "name": "Coupon Processing Worker",
      "successfullPostingsCount": 83849,
      "failedPostingsCount": 180,
      "pendingPostingsCount": 100,
      "status": "UNHEALTHY",
      "clusterName": "aura",
      "description": "real time worker to rebuild transaction summaries for a ledger",
      "dlqProcessingStatus": "INACTIVE"
    },
    {
      "code": "RTWUS0014",
      "name": "Coupon Processing Worker",
      "successfullPostingsCount": 83849,
      "failedPostingsCount": 10,
      "pendingPostingsCount": 100,
      "status": "SLOW",
      "clusterName": "aura",
      "description": "real time worker to rebuild transaction summaries for a ledger",
      "dlqProcessingStatus": "INACTIVE"
    }
  ]
}
 

        workers.value = data?.realTimeWorkers?.map((worker: any) => ({
          id: `${worker.code}-${worker.name}`,
          name: worker.name,
          successCount: worker.successfullPostingsCount,
          totalPostings:
            worker.successfullPostingsCount +
            worker.failedPostingsCount +
            worker.pendingPostingsCount,
          failedCount: worker.failedPostingsCount,
          queueCount: worker.pendingPostingsCount,
          highlighted: worker.status === 'UNHEALTHY',
          status: worker.status
        }));
        totalPostings.value = data.totalPostings;
        lastRefresh.value = new Date();

        // Perform health check if enabled and in production mode
        if (isProductionMode.value && props.enableHealthCheck) {
          try {
            healthStatus.value = await performProductionHealthCheck({
              tenantId: String(props.tenantId),
              coaid: props.coaid,
              cbuid: props.cbuid,
              atlantaUrl: props.atalantaServiceUrl
            });
          } catch (healthError) {
            console.warn('Health check failed:', healthError);
          }
        }
      } catch (err: any) {
        error.value = err.message || 'Failed to fetch workers';
        workers.value = [];
        totalPostings.value = 0;
      } finally {
        loading.value = false;
      }
    }



    // Production action handlers
    const handleWorkerRestart = async (workerCode: string, reason?: string) => {
      try {
        const success = await restartProductionWorker({
          tenantId: String(props.tenantId),
          coaid: props.coaid,
          cbuid: props.cbuid,
          atlantaUrl: props.atalantaServiceUrl,
          workerCode,
          reason
        });

        if (success) {
          // Refresh data after restart
          await fetchWorkers();
        }
        return success;
      } catch (error) {
        console.error('Failed to restart worker:', error);
        return false;
      }
    };

    const handleAlertAcknowledge = async (workerCode: string, alertId: string) => {
      try {
        const success = await acknowledgeWorkerAlert({
          tenantId: String(props.tenantId),
          coaid: props.coaid,
          cbuid: props.cbuid,
          atlantaUrl: props.atalantaServiceUrl,
          workerCode,
          alertId
        });

        if (success) {
          // Refresh data after acknowledging alert
          await fetchWorkers();
        }
        return success;
      } catch (error) {
        console.error('Failed to acknowledge alert:', error);
        return false;
      }
    };

    // Auto-refresh setup
    const setupAutoRefresh = () => {
      if (refreshTimer.value) {
        clearInterval(refreshTimer.value);
      }

      if (props.autoRefreshInterval > 0) {
        refreshTimer.value = setInterval(() => {
          fetchWorkers();
        }, props.autoRefreshInterval);
      }
    };

    // Cleanup function
    const cleanup = () => {
      if (refreshTimer.value) {
        clearInterval(refreshTimer.value);
        refreshTimer.value = null;
      }
    };

    watch(
      () => [props.tenantId, props.coaid, props.cbuid],
      fetchWorkers ,
      { immediate: true }
    );

    watch(
      () => props.autoRefreshInterval,
      setupAutoRefresh,
      { immediate: true }
    );

    // Cleanup on unmount
    watch(
      () => false, // This will never trigger, but we need it for onUnmounted
      cleanup
    );

    const filteredWorkers = computed(() => {
      if (!searchQuery.value) return workers.value;
      return workers.value.filter(worker =>
        worker.name.toLowerCase().includes(searchQuery.value.toLowerCase())
      );
    });

    const filteredAndSortedWorkers = computed(() => {
      if (sortOrder.value === 'default') return filteredWorkers.value;
      return [...filteredWorkers.value].sort((a, b) =>
        sortOrder.value === 'asc'
          ? a.failedCount - b.failedCount
          : b.failedCount - a.failedCount
      );
    });

    function toggleSortOrder(): void {
      if (sortOrder.value === 'default') {
        sortOrder.value = 'asc';
      } else if (sortOrder.value === 'asc') {
        sortOrder.value = 'desc';
      } else {
        sortOrder.value = 'default';
      }
    }

    return {
      searchQuery,
      totalPostings,
      sortOrder,
      filteredAndSortedWorkers,
      toggleSortOrder,
      loading,
      error,
      workers,
      // Production features
      healthStatus,
      lastRefresh,
      isProductionMode,
      handleWorkerRestart,
      handleAlertAcknowledge,
      cleanup
    };
  },
});
</script>

<style scoped lang="scss">
.header-row {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 1rem;
}

.search-input {
  flex-grow: 1;
  max-width: 300px;
  border: 1px solid #ccc;
}

.total-postings {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.posting-count {
  background-color: #e7f0ff;
  color: #1a73e8;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
}

.result-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  margin-top: 2rem;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

/* Production Health Status Styles */
.health-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 8px;
  border-left: 4px solid;
}

.health-healthy {
  background-color: #f0f9ff;
  border-left-color: #10b981;
}

.health-degraded {
  background-color: #fffbeb;
  border-left-color: #f59e0b;
}

.health-critical {
  background-color: #fef2f2;
  border-left-color: #ef4444;
}

.health-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-healthy {
  color: #10b981;
  font-weight: 600;
}

.status-degraded {
  color: #f59e0b;
  font-weight: 600;
}

.status-critical {
  color: #ef4444;
  font-weight: 600;
}

.health-details {
  color: #6b7280;
  font-size: 0.875rem;
}

.critical-alerts {
  color: #ef4444;
  font-weight: 600;
  font-size: 0.875rem;
}

.last-refresh {
  display: flex;
  align-items: center;
}

.refresh-time {
  color: #6b7280;
  font-size: 0.75rem;
}

.sort-icon {
  padding-left: 0.5rem;
}
</style>
