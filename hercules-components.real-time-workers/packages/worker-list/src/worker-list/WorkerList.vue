<template>
  <HerculesBusinessComponentRoot style="margin:2rem">
    <div class="header-row">
      <z-input
        v-model="searchQuery"
        placeholder="Search Worker"
        aria-label="Search Worker"
        clearable
        class="search-input"
        round
      />
      <z-button
        variant="outlined"
        @click="toggleSortOrder"
        :aria-pressed="sortOrder !== 'default'"
        aria-label="Toggle Failed postings sort order "
        round
      >
        Failed postings
        <span class="sort-icon" v-if="sortOrder === 'asc'">↑</span>
        <span class="sort-icon" v-else-if="sortOrder === 'desc'">↓</span>
      </z-button>
    </div>

    <div class="total-postings">
      <z-text strong>Total postings received: </z-text>
      <z-text class="posting-count" tag="span">{{ totalPostings }}</z-text>
    </div>

    <div v-if="error" class="result-container">
      <z-result
        status="error"
        :title="error"
        description="Please try again later or contact support if the problem persists."
      />
    </div>
    <div v-else-if="!loading && (!workers.length || !filteredAndSortedWorkers.length)" class="result-container">
      <z-result
        status="info"
        :title="!workers.length ? 'No workers found' : 'No matching workers'"
        :description="!workers.length ? 'There are currently no workers available.' : 'Try adjusting your search criteria.'"
      />
    </div>
    <div v-else class="cards-grid" role="list">
      <CardComponent
        v-for="worker in filteredAndSortedWorkers"
        :key="worker.id"
        :name="worker.name"
        :code="worker.code"
        :description="worker.description"
        :clusterName="worker.clusterName"
        :successCount="worker.successCount"
        :totalPostings="worker.totalPostings"
        :failedCount="worker.failedCount"
        :queueCount="worker.queueCount"
        :highlighted="worker.highlighted"
        :status="worker.status"
        @reprocess="handleReprocess"
        role="listitem"
      />
    </div>

  </HerculesBusinessComponentRoot>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue';
import { HerculesBusinessComponentRoot } from '@hercules/component-core';
import { ZText, ZInput, ZButton, ZResult } from '@zeta-gds/components';
import CardComponent from './components/card.vue';
import { getRealTimeWorkerData, reprocessWorker } from './services/Services';
import dayjs from 'dayjs';

export default defineComponent({
  name: 'WorkerList',
  components: {
    HerculesBusinessComponentRoot,
    ZText,
    ZInput,
    ZButton,
    CardComponent,
    ZResult
  },
  props: {

    /** 
     * hercules base url
    */
    herculesBaseUrl: {
      type: String,
      required: true
    },

    /**
     * rhea base url
     */
    rheaRequestUrl: {
      type: String,
      required: true
    },
    /**
     * The tenant identifier (string or number) for fetching worker data.
     */
    tenantId: {
      type: [String, Number],
      required: true
    },
    /**
     * The COA (Chart of Accounts) identifier as a string used in the API endpoint.
     */
    coaid: {
      type: String,
      required: true
    },
    /**
     * The CBU (Cost Business Unit) identifier as a string used in the API endpoint.
     */
    cbuid: {
      type: String,
      required: true
    },
    /**
     * The service URL for the API endpoint
     */
     atalantaServiceUrl: {
      type: String,
      required: true
    },
    /**
     * The aura app base URL for reprocessing
     */
    auraAppBaseUrl: {
      type: String,
      required: false,
      default: ''
    }
  },
  setup(props) {
    const searchQuery = ref<string>('');
    const sortOrder = ref<'default' | 'asc' | 'desc'>('default');
    const workers = ref<any[]>([]);
    const totalPostings = ref<number>(0);
    const loading = ref<boolean>(false);
    const error = ref<string | null>(null);

    async function fetchWorkers() {
      loading.value = true;
      error.value = null;
      try {
        const payload = {
          tenantId: props.tenantId,
          coaid: props.coaid,
          cbuid:  props.cbuid,
          atlantaUrl: props.atalantaServiceUrl
          
        };
       
        let { data } = await getRealTimeWorkerData( payload);
       data = {
  "tenantID": 600309,
  "coaCode": "COAUS001007",
  "cbu": "2007-10-20",
  "phase": "ACTIVE",
  "totalPostings": **********,
  "realTimeWorkers": [
    {
      "code": "RTWUS0010",
      "name": "Coupon Processing Worker1",
      "successfullPostingsCount": 89849,
      "failedPostingsCount": 100,
      "pendingPostingsCount": 100,
      "status": "HEALTHY",
      "clusterName": "aura",
      "description": "real time worker to rebuild transaction summaries for a ledger",
      "dlqProcessingStatus": "INACTIVE"
    },
    {
      "code": "RTWUS0011",
      "name": "Transaction Processing Worker2",
      "successfullPostingsCount": 50849,
      "failedPostingsCount": 5,
      "pendingPostingsCount": 200,
      "status": "SLOW",
      "clusterName": "aura",
      "description": "real time worker to process transactions for the ledger",
      "dlqProcessingStatus": "ACTIVE"
    },
    {
      "code": "RTWUS0012",
      "name": "Coupon Processing Worker3",
      "successfullPostingsCount": 89849,
      "failedPostingsCount": 100,
      "pendingPostingsCount": 100,
      "status": "HEALTHY",
      "clusterName": "aura",
      "description": "real time worker to rebuild transaction summaries for a ledger",
      "dlqProcessingStatus": "INACTIVE"
    },
    {
      "code": "RTWUS0013",
      "name": "Coupon Processing Worker",
      "successfullPostingsCount": 83849,
      "failedPostingsCount": 180,
      "pendingPostingsCount": 100,
      "status": "UNHEALTHY",
      "clusterName": "aura",
      "description": "real time worker to rebuild transaction summaries for a ledger",
      "dlqProcessingStatus": "INACTIVE"
    },
    {
      "code": "RTWUS0014",
      "name": "Coupon Processing Worker",
      "successfullPostingsCount": 83849,
      "failedPostingsCount": 10,
      "pendingPostingsCount": 100,
      "status": "SLOW",
      "clusterName": "aura",
      "description": "real time worker to rebuild transaction summaries for a ledger",
      "dlqProcessingStatus": "INACTIVE"
    }
  ]
}
 

        workers.value = data?.realTimeWorkers?.map((worker: any) => ({
          id: `${worker.code}-${worker.name}`,
          name: worker.name,
          code: worker.code,
          description: worker.description,
          clusterName: worker.clusterName,
          successCount: worker.successfullPostingsCount,
          totalPostings:
            worker.successfullPostingsCount +
            worker.failedPostingsCount +
            worker.pendingPostingsCount,
          failedCount: worker.failedPostingsCount,
          queueCount: worker.pendingPostingsCount,
          highlighted: worker.status === 'UNHEALTHY',
          status: worker.status
        }));
        totalPostings.value = data.totalPostings;
      } catch (err: any) {
        error.value = err.message || 'Failed to fetch workers';
        workers.value = [];
        totalPostings.value = 0;
      } finally {
        loading.value = false;
      }
    }



    watch(
      () => [props.tenantId, props.coaid, props.cbuid],
      fetchWorkers , 
      { immediate: true }
    );

    const filteredWorkers = computed(() => {
      if (!searchQuery.value) return workers.value;
      return workers.value.filter(worker =>
        worker.name.toLowerCase().includes(searchQuery.value.toLowerCase())
      );
    });

    const filteredAndSortedWorkers = computed(() => {
      if (sortOrder.value === 'default') return filteredWorkers.value;
      return [...filteredWorkers.value].sort((a, b) =>
        sortOrder.value === 'asc'
          ? a.failedCount - b.failedCount
          : b.failedCount - a.failedCount
      );
    });

    function toggleSortOrder(): void {
      if (sortOrder.value === 'default') {
        sortOrder.value = 'asc';
      } else if (sortOrder.value === 'asc') {
        sortOrder.value = 'desc';
      } else {
        sortOrder.value = 'default';
      }
    }

    async function handleReprocess(workerData: any): Promise<void> {
      try {
        console.log('Starting reprocess for worker:', workerData);

        // Validate required props
        if (!props.auraAppBaseUrl) {
          console.error('auraAppBaseUrl is required for reprocessing');
          return;
        }

        if (!workerData.code) {
          console.error('Worker code is required for reprocessing');
          return;
        }

        const reprocessPayload = {
          herculesServiceUrl: props.herculesBaseUrl,
          rheaRequestUrl: props.rheaRequestUrl,
          auraAppBaseUrl: props.auraAppBaseUrl,
          coaCode: props.coaid, // Use coaid prop for coaCode
          cbuId: dayjs(Number(props.cbuid)).format("YYYY-MM-DD"),
          phaseId: 'ACTIVE', // Always ACTIVE as const
          pdaCode: workerData.code, // Use worker code as pdaCode
          rdCode: 'RQDINZZ0326', // Always RQDINZZ0326 as const
          tenantId: String(props.tenantId),
          workerCode: workerData.code,
          appUrl: 'lsg-eodc0hercules.internal.mum1-pp.zetaapps.in' // window.location.hostname,
        };

        const result = await reprocessWorker(reprocessPayload);

        if (result.success) {
          console.log('Reprocess request submitted successfully:', result);
          // Optionally refresh the worker data to show updated status
          await fetchWorkers();
        } else {
          console.error('Reprocess request failed:', result.error);
        }
      } catch (error) {
        console.error('Error during reprocess:', error);
      }
    }

    return {
      searchQuery,
      totalPostings,
      sortOrder,
      filteredAndSortedWorkers,
      toggleSortOrder,
      handleReprocess,
      loading,
      error,
      workers
    };
  },
});
</script>

<style scoped lang="scss">
.header-row {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 1rem;
}

.search-input {
  flex-grow: 1;
  max-width: 300px;
  border: 1px solid #ccc;
}

.total-postings {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.posting-count {
  background-color: #e7f0ff;
  color: #1a73e8;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
}

.result-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  margin-top: 2rem;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.sort-icon {
  padding-left: 0.5rem;
}
</style>
