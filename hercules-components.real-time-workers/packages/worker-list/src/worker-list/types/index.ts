/**
 * Production Worker Types and Interfaces
 * Enhanced types for real-time worker production monitoring
 */

// Base worker status types
export type WorkerStatus = 'HEALTHY' | 'UNHEALTHY' | 'SLOW' | 'CRITICAL';
export type WorkerPhase = 'BOFI' | 'BOPI' | 'EOFI' | 'EOPI' | 'ACTIVE';
export type Environment = 'DEVELOPMENT' | 'STAGING' | 'PRODUCTION';
export type DLQStatus = 'ACTIVE' | 'INACTIVE';

// Alert severity levels
export type AlertSeverity = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

// Health status types
export type HealthStatus = 'HEALTHY' | 'DEGRADED' | 'CRITICAL';

/**
 * Production worker metrics interface
 */
export interface ProductionWorkerMetrics {
  /** CPU usage percentage (0-100) */
  cpuUsage: number;
  /** Memory usage percentage (0-100) */
  memoryUsage: number;
  /** Processing throughput per second */
  throughputPerSecond: number;
  /** Error rate percentage (0-100) */
  errorRate: number;
  /** Last heartbeat timestamp */
  lastHeartbeat: string;
  /** Worker uptime in seconds */
  uptime: number;
  /** Network I/O metrics */
  networkIO?: {
    bytesIn: number;
    bytesOut: number;
  };
  /** Disk I/O metrics */
  diskIO?: {
    readBytes: number;
    writeBytes: number;
  };
}

/**
 * Production worker alert interface
 */
export interface ProductionWorkerAlert {
  /** Unique alert identifier */
  id: string;
  /** Alert severity level */
  severity: AlertSeverity;
  /** Alert message */
  message: string;
  /** Alert timestamp */
  timestamp: string;
  /** Whether alert has been acknowledged */
  acknowledged: boolean;
  /** Alert category */
  category?: 'PERFORMANCE' | 'ERROR' | 'RESOURCE' | 'CONNECTIVITY';
  /** Additional alert metadata */
  metadata?: Record<string, any>;
}

/**
 * Production worker configuration interface
 */
export interface ProductionWorkerConfig {
  /** Maximum retry attempts */
  maxRetries: number;
  /** Request timeout in milliseconds */
  timeoutMs: number;
  /** Processing batch size */
  batchSize: number;
  /** Enable detailed logging */
  enableDetailedLogging: boolean;
  /** Alert threshold configurations */
  alertThresholds: {
    /** Error rate threshold percentage */
    errorRatePercent: number;
    /** Memory usage threshold percentage */
    memoryUsagePercent: number;
    /** CPU usage threshold percentage */
    cpuUsagePercent: number;
    /** Response time threshold in milliseconds */
    responseTimeMs?: number;
  };
  /** Auto-scaling configuration */
  autoScaling?: {
    enabled: boolean;
    minInstances: number;
    maxInstances: number;
    targetCpuPercent: number;
  };
}

/**
 * Base real-time worker interface
 */
export interface BaseRealTimeWorker {
  /** Worker code identifier */
  code: string;
  /** Worker display name */
  name: string;
  /** Successful postings count */
  successfullPostingsCount: number;
  /** Failed postings count */
  failedPostingsCount: number;
  /** Pending postings count */
  pendingPostingsCount: number;
  /** Worker status */
  status: WorkerStatus;
  /** Cluster name */
  clusterName: string;
  /** Worker description */
  description: string;
  /** DLQ processing status */
  dlqProcessingStatus: DLQStatus;
}

/**
 * Enhanced real-time worker with production features
 */
export interface EnhancedRealTimeWorker extends BaseRealTimeWorker {
  /** Production metrics */
  metrics?: ProductionWorkerMetrics;
  /** Active alerts */
  alerts?: ProductionWorkerAlert[];
  /** Worker configuration */
  config?: ProductionWorkerConfig;
  /** Deployment environment */
  environment: Environment;
  /** Worker version */
  version: string;
  /** Last deployment timestamp */
  lastDeployment: string;
  /** Worker phase */
  phase?: WorkerPhase;
  /** Additional production metadata */
  productionMetadata?: {
    /** Deployment region */
    region?: string;
    /** Kubernetes namespace */
    namespace?: string;
    /** Pod name */
    podName?: string;
    /** Node name */
    nodeName?: string;
  };
}

/**
 * Worker list response interface
 */
export interface WorkerListResponse {
  /** Tenant ID */
  tenantID: number;
  /** COA code */
  coaCode: string;
  /** CBU identifier */
  cbu: string;
  /** Processing phase */
  phase: string;
  /** Total postings count */
  totalPostings: number;
  /** Array of real-time workers */
  realTimeWorkers: EnhancedRealTimeWorker[];
}

/**
 * Production health check result
 */
export interface ProductionHealthCheck {
  /** Overall system health status */
  overallHealth: HealthStatus;
  /** Total number of workers */
  totalWorkers: number;
  /** Number of healthy workers */
  healthyWorkers: number;
  /** Number of unhealthy workers */
  unhealthyWorkers: number;
  /** Number of critical alerts */
  criticalAlerts: number;
  /** Health recommendations */
  recommendations: string[];
  /** Detailed health metrics */
  healthMetrics?: {
    /** Average CPU usage across all workers */
    avgCpuUsage: number;
    /** Average memory usage across all workers */
    avgMemoryUsage: number;
    /** Total throughput per second */
    totalThroughput: number;
    /** Overall error rate */
    overallErrorRate: number;
  };
}

/**
 * Worker action result interface
 */
export interface WorkerActionResult {
  /** Whether action was successful */
  success: boolean;
  /** Action result message */
  message?: string;
  /** Error details if action failed */
  error?: string;
  /** Additional result data */
  data?: any;
}

/**
 * Worker log entry interface
 */
export interface WorkerLogEntry {
  /** Log timestamp */
  timestamp: string;
  /** Log level */
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
  /** Log message */
  message: string;
  /** Additional log context */
  context?: Record<string, any>;
}

/**
 * Production worker props interface for Vue components
 */
export interface ProductionWorkerProps {
  /** Enable production-specific features */
  enableProductionFeatures?: boolean;
  /** Enable metrics collection */
  enableMetrics?: boolean;
  /** Enable alerts monitoring */
  enableAlerts?: boolean;
  /** Auto-refresh interval in milliseconds */
  autoRefreshInterval?: number;
  /** Enable health check functionality */
  enableHealthCheck?: boolean;
  /** Custom action handlers */
  onWorkerRestart?: (workerCode: string, reason?: string) => Promise<boolean>;
  /** Alert acknowledgment handler */
  onAlertAcknowledge?: (workerCode: string, alertId: string) => Promise<boolean>;
}

/**
 * Service payload interfaces
 */
export interface BaseServicePayload {
  tenantId: string;
  coaid: string;
  cbuid: string;
  atlantaUrl: string;
}

export interface EnhancedWorkerDataPayload extends BaseServicePayload {
  includeMetrics?: boolean;
  includeAlerts?: boolean;
}

export interface WorkerMetricsPayload extends BaseServicePayload {
  workerCode: string;
}

export interface WorkerActionPayload extends BaseServicePayload {
  workerCode: string;
  reason?: string;
}

export interface AlertActionPayload extends BaseServicePayload {
  workerCode: string;
  alertId: string;
}

export interface WorkerLogsPayload extends BaseServicePayload {
  workerCode: string;
  limit?: number;
}
