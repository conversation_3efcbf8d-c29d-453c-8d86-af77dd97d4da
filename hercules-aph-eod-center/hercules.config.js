module.exports = {
    name: 'EOD Center',
    description: 'EOD Center web app powered by Hercules',
    code: 'hercules-aph-eod-center',
    store: {},
    attributes: {
        routeBasePath: 'eod-center',
        locale: 'en-IN',
        omsBaseUrl: 'https://proteus-cipher.mum1-pp.zeta.in',
        auraAppBaseUrl: 'https://auraapps.internal.mum1-pp.zetaapps.in',
        herculesServiceBaseUrl: 'https://0-0-hercules.mum1-pp.zetaapps.in/hercules-service',
        herculesServiceClientBaseUrl: 'https://0-0-hercules.mum1-pp.zetaapps.in/hercules-service',
        cipherBaseUrl: 'https://sb1-god-cipher.mum1-pp.zetaapps.in/proteus',
        OMS: 'https://api.preprod.zetaapps.in/zeta.in',
        OMS_BASIC: 'https://api.mum1-pp.zetaapps.in/zeta.in',
        SSO: 'https://sso-pp.zetaapps.in',
        OAUTH: 'https://oauth-pp.zetaapps.in',
        RHEA: 'https://camunda.rhea.mum1-pp.zetaapps.in',
        AURA_URL: 'https://sb1-god-aura.mum1-pp.zetaapps.in/tachyon',
        AURA_INTERNAL_URL: 'https://aura.internal.mum1-pp.zetaapps.in',
        LEDGER_MANAGER_URL: 'https://api.internal.mum1-pp.zetaapps.in',
        BOOKKEEPER_URL: 'https://aura.internal.mum1-pp.zetaapps.in/',
        ANGELOS_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos',
        GRAFANA_URL: 'https://owcc-grafana.internal.mum1-pp.zetaapps.in/d/lm9M9q97k/orchestra?orgId=1',
        GRAFANA_CLEARING_STATUS_URL: 'https://owcc-grafana.internal.mum1-pp.zetaapps.in/d/lm9M9q97k/orchestra?orgId=1',
        ATLANTA_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/atalanta',
        ATLANTA_ORCHESTRA_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/atalanta/orchestra/api/v1',
        WORKFLOW_URL: 'https://operations-management.internal.mum1-pp.zetaapps.in/operations-center/',
        BOOKKEEPER_TRIGGER_URL: 'https://orchestra.internal.mum1-pp.zetaapps.in/bookkeeper',
        ORCHESTRA_URL: 'https://aura.internal.mum1-pp.zetaapps.in/orchestra-v3',
        RHEA_REQUEST_URL: 'https://operations-management.internal.mum1-pp.zetaapps.in/service-request',
        OMS_URL: 'https://proteus-cipher.mum1-pp.zeta.in',
        LOCKER_SERVICE_URL: 'https://api.mum1-pp.zetaapps.in/zeta.in',
        RHEA_BASE_URL: 'https://rhea.internal.mum1-pp.zetaapps.in/camunda',
        TACHYON_URL: 'https://sb1-god-aura.mum1-pp.zetaapps.in/tachyon',
        TACHYON_CALENDAR_URL: 'https://sb1-god-aura.mum1-pp.zetaapps.in/tachyon/calendar',
        DOMAIN: 'rbl-admin.in',
    },
    routes: [
        {
            name: 'Aphrodite EOD Center',
            path: '/',
            enableHistoryRoute: true,
            src: '@src/index.hpage',
        },
    ],
};
