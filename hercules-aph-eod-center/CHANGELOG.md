### Changelog

All notable changes to this project will be documented in this file. Dates are displayed in UTC.

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

#### [1.12.0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-20...1.12.0)

- fix(SPE-6273): add feature flag for realtime worker [`#55`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/55)
- fix(SPB-2344): cutoff schedule API driven by feature flag [`#54`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/54)

#### [1.12.0-20](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-19...1.12.0-20)

> 18 June 2025

- Release 1.12.0-20 [`a542db8`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/a542db84682bf270713c5c224c02af3181c562f7)
- feat: mis report version bump-up [`145b97b`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/145b97bb346dec2a823af5cdda0a628b2364001e)

#### [1.12.0-19](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-18...1.12.0-19)

> 18 June 2025

- Release 1.12.0-19 [`81fc4f1`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/81fc4f1ee701b49e4151a81e98d9bea4d45d2a60)
- feat: version increment [`4d478d0`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/4d478d03feb1ad010ec3bccdfb48c41685268d1c)

#### [1.12.0-18](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-17...1.12.0-18)

> 18 June 2025

- Release 1.12.0-18 [`eb3475b`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/eb3475b4dec19d457a5dc6c1375e462508c44777)
- feat: mis biz comp version bump-up [`51ad6cf`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/51ad6cf7fd4ade2eaa3394d106ba94deb648b789)

#### [1.12.0-17](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-16...1.12.0-17)

> 13 June 2025

- Release 1.12.0-17 [`3e8ba63`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/3e8ba63e4b98c498b6bddd7dee104a4e91b77828)
- feat: update orchestra business component version [`749bd15`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/749bd15656bfb213a1cf6d5cf6ff2cf5393019f5)

#### [1.12.0-16](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-15...1.12.0-16)

> 11 June 2025

- Release 1.12.0-16 [`3215745`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/3215745d82ef0d8b0576ffafbef1468dd560399a)
- feat: comment [`a31fbd4`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/a31fbd45f6c8acd4828ca75814dd0513dddc09b5)

#### [1.12.0-15](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-14...1.12.0-15)

> 11 June 2025

- Release 1.12.0-15 [`c4538ca`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c4538ca2b8b57767e7651ea9f24d3a72732b64be)
- feat: updated biz components version [`9cdf68b`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/9cdf68bd2078b447366614031c0beea05cb771ae)

#### [1.12.0-14](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-13...1.12.0-14)

> 9 June 2025

- Release 1.12.0-14 [`71d8e96`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/71d8e9674f12e35263f687b39c4989e29bb9c553)
- feat(SPE-6799): updated comments [`8df41ec`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/8df41ec6dfd3d1a8ad9c23ed37895ad9b4840333)

#### [1.12.0-13](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-12...1.12.0-13)

> 8 June 2025

- feat(SPE-6799): formatting [`d757c4a`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/d757c4a742e28a5de6258b032c362959b3bd4469)
- Release 1.12.0-13 [`c195e0e`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c195e0e25d1c6f85bbf91e3487468c4b60ecc027)

#### [1.12.0-12](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-11...1.12.0-12)

> 6 June 2025

- Release 1.12.0-12 [`81da0c1`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/81da0c162989348e97fa72e3a62736f527f3ab39)
- feat(SPE-6799): formatting [`b669d09`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/b669d0950246a3f66115b0baa028f688b51189d3)

#### [1.12.0-11](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-10...1.12.0-11)

> 6 June 2025

- feat(spe-6799): Calendar enhancement integration [`#53`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/53)
- Release 1.12.0-11 [`794737b`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/794737bbc00a18d0447ce78e3ee33ebdf5c7c095)

#### [1.12.0-10](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-9...1.12.0-10)

> 5 June 2025

- Release 1.12.0-10 [`e6b33a8`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/e6b33a80395ee39b86cf47c5a7ddae1483f995ac)
- chore : release refresh [`c7ee5b8`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c7ee5b8a24889d2d032455d9bbd5c1a0a13ee5c9)

#### [1.12.0-9](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-8...1.12.0-9)

> 5 June 2025

- Release 1.12.0-9 [`ef2c224`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/ef2c224b0d962fa198a38a6d6b97d0c56d0db7b0)
- chore : release refresh [`c799b54`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c799b54325e2622812c4bc9a1cb4c3e5e0ed6f82)

#### [1.12.0-8](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-7...1.12.0-8)

> 4 June 2025

- Release 1.12.0-8 [`ab574e5`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/ab574e56c28ecf02bf9c812f78db00826310fe47)
- feat: release refresh [`e0f8e6f`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/e0f8e6fb9354687cb76331e6e09fe3ddf74bfa86)

#### [1.12.0-7](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-6...1.12.0-7)

> 4 June 2025

- realtime worker and backdated coa [`#52`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/52)
- Release 1.12.0-7 [`7e99823`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/7e99823d21f83c539f6bcebb24117737e17e9d69)
- address comments [`2100bf8`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/2100bf8913218492bdf18a87fdb687d31305e822)

#### [1.12.0-6](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-5...1.12.0-6)

> 30 May 2025

- Release 1.12.0-6 [`43ca019`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/43ca01990d8a4f557e58b18224081413adb0f2b2)
- add keyfor realtime [`ca97a19`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/ca97a195a18e5d048b9e53e73454f7facd0cb224)

#### [1.12.0-5](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-4...1.12.0-5)

> 29 May 2025

- update orchestra business component version [`c40ca01`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c40ca01844fe3702d4ef0aed54cd905dd3aaf7bd)
- Release 1.12.0-5 [`a18d8aa`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/a18d8aa6056853c85161534fe36d9f44ba97d3bb)

#### [1.12.0-4](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-3...1.12.0-4)

> 29 May 2025

- Release 1.12.0-4 [`f9efab2`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/f9efab2df256ab824c15f6ac816884ba59a3805b)
- add prop for enableEodBulkProcessing [`efe89e8`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/efe89e860d998763e796f000ef1dafe2d79fbfa0)

#### [1.12.0-3](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-2...1.12.0-3)

> 29 May 2025

- realtime worker and backdated coa [`#52`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/52)
- Release 1.12.0-3 [`759025c`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/759025c09dabaaf7db71f186392537de309edd4e)

#### [1.12.0-2](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-1...1.12.0-2)

> 26 May 2025

- Feat/spe 3175 [`#51`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/51)
- Release 1.12.0-2 [`5ffc9f2`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/5ffc9f27fd17e9fc2a3e5c326fd902824bbcec53)
- refactor: removed consoles [`11651c8`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/11651c831643a0d1908b194941ec8054f0235a89)

#### [1.12.0-1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.12.0-0...1.12.0-1)

> 26 May 2025

- Release 1.12.0-1 [`4d6dcc4`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/4d6dcc48f3c72430b4b90cfacff56b629af45dfa)
- feat: adding logs for debugging [`f757370`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/f75737058c2632d194da0fcbebc25d516c65ba5b)

#### [1.12.0-0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.11.0...1.12.0-0)

> 23 May 2025

- feat(spe-3175): eod mis reports [`#50`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/50)
- Release 1.12.0-0 [`c768295`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c768295f29d988a9f3f5c40b0b91d3b058a03231)

#### [1.11.0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.11.0-2...1.11.0)

> 19 May 2025

- fix(SPB-1704): CBU Date is not displayed in Balance Explorer Grid Header [`#49`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/49)
-  Develop merge [`#48`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/48)
- feat(SPI-2672): Enable Functionalities regarding Scheduled & Manual cutoff [`#45`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/45)
- Revert "Merge pull request #45 from Zeta-Enterprise/feat/SPI-2672" [`8ac3fe5`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/8ac3fe5d240d09adf10bcb03b612258675938b96)
- Release 1.11.0 [`d0d008f`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/d0d008fa0228cc8829ca7a6566b6de1458191839)

#### [1.11.0-2](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.11.0-1...1.11.0-2)

> 16 May 2025

- feat(SPI-2672): scheduled cut off time local timezone handling [`53c457a`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/53c457a1775150038008da8e555835e547bada69)
- Release 1.11.0-2 [`81b25c5`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/81b25c554797b06edaebc00f86e6ac3f11b70fd4)
- feat: reverted temp handling for feature flags [`9287ff4`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/9287ff438b437fff3c4acef3b7d4d93552325088)

#### [1.11.0-1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.11.0-0...1.11.0-1)

> 12 May 2025

- fix(SPI-2672): ts error fix [`#47`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/47)
- Release 1.11.0-1 [`93a1d9c`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/93a1d9c1f179dce1786202e7b92262a71c71f009)

#### [1.11.0-0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.14...1.11.0-0)

> 12 May 2025

- Feat/spi 2672 [`#46`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/46)
- feat(SPI-2672): Enable Functionalities regarding Scheduled & Manual cutoff [`f395031`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/f395031bfcf550bfa00c85f60908e49abac15d4e)
- fix: PR review changes [`71c22fb`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/71c22fb0383c0daba63fdc30a30d802869c38260)
- Release 1.11.0-0 [`6f6d8b9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/6f6d8b943056b93d63c80312e88b1bf181020f71)

#### [1.10.14](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.13...1.10.14)

> 6 May 2025

- bugfix spb-1391 [`#43`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/43)
- Release 1.10.14 [`76c41f0`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/76c41f00b09d58a9be3163aabeefa16ad28a037c)

#### [1.10.13](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.12...1.10.13)

> 10 April 2025

- bugfix tcu-2750 [`#42`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/42)
- Release 1.10.13 [`3b56dec`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/3b56decd805b8dc594eb303f6b297aa68e732761)

#### [1.10.12](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.11...1.10.12)

> 24 March 2025

- Release 1.10.12 [`2ca97cb`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/2ca97cb052b2db043b9144e55a76e5655952dede)
- update orchestra business component version [`088d419`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/088d419c794445be1fd73533e427b64488b3da73)

#### [1.10.11](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.10...1.10.11)

> 24 March 2025

- Release 1.10.11 [`738b662`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/738b662c136b7cbcefa00064ccea00a354f86c86)
- update orchestra business component version [`29feec9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/29feec997922d60fc9842e71da901956bab6b297)

#### [1.10.10](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.9...1.10.10)

> 24 March 2025

- update calculate time logic [`#41`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/41)
- Release 1.10.10 [`c65e060`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c65e0601fed579e9a00a376752352c44fbf719ba)

#### [1.10.9](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.9-0...1.10.9)

> 21 March 2025

- Feature/automate eod spe 3180 [`#40`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/40)
- format files [`8465e67`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/8465e671370cbfcf54d84f6711a4611991f51488)
- address comments for automate eod [`e8dd264`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/e8dd2642a9ba919e2e666acdad64d182c05d34c6)
- update pause or scheduled eod [`cf6136f`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/cf6136f271a482b0666303a271150cb8cf2e9346)

#### [1.10.9-0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.8...1.10.9-0)

> 21 March 2025

- update pause or scheduled eod [`25ac99b`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/25ac99be49586ce4498794f0e67251c9e88c6826)
- Release 1.10.9-0 [`c1c0613`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c1c061344c03e2077ec5f23702112cfa92d39d54)

#### [1.10.8](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.7...1.10.8)

> 13 March 2025

- fix: add cbusequence column in periods table [`#39`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/39)
- Release 1.10.8 [`e62988d`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/e62988d9aa210a288e82c5bacce9a9dc6245026f)

#### [1.10.7](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.6...1.10.7)

> 11 February 2025

- remove get url for rerun [`#38`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/38)
- Release 1.10.7 [`39f7553`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/39f7553b774f98515486eb6b49e8949f1eebb77d)

#### [1.10.6](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.5...1.10.6)

> 7 February 2025

- Release 1.10.6 [`6439a8d`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/6439a8def867f7f754177fbcd73589166a027e6b)
- orchestra business component update [`714900f`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/714900fa7b4cdca8963a1bc57911b513771644e6)

#### [1.10.5](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.4...1.10.5)

> 7 February 2025

- orchestra business component update [`#37`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/37)
- run refresh changes [`#36`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/36)
- Release 1.10.5 [`69691b3`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/69691b333d212c94c4baeb28a708f57d9e8f243a)

#### [1.10.4](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.3...1.10.4)

> 6 February 2025

- run refresh changes [`#35`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/35)
- Release 1.10.4 [`3cca3d8`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/3cca3d8d3b0168c4b9afbc99b8eda175ed6a2a65)

#### [1.10.3](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.2...1.10.3)

> 26 January 2025

- fix/(TCU-11077): fix clock start time badge position in UI [`#34`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/34)
- Release 1.10.3 [`25064a3`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/25064a30722f8b9e48598fa0873aff1eac5c1eff)

#### [1.10.2](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.1...1.10.2)

> 24 January 2025

- fix(TCU-11030) : update calendar components version [`#33`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/33)
- Release 1.10.2 [`f0b75fc`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/f0b75fca8061ca17f3a9be77a6e4154c29afdfda)
- update calendar components version [`408d204`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/408d20499bb0dfa61a59abaadf8d88892431e6b2)

#### [1.10.1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.10.0...1.10.1)

> 23 January 2025

- update calendar components version [`#32`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/32)
- Release 1.10.1 [`44f4393`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/44f43935f0258b2e8349e21703a1804147d80e9b)

#### [1.10.0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.9.6...1.10.0)

> 22 January 2025

- feat(TCH-32421) : add routing for new calendar components [`#31`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/31)
- add routing for new calendar components [`8bbc8c9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/8bbc8c96d72b6b889f6c2033468a33863b1fd0e3)
- address PR comments [`718faef`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/718faefbd1d2d6ecc46d3d4c82269bb4e7ea12dd)
- update breadcrumbs links [`1ccfa50`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/1ccfa50a081e161371aa9fa40e80f43ea5c775a4)

#### [1.9.6](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.9.5...1.9.6)

> 13 January 2025

- seperate base url for hercules service [`#30`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/30)
- Release 1.9.6 [`f4ba8f2`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/f4ba8f2a92e1d1e05cce4e27382ccbf3e123a4fa)

#### [1.9.5](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.9.4...1.9.5)

> 10 January 2025

- make modal footer visible [`#29`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/29)
- Release 1.9.5 [`670ce5d`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/670ce5d8a0e513a9ec34f79bc97c89cec059fe3a)

#### [1.9.4](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.9.3...1.9.4)

> 18 December 2024

- fix(TCU-9895) : add tooltip to buttons [`#28`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/28)
- Release 1.9.4 [`1703d8a`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/1703d8a92f6abcdcde6e42f940f49dd1073a6317)
- address PR comment [`849d3bd`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/849d3bd0ef5dd72f51341cc98e3fabb67b674c66)

#### [1.9.3](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.9.2...1.9.3)

> 3 December 2024

- featr(TCH-24560) - scheduled cutoff popover text changes [`#27`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/27)
- Release 1.9.3 [`c68a342`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c68a342e64e34324d6c44b04eec7351eaa02bf4c)

#### [1.9.2](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.9.1...1.9.2)

> 29 November 2024

- Release 1.9.2 [`464ae55`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/464ae558ff89c235b04a2652e8646b2b38d8933a)
- chore: updated service client [`e51c041`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/e51c04172fb837924c07efa330312975dd794ab2)

#### [1.9.1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.9.0...1.9.1)

> 28 November 2024

- chore: updated service clients [`8fe3d95`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/8fe3d954e62d5d0b0021a2ce615357634a4cd116)
- Release 1.9.1 [`b1128dc`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/b1128dc4c9366e182300f286ffbb488d9fd5cb22)
- chore: service client updated [`7398841`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/73988414ea82822cb053c9cf48dfaa317c36560c)

#### [1.9.0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.8.8...1.9.0)

> 28 November 2024

- feat(TCH-24560): Scheduled cutoff changes [`#26`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/26)
- chore: wip [`0f1cb30`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/0f1cb306bc3d658d578f56e59590157c39b340ac)
- feat(TCH-24560): scheduled cutoff changes [`bbe71c3`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/bbe71c3261cf369ba069953ecc8df0cf0aa7f57d)
- chore(TCH-24560): code refactoring [`f2a73c3`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/f2a73c36cd40d33b702115f2706def5e153d31fa)

#### [1.8.8](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.8.7...1.8.8)

> 24 November 2024

- make maker checker configurable for cutoff [`#25`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/25)
- Release 1.8.8 [`ea9a2d9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/ea9a2d989c7e30a1e71e7a915c3f9c25e7759b6e)

#### [1.8.7](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.8.6...1.8.7)

> 17 November 2024

- add payload variable hercules service url [`#24`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/24)
- Release 1.8.7 [`7aa47b1`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/7aa47b18f59b3eefce36655f2814111abb1b983c)

#### [1.8.6](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.8.5...1.8.6)

> 14 November 2024

- tch-24548 add hercules base url [`#23`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/23)
- Release 1.8.6 [`fd5056a`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/fd5056a8ed37bf205483a6462d0da8fd82286860)
- update orchestra business component [`c9799f9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c9799f9e02855a34524189b246ba327109ab917c)

#### [1.8.5](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.8.4...1.8.5)

> 24 October 2024

- Release 1.8.5 [`0a73ca9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/0a73ca9239d8a0c133b3cc6925f3c6945c3eaaac)
- make app-switcher enable by default [`0e3462c`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/0e3462c1ccf10501a974ae64c11f72a530d5274c)

#### [1.8.4](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.8.3...1.8.4)

> 24 October 2024

- fix tch-32420-header-fix [`#22`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/22)
- Release 1.8.4 [`92fb5da`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/92fb5da1d605e3415ceede6f6ae8f0e7364c3985)

#### [1.8.3](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.8.2...1.8.3)

> 24 October 2024

- fix(TCU-8639): balance explorer not loading on initial load sometimes [`#21`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/21)
- Release 1.8.3 [`879ab14`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/879ab14e13991e43b67cc6a1e3d58abad70823fd)

#### [1.8.2](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.8.1...1.8.2)

> 18 October 2024

- fix(TCU-8561): Balance explorer loading issue while swithcing between calendars [`#20`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/20)
- Release 1.8.2 [`d07e32e`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/d07e32e7aef007bdb4667834a3364e4506c35090)

#### [1.8.1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.8.0...1.8.1)

> 15 October 2024

- feat: updated ledger business component version [`#19`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/19)
- feat(TCH-30057): Balance explorer added in Dashboard [`#17`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/17)
- Release 1.7.3 [`fa8580e`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/fa8580e6a3916e8f27ecc7bd3c0388890af944b9)
- Release 1.8.1 [`ed515d5`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/ed515d5927db4ef6c6806582b6dd54592aec5bd4)
- Release 1.8.0 [`884bb27`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/884bb27f26d9d8ed0b0f59d851f6345d354cc095)

#### [1.8.0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.7.3...1.8.0)

> 14 October 2024

- Release 1.8.0 [`cff7310`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/cff731049f9c0a0385dfd7ec095e34de6c163f7e)

#### [1.7.3](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.7.2...1.7.3)

> 14 October 2024

- feat(tch-29122): partiton ledger adoption [`#18`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/18)
- Release 1.7.3 [`fa8580e`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/fa8580e6a3916e8f27ecc7bd3c0388890af944b9)
- feat: ledger & coa business component version upgrade [`1b6b1f4`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/1b6b1f48a43d05d653335814e426bcbca5553c08)
- feat: updated label [`38d7663`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/38d7663028b8809183a9d061e7b0f6292cf9bf3a)

#### [1.7.2](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.7.1...1.7.2)

> 27 September 2024

- fix(TCU-7998): Worker banner issue fix [`#16`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/16)
- Release 1.7.2 [`62278e4`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/62278e458b202bddbadb6bc7c4a01b490258f78f)

#### [1.7.1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.7.0...1.7.1)

> 24 September 2024

- Release 1.7.1 [`6040f15`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/6040f1538ffcc29ca14040fc962f3b0b9b34294a)
- chore: updated angelos version [`4cca1d6`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/4cca1d69e52a0ee18f15ce261685b192003bd88f)

#### [1.7.0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.6.3...1.7.0)

> 18 September 2024

- fix(TCH-27984): Feature gaps fixes [`#15`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/15)
- Release 1.7.0 [`8b5ec14`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/8b5ec142839ec54b89e6399f0175017df08ef95c)

#### [1.6.3](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.6.2...1.6.3)

> 16 September 2024

- Release 1.6.3 [`f8f7d42`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/f8f7d424c89b0d40e27a04af95cb7cb4f073260d)
- removed base obj value hotfix [`25deb1c`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/25deb1c8ec45cf19a0a7194fbe9878791f2372c9)

#### [1.6.2](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.6.1...1.6.2)

> 15 September 2024

- Added false check with optional chaining to fix force-allow flow [`ab8df42`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/ab8df4225eff2d2e18b9c7a5f734282352bf30e5)
- Release 1.6.2 [`4ec5faf`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/4ec5faf920ea8de80e008442856f0eec0ec9b551)

#### [1.6.1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.6.0...1.6.1)

> 11 September 2024

- Moved forceallow buttons to modal body and added event listers support [`30e4cd6`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/30e4cd69fa9d6bb5aa5ce896cdbba888c3eec493)
- Release 1.6.1 [`0a88ca4`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/0a88ca49746f7d9d7994d99149bb8c9f44953d3e)

#### [1.6.0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.5.9...1.6.0)

> 11 July 2024

- Release 1.6.0 [`d923c46`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/d923c4659c9ae80f3d5a68633ab667f8c1713074)

#### [1.5.9](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.5.8...1.5.9)

> 11 July 2024

- Added @zeta/components as it is used in main.scss, to be removed later [`#9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/9)
- Update CODEOWNERS [`#8`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/8)
- Removed @zeta/components and buefy dependecies for security fix [`#7`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/7)
- Removed @zeta/components and buefy dependecies for security fix, buefy duplicate import removed [`a7b0735`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/a7b0735cc79cee3fcfbed8911fc27c78057e4d61)
- Added @zeta/components as it is used in main.scss, to be removed it later [`2fc208e`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/2fc208e121d3af9c20c0ee1623e3a71466c63390)
- Release 1.5.9 [`8c85e77`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/8c85e77a802c92c389603ed3deb57a477065a801)

#### [1.5.8](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.5.7...1.5.8)

> 9 July 2024

- Added feature flag for dashboard balance rollup [`#6`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/6)
- Updated service-client & zwe-component dep for snyk issue [`#70`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/70)
- Release 1.5.8 [`c18959a`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c18959aa75f4a25bc69a7859cbcff972ac4f14d8)
- Replace Bitbucket SCM URL in all POM files [`12fc63d`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/12fc63d861202a8874e9ac42e0c6fd7246675a9f)
- Add CODEOWNERS file [`0f8acf7`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/0f8acf73454393a1c01878a1ec1881b303947448)

#### [1.5.7](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.5.6...1.5.7)

> 18 April 2024

- Updated the attribute for bookkeepertriggerurl and fixed the key for app header  config fallback [`#69`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/69)
- Release 1.5.7 [`6bf1a4d`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/6bf1a4df8cc8cb3588433a92ff9721da94ad3686)
- Updated the attribute for bookkeepertriggerurl [`718e43f`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/718e43f4b301ff2aab7bb1d7c1bb80d1bbad86c0)

#### [1.5.6](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.5.5...1.5.6)

> 16 April 2024

- Release 1.5.6 [`234f2ad`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/234f2adfee7ba6e33df870702422a2023509f80e)
- Moved livedashboardurl to appconfig endpoitns [`933469a`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/933469ad1f62dc8a26c28bb24d311d6418a3ca74)

#### [1.5.5](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.5.4...1.5.5)

> 16 April 2024

- Made header options based on day zero config [`#68`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/68)
- Release 1.5.5 [`5408093`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/5408093cc7f906ae502351c37b0c108b4f852dd4)

#### [1.5.4](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.5.3...1.5.4)

> 10 April 2024

- Added support for orchestra Url from day zero config [`#67`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/67)
- Release 1.5.4 [`0c98062`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/0c980623e56102976cb41c264877a893ec070bc5)

#### [1.5.3](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.5.2...1.5.3)

> 2 April 2024

- feat(TCH-25970) Updated the logo paramter in EOD center [`#66`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/66)
- Release 1.5.3 [`005da58`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/005da58ae8b58d41c48984de5302ee69bbaeb464)

#### [1.5.2](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.5.1...1.5.2)

> 27 February 2024

- Release 1.5.2 [`4f06a5a`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/4f06a5ab9d982b71ec9ea577972e559e0a440344)
- chore: updated workflow url [`12d6af3`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/12d6af337a5bb7d580297dc4844285d6582285a1)

#### [1.5.1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.5.0...1.5.1)

> 23 February 2024

- feat(TCH-23688): Ability to add task metrics added in checklist details [`#65`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/65)
- feat(TCH-16557): After the day checklist tab integration [`#64`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/64)
- Release 1.5.1 [`83c1b86`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/83c1b86b7269bba6638b761711327133c1c6ac2a)

#### [1.5.0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.13...1.5.0)

> 16 February 2024

- feat(TCH-16557): After the day checklist tab added [`16968a9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/16968a956fedff26173a0d1fff936ec89cdf9860)
- chore: removed debugger [`7383caa`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/7383caad8a7b6089577457a8021003faee1f5adf)
- Release 1.5.0 [`baac2c5`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/baac2c5116166c6dfdf53f29db63294d6c8f6424)

#### [1.4.13](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.12...1.4.13)

> 2 February 2024

- Release 1.4.13 [`0a07ba9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/0a07ba977958201e106de3cd1f9f0704c4b72008)
- fix: remove extra coa from aura internal url [`621b807`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/621b807ff8a87ad23f198bbdee4b49e0d34a8022)

#### [1.4.12](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.12-0...1.4.12)

> 24 January 2024

- feat(TCH-18994): UX enhancements [`#63`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/63)
- Release 1.4.12 [`33f3e8d`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/33f3e8d18fd57046967465ffae57edc88cd81569)

#### [1.4.12-0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.11...1.4.12-0)

> 11 September 2024

- Release 1.4.12-0 [`bd10fa6`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/bd10fa6926e72c064b3e44a11ddb2b6285984ace)
- Moved forceallow buttons to modal body and added event listers support [`520b21e`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/520b21e91b17d07d1728dcb776bb69f55e7d0089)

#### [1.4.11](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.10...1.4.11)

> 11 January 2024

- Bugfix(TCH-14638): update baseurl for checklist update for workflow [`#62`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/62)
- Release 1.4.11 [`5607cb8`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/5607cb82cbd5bfe30b1ecfb3f6828745da2f4344)

#### [1.4.10](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.9...1.4.10)

> 9 January 2024

- Release 1.4.10 [`222178a`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/222178a12819c4fc3c8626eb062f0929f1f72658)
- fix: add tasks list width and key to z-alert [`71e9cd9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/71e9cd9799e72c10ae170d85da9a5b4e56bcd3df)

#### [1.4.9](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.8...1.4.9)

> 9 January 2024

- fix: [`a667d79`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/a667d796cf99aea5b3033ffe29c38bd8964ad69f)
- Release 1.4.9 [`fc7ca31`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/fc7ca313b16dbffa11fb761cdbf8e7dab12f4d7b)
- fix: active for a-zlert not working so using v-if [`00660d9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/00660d952b2a9a279eca9c54686476e32e4fa181)

#### [1.4.8](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.7...1.4.8)

> 8 January 2024

- fix(USSM-3192) : Attributes optional check added [`4733160`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/4733160adffe68efe6e96746628e0dd67cfbd93e)
- Release 1.4.8 [`3b8df29`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/3b8df29934b33ddebd6659f2b5d34de2219509cb)

#### [1.4.7](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.6...1.4.7)

> 8 January 2024

- bugfix(TCH-21054): Multiple fixes [`#61`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/61)
- Release 1.4.7 [`ad31724`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/ad317249c348c6bde4fec0c916d5bd8626c9e4be)

#### [1.4.6](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.5...1.4.6)

> 18 December 2023

- [Do not Merge] Feature/TCH-18426 Cluster Migration Changes [`#48`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/48)
- Release 1.4.6 [`20ebdae`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/20ebdaeeb760be6583c2e57245f40f1dacb5bb39)

#### [1.4.5](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.4...1.4.5)

> 15 December 2023

- feature(TCU-1927): empty search box after clicking on the calendar [`#59`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/59)
- Release 1.4.5 [`152a69e`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/152a69ec8a6f43d7627e0ee808db168b999a8457)

#### [1.4.4](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.3...1.4.4)

> 14 December 2023

- Feature/upgrade to node18 [`#58`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/58)
- Release 1.4.4 [`63b0824`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/63b08248cd4a156884aa80c2c52cb2adff2172b5)

#### [1.4.3](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.2...1.4.3)

> 13 December 2023

- chore: package lock update [`776b0ed`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/776b0edeca3404bca5c1babd153a9be449976b3b)
- Release 1.4.3 [`e14e078`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/e14e078a4a2cbbcabf75c9c1ac624c26776be0b9)

#### [1.4.2](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.1...1.4.2)

> 13 December 2023

- chore: update package lock [`8819ccd`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/8819ccd94565b2267c35078a68f6091e96634f75)
- Release 1.4.2 [`20c64ce`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/20c64ce605156ee57559eb69cefcdb97cc4b919b)

#### [1.4.1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.4.0...1.4.1)

> 13 December 2023

- dMerged in feature/TCU-1927-add-search-cal-dropdown (pull request #56) [`a3a0206`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/a3a02062ab0bbd9a646b205e29697a3b54cd6e95)
- Release 1.4.1 [`03987c9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/03987c9db2a845b337e1b9255bb5666670c1e478)

#### [1.4.0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.3.9...1.4.0)

> 11 December 2023

- Revert pr 52 [`#55`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/55)
- feat(TCH-19023): Added coa tabs and failure alert for workers and checklist [`#52`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/52)
- Release 1.4.0 [`c47d124`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c47d124bd8077af758660674ff27be9094555040)

#### [1.3.9](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.3.8...1.3.9)

> 6 December 2023

- feat(TCH-18426): Cluster migration changes [`1ac686e`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/1ac686e4c016e988d5747b18515996f36ff259ca)
- feat(TCH-18426): Cluster migration changes [`1bec32c`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/1bec32c3cf8677526ee8278f51be1ec0c8ae42f1)
- feat(TCH-18426): Updated node version [`1085d29`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/1085d29574145394aef1fbbbfdf181ffe1a26136)

#### [1.3.8](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.3.8-release.1...1.3.8)

> 1 December 2023

- Adding the changes for DST conversion (USSM-2525) [`#54`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/54)
- fix(TCU-1991) : Run phase modal changes [`#49`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/49)
- feature(TCH-11930): added checklist null checks [`#47`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/47)
- Release 1.3.8 [`3172791`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/317279197ed0bb6ea4784393d2f847831d47f795)
- Release 1.3.6 [`bfb0ce3`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/bfb0ce3f02166b466e127a4746282fa889912fe5)
- Release 1.3.7 [`ffa9938`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/ffa9938b012ef25b495d18782f36a16cd596b001)

#### [1.3.8-release.1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.3.8-release.0...1.3.8-release.1)

> 30 November 2023

- Adding the changes for DST conversion (USSM-2525) [`#53`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/53)
- Release 1.3.8-release.1 [`c7e9e31`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c7e9e31b2a6c02e23dcc01e4b581a3f51d298135)

#### [1.3.8-release.0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.3.7...1.3.8-release.0)

> 27 November 2023

- fix(TCH-15643) : Task summary will be sent in payload now instead of defining it in RD config [`#51`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/51)
- Release 1.3.8-release.0 [`d5db2a9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/d5db2a95cd874a43601a0c6faa10aae2fd1cfbb4)
- Release 1.3.5 [`496936b`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/496936b7bace9bc03cfeef78ce7b1f486d22ff8d)

#### [1.3.7](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.3.6...1.3.7)

> 24 November 2023

- fix(TCU-1991) : Run phase modal changes [`#49`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/49)
- Release 1.3.7 [`ffa9938`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/ffa9938b012ef25b495d18782f36a16cd596b001)

#### [1.3.6](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.3.5...1.3.6)

> 8 November 2023

- feature(TCH-11930): added checklist null checks [`#47`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/47)
- Release 1.3.6 [`bfb0ce3`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/bfb0ce3f02166b466e127a4746282fa889912fe5)

#### [1.3.5](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.3.4...1.3.5)

> 31 October 2023

- Release 1.3.5 [`496936b`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/496936b7bace9bc03cfeef78ce7b1f486d22ff8d)

#### [1.3.4](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.3.3...1.3.4)

> 31 October 2023

- fix(TCU-1703) : BOFI to active phase change not working fix [`#45`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/45)
- Release 1.3.4 [`301c734`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/301c7343a584c0df2a15ef86287375562627948c)

#### [1.3.3](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.3.2...1.3.3)

> 30 October 2023

- feature(TCH-15185) phase timeline redesign changes [`#44`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/44)
- fix(TCU-1703) : BOPI and BOFI phase change will use next period id [`#43`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/43)
- Release 1.3.3 [`56bac20`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/56bac209ba0b5cca57ecce95fabac34757a50db0)

#### [1.3.2](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.3.1...1.3.2)

> 27 October 2023

- fix(TCU-1756): Overview tiles click not working fix [`#42`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/42)
- Release 1.3.2 [`c0367ca`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c0367ca3a049867502226c6525bf601429d2b343)

#### [1.3.1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.3.0...1.3.1)

> 27 October 2023

- Updated the name used for routes in calendar to constant as it was being minified [`#41`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/41)
- Release 1.3.1 [`ffbe75e`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/ffbe75ebbaebbfb56be19f03321c14066541b6f8)

#### [1.3.0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.2.1...1.3.0)

> 26 October 2023

- feat(TCH-15189) Added ledger list, coa tree and overview to coa details [`#36`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/36)
- updated package lock [`e9d1a24`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/e9d1a249d95befd29d999818618a12086da32328)
- Release 1.3.0 [`aae9c14`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/aae9c149cfba83095ac529a7ea578022d7b86db1)

#### [1.2.1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.2.0...1.2.1)

> 26 October 2023

- fix(TCU-1140) Updated failed ledger sidesheet as per the ui ux audit comments [`#22`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/22)
- bug(TCU-1133,1149,1140): UI UX Fixes [`#40`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/40)
- Release 1.2.1 [`05f06d9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/05f06d991d66abfa6f5b9114b4aae319b31d8383)

#### [1.2.0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.1.1...1.2.0)

> 26 October 2023

- feat(TCH-16537) : Manual cut-off feature [`#39`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/39)
- Release 1.2.0 [`c583b0f`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c583b0f13986a336de5480016091ada22e4ae87d)

#### [1.1.1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.1.0...1.1.1)

> 20 October 2023

- feat(TCH-15643): Sync retry issue fix [`#38`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/38)
- Release 1.1.1 [`4e805b6`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/4e805b6a9385de4183e3c5c931e987f089d5a535)

#### [1.1.0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.22...1.1.0)

> 20 October 2023

- Release 1.1.0 [`10e0250`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/10e0250e61f565ebbc2c44e3567d02c586f4bd21)

#### [1.0.22](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.21...1.0.22)

> 18 October 2023

- bugfix(TCU-1665): Temporarily reverted RD changes & Added requestedBy field [`#37`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/37)
- Release 1.0.22 [`9e91f95`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/9e91f95cb88342b06a243e65d6a884e5493c2c92)

#### [1.0.21](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.20...1.0.21)

> 17 October 2023

- Bugfix(TCU-1665) angelos and gds version update for consuming new icons [`#35`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/35)
- fix(TCH-16023) : Eod checklist workflow submit url fix [`#34`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/34)
- chore(TCH-16023) : Updated service request dependency [`#33`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/33)
- RD Migration changes : Canary to master merge [`#31`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/31)
- Release 1.0.21 [`fdb6264`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/fdb62641378ee5904be88e42ff1fb989adcce196)

#### [1.0.20](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.19...1.0.20)

> 13 October 2023

- bug(TCU-1134): Eod run modal ui Fixes [`#30`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/30)
- Release 1.0.20 [`b570ab5`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/b570ab5bc24138bfb6585aa260a7a18ba83f99bd)

#### [1.0.19](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.18...1.0.19)

> 13 October 2023

- Bugfix(TCU-1665): send proper cbu as per phase [`#29`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/29)
- Release 1.0.19 [`7a5f08c`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/7a5f08c69a3561ee0f64641644acf44032d81cf5)

#### [1.0.18](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.17...1.0.18)

> 12 October 2023

- bugfix(TCU-1665): update request payload for workflow: checklist [`#28`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/28)
- feat(ci): Add ci.Jenkinsfile for Rose CI onboarding [`#25`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/25)
- Release 1.0.18 [`8f265e8`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/8f265e8ec53feb2dbd6834021c2da6532da533f8)

#### [1.0.17](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.16...1.0.17)

> 5 October 2023

- chore: fix package-lock and update nvmrc version [`60e37cc`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/60e37cc44d8ce10aaaba6506535b583853e91d97)
- Release 1.0.17 [`f3bceb7`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/f3bceb7199448b5ae40a7547067762a735acc29d)
- chore: added nvmrc file [`06bedd6`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/06bedd6687a1654a0fcedd36338b239ba4d68f70)

#### [1.0.16](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.15...1.0.16)

> 5 October 2023

- Feature/TCH-11930 checklist integration [`#27`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/27)
- Release 1.0.16 [`4f33494`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/4f3349486e31928b0eec0221ccc4b6e2f6a87480)

#### [1.0.15](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.14...1.0.15)

> 21 September 2023

- Feature/TCH-11930 checklist integration [`#24`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/24)
- Release 1.0.15 [`3c5aa19`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/3c5aa19559ad5897c8999ffc4decb478925fb3e7)
- refactor: remove checklist map [`4303566`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/4303566c1b3d53b1371b7d756a85d4256f2a11d2)

#### [1.0.14](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.13...1.0.14)

> 20 September 2023

- feature(TCH-11930) checklist integration - workflow integration [`#23`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/23)
- Release 1.0.14 [`7f4f9df`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/7f4f9dfe3adb286016646d6375a6654d812fdd27)

#### [1.0.13](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.13-canary.2...1.0.13)

> 14 September 2023

#### [1.0.13-canary.2](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.13-canary.1...1.0.13-canary.2)

> 10 October 2023

- Release 1.0.13-canary.2 [`efa4b99`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/efa4b99d23ea20d5849ca609c176adb77f720910)
- fix(TCH-16023) : Bug fixes [`fcdfc01`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/fcdfc01db20c776f64ec02729c42b4effdb4e285)

#### [1.0.13-canary.1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.13-canary.0...1.0.13-canary.1)

> 5 October 2023

- feat(TCH-16023) : checklist RD changes [`293970e`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/293970eb59ef52f3fa37d14ceb2aa51c4a71e917)
- Release 1.0.13-canary.1 [`2645238`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/264523859ea899baf36503613a6003bc9d8f288d)

#### [1.0.13-canary.0](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.12...1.0.13-canary.0)

> 28 September 2023

- feat(TCH-16023) : RD Migration change [`#26`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/26)
- feature(TCH-11930) checklist integration including detail view [`#19`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/19)
- Release 1.0.13-canary.0 [`85de10d`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/85de10de0871f2a33701532eef39b3134892d734)
- Release 1.0.13 [`710b266`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/710b2663e4554b00fe05e58f9e247c0bd60146f9)

#### [1.0.12](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.11...1.0.12)

> 14 September 2023

- TCU-1146 Ui / ux enchacements [`#20`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/20)
- Feat/TCH-15189 calendar sub routes [`#15`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/15)
- Release 1.0.12 [`f21dfd5`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/f21dfd5ce51f1ce45dc7b97a21fccf8186149530)

#### [1.0.11](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.10...1.0.11)

> 13 September 2023

- feat(TCU-1150) Updated orchestra observability version for ui enhancements [`#18`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/18)
- Release 1.0.11 [`9ec451c`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/9ec451c70bb720f2c319c6fcdd7aff8d4474b7fa)

#### [1.0.10](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.9...1.0.10)

> 13 September 2023

- Feat/tcu 797 revert orchestra version [`#17`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/17)
- feat(TCU-797) : start phase run and calendar menu disabled [`#16`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/16)
- Release 1.0.10 [`6240e42`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/6240e429a896f8af872ef3e2418a5240067ae5f7)

#### [1.0.9](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.8...1.0.9)

> 11 September 2023

- feat(TCH-15189) Added feature flag for calendar menu in header [`#14`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/14)
- feat(TCH-15189)Added calendar option in header [`#13`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/13)
- Added the calendar menu with overview page [`#12`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/12)
- Release 1.0.9 [`8f07a25`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/8f07a2573004a90c2d70ad630485c6f8bb3f2882)

#### [1.0.8](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.7...1.0.8)

> 1 September 2023

- fix(TCU-797) : Clear the selected highlighted item after closing the actions dropdown [`#10`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/10)
- Release 1.0.8 [`a56b366`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/a56b366c39df4cba7d60e548437b1a6fe2ff6bd4)

#### [1.0.7](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.6...1.0.7)

> 31 August 2023

- feat(TCH-15186): EoD Run Modal enhancements [`#9`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/9)
- Release 1.0.7 [`5c0ab55`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/5c0ab55e8927328d3c92f59d63ae6279c50c0289)

#### [1.0.6](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.5...1.0.6)

> 28 August 2023

- feat(TCH-15184): add Start Phase Run action [`#8`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/8)
- Release 1.0.6 [`d920134`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/d9201346157b1209163c712e7c39a35a30620a35)

#### [1.0.5](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.5-feature-tcu-797-actions-fix...1.0.5)

> 24 August 2023

- bugfix(TCT-9450): fix periods list incorrect status issue [`#7`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/7)
- Release 1.0.5 [`36e90cf`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/36e90cf21dc489927af47986ca1ef3d4a2762c0f)

#### [1.0.5-feature-tcu-797-actions-fix](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.4...1.0.5-feature-tcu-797-actions-fix)

> 12 September 2023

- Release 1.0.5-feature-tcu-797-actions-fix [`a0f950c`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/a0f950c244d7a9f9a27318e258498bbcaaa72de4)
- feat(TCU-797) : Actions dropdown fix [`f637dc7`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/f637dc7ee578dcd2b88d565eed63153853f1d86b)

#### [1.0.4](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.4-3...1.0.4)

> 23 August 2023

#### [1.0.4-3](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.4-2...1.0.4-3)

> 21 December 2023

- fix(USSM-3192) : Attributes optional check added [`ecad454`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/ecad4548539630009c427e9d04c384b659901fe6)
- Release 1.0.4-3 [`8d0a642`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/8d0a642e2d06e58fbe267994489301e3a460c842)
- fix(USSM-3192) : Attributes optional check added [`63b1c01`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/63b1c01e54464503ff931d2681b5cd6c21b187ac)

#### [1.0.4-2](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.4-1...1.0.4-2)

> 18 December 2023

- cluster migration changes [`7f3665a`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/7f3665a04b9f6d00c537b2798c5a918b75f869c9)
- node-sass change [`c269f1c`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/c269f1c95d6515207aa189d299694c94ce78edb2)
- Rd changes [`4de0df3`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/4de0df3988128663e5d5603a4b16e3ad0a3ef690)

#### [1.0.4-1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.3...1.0.4-1)

> 7 November 2023

- bug(TCU_547): Fixed Extra space workers table [`#6`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/6)
- Release 1.0.4-1 [`5db378e`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/5db378e19cc23c552941f0207093475673b74549)
- Changed the timezone conversion logic for fetching periods [`5a0dea8`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/5a0dea8e6d8f7720f45a2c858a8d3946eba72b28)
- Release 1.0.4 [`2c8d695`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/2c8d695911d57e4a36a042952dc8fd8586505615)

#### [1.0.3](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.2...1.0.3)

> 23 August 2023

- bug(TCU-547,TCP-2330): Removed Menu Items and Extra Space from worker table [`#5`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/5)
- Release 1.0.3 [`1504d65`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/1504d65e9162d4d1a73ac92d2f10ff4016acbc91)

#### [1.0.2](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.1...1.0.2)

> 22 August 2023

- other: reverted changes for phase timeline in orchestra component [`#3`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/3)
- added changelog [`b3f1de5`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/b3f1de5f9e27f77045fa32cc1b5121365c1ee560)
- Release 1.0.2 [`20da2da`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/20da2da666cd0f4984a652e84c1ef4a473265d45)

#### [1.0.1](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/compare/1.0.1-canary.0...1.0.1)

> 22 August 2023

#### 1.0.1-canary.0

> 22 August 2023

- chore: updated app cli version to enable release [`#2`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/2)
- other: reverted changes for TCU-612, TCT-9684 [`#1`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/pull/1)
- Intial EOD Base [`f4f1277`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/f4f12777e3c753178a782a6f1bb7df45b55c7174)
- Dummy commit to test app-cli version and also to upgrade to node 19 version [`651de86`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/651de868a9e7c00a45033d9a77add8888ba5bc68)
- Modified Config [`cdcb8b8`](https://github.com/Zeta-Enterprise/hercules-aph-eod-center/commit/cdcb8b8008a300e97e84d5cdae18b2d41c17825c)
