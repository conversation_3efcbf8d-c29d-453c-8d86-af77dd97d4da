{"name": "hercules-aph-eod-center", "description": "Application within Aphrodite Suite to view/manage accounting entities.", "version": "1.12.0", "private": true, "prettier": "@zeta/prettier-config", "scripts": {"serve": "hercules-app serve", "build": "hercules-app build", "release": "hercules-app release", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "lint": "vue-cli-service lint", "lint:fix": "vue-cli-service lint --fix", "build:icons": "node build.fonts.js", "i18n:report": "vue-cli-service i18n:report --src './src/**/*.?(js|vue)' --locales './src/locales/**/*.json'", "update:@zeta": "./node_modules/.bin/ncu '/^@zeta.*$/' -u && npm i", "store:generate": "node store-schema.js && npm run lint:fix"}, "dependencies": {"@hercules/component-core": "^1.3.0", "@hercules/context": "1.7.2", "@hercules/vuex-plugin": "0.2.0", "@zeta-business/orchestra-common": "1.1.3", "@zeta/components": "^1.21.0-beta.0", "@zeta/service-clients": "^1.76.11", "@zeta/utils": "^1.0.3", "bulma": "^0.8.2", "core-js": "^3.20.3", "lodash": "^4.17.21", "vue": "~2.6.14", "vue-class-component": "^7.2.4", "vue-i18n": "^8.18.2", "vue-json-tree-view": "^2.1.6", "vue-property-decorator": "^9.1.2", "vue-router": "^3.5.1", "vuex": "^3.6.2", "vuex-class": "^0.3.2"}, "devDependencies": {"@hercules/app-cli": "^2.5.1", "@types/jest": "^27.4.1", "@typescript-eslint/eslint-plugin": "^5.12.1", "@typescript-eslint/parser": "^5.12.1", "@vue/cli-plugin-babel": "~5.0.3", "@vue/cli-plugin-e2e-cypress": "~5.0.3", "@vue/cli-plugin-eslint": "~5.0.3", "@vue/cli-plugin-router": "~5.0.3", "@vue/cli-plugin-typescript": "~5.0.3", "@vue/cli-plugin-unit-jest": "~5.0.3", "@vue/cli-plugin-vuex": "~5.0.3", "@vue/cli-service": "~5.0.3", "@vue/eslint-config-typescript": "^9.1.0", "@vue/test-utils": "^1.3.0", "@vue/vue2-jest": "^27.0.0-alpha.4", "@zeta/prettier-config": "^1.0.0", "babel-jest": "^27.5.1", "cliui": "^7.0.4", "cypress": "^8.7.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.4.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.5.0", "jest": "^27.5.1", "lint-staged": "^11.2.6", "node-sass": "^8.0.0", "prettier": "^2.6.2", "sass": "^1.66.1", "sass-loader": "^12.6.0", "ts-jest": "^27.1.3", "typescript": "~4.5.5", "vue-cli-plugin-i18n": "~2.3.1", "vue-template-compiler": "~2.6.14"}, "federatedDependencies": {"@zeta-design/galaxy": "0.14.10", "@zeta-business/aphrodite-core": "3.8.4", "@zeta-business/calendar-management": "4.5.34-canary.0", "@zeta-business/ledger": "2.17.1-fc.0", "@zeta-business/service-requests": "0.8.5", "@zeta-business/transaction-policies": "3.0.2-canary.0", "@zeta-business/coa-management": "0.18.4", "@zeta-business/chart-of-accounts": "2.4.0-fc.0", "@zeta-business/orchestra-observability": "0.14.0", "@zeta-business/calendar-components": "0.2.11", "@zeta-business/worker-list": "*"}}