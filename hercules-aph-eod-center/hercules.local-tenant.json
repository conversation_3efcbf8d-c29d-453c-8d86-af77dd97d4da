{"name": "eodc_hercules_rbl", "hostname": "rbl-eodc0hercules.mum1-pp.zetaapps.in", "appCode": "hercules-aph-eod-center", "appVersion": "1.0.1-master.83970f1e.0", "tenantId": "600309", "sandboxId": "60062354", "attributes": {"tenantCode": "lsg", "system": {"aphShell": {"appHeaderName": "EOD Center", "showAppSwitcher": false, "fromCollection": false, "sandboxId": 60062354, "theme": "light"}}, "appConfig": {"endpoints": {"orchestraUrl": "https://orchestra.internal.mum1-pp.zetaapps.in/orchestra-v3", "bookkeeperTriggerUrl": "https://orchestra.internal.mum1-pp.zetaapps.in", "liveDashboardUrl": "https://backrubv2-bvc0hercules.mum1-pp.zetaapps.in/overview"}}, "viewConfig": {"dashboard": {"autoRefresh": true, "autoRefreshInterval": 60000, "enablePreferenceCache": false, "dbName": "eod", "filters": {"enablePhaseRun": true, "enableManualCutoff": true, "enableScheduleCutoff": true, "enableScheduleEOD": true, "enableRealTimeWorkers": true, "enablePauseResumeCutoff": true, "showCutoff": true}, "tabs": {"showChecklist": true, "showATDChecklist": true}, "balanceExplorer": {"show": true}}, "header": {"tenantLogoUrl": "logos/rbl.svg", "menuItems": {"dashboard": {"show": true, "label": "Home"}, "calendars": {"show": true, "label": "Calendars"}, "requests": {"show": false, "label": "Requests"}, "livedashboard": {"show": true, "label": "Live Dashboard"}}}, "modules": {"calendar": {"displayGrafanaDashboard": true, "displayGrafanaClearingStatusDashboard": true, "grafanaDashboardParams": {"refreshInterval": "1m", "orchestraPgDataSource": "aura_orchestra_pg_datasource"}, "periods": {"defaultFilters": {"periodRange": "END"}, "enableDefaultFilters": true, "enableCache": true, "cacheEntityName": "allPeriods"}, "workBenchId": "117813", "catalogId": "198308", "rdCode": "RQDINZZ00100", "requestHistoryEnablePolling": false, "enableRerunData": true, "enableRD": true, "makerCheckerRequiredMap": {"startEoD": true, "cutoff": false, "startPhase": true, "automatedChecklist": false, "manualChecklist": false, "createCutoffSchedule": false, "editCutoffSchedule": false, "deleteCutoffSchedule": false, "pauseCutoffSchedule": false, "resumeCutoffSchedule": false}, "rdMap": {"startEoD": "RQDINZZ00100", "skipLedgers": "RQDINZZ00101", "checklist": "RQDINZZ00102", "cutoff": "RQDINZZ00103", "createCutoffSchedule": "RQDINZZ0226", "editCutoffSchedule": "RQDINZZ0227", "deleteCutoffSchedule": "RQDINZZ0228", "pauseCutoffSchedule": "RQDINZZ0229", "resumeCutoffSchedule": "RQDINZZ0230", "scheduledEOD": "RQDINZZ0273"}}}}}, "plugins": {"oauth": {"realm": "rbl-admin.in", "scope": "admin", "sandboxId": "60062354", "tenantId": "600309", "token": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "assets": {"commonAssetsVersion": "3.0.144", "cdnZoneName": "aws-default-pp-mumbai", "localFederatedDependencies": ["@zeta-business/worker-list"]}, "security": {"csp": {"default": ["'self'"], "script-src": ["https://apis.google.com", "https://cdn.materialdesignicons.com", "https://cdn.jsdelivr.net", "'unsafe-eval'"], "font-src": ["data/font/", "https://fonts.gstatic.com/"], "img-src": ["data: w3.org/svg/2000", "data:"]}}, "product": {"aphrodite": {"logo": "https://hercules-assets.mum1-pp.zetaapps.in/common-assets/3.0.101/logos/zwe-aph-finance-center-dark.svg", "type": "core", "description": "Provides all financial information for a customer", "displayName": "EOD Center"}}}}