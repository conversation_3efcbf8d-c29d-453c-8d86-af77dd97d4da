import { getCommonAssetUrl } from '@hercules/context';
import { DateTimeFormatOptions, DateTimeFormats } from 'vue-i18n';

export const MODULES_PATH = {
    HOME: '/',
    DASHBOARD: 'dashboard',
    CALENDAR: '/calendars',
    COA: 'chart-of-accounts',
    CALENDAR_MANAGEMENT: '/calendar-management',
    SKIP_LEDGERS_FORM: 'dashboard/skip-ledgers-form',
    REQUESTS: '/requests',
    OVERVIEW: 'overview',
    BALANCES: 'balances',
    INTERNAL_LEDGERS: 'internal-ledgers',
    ACCOUNT_HOLDER_LEDGERS: 'account-holder-ledgers',
    WORKERS: 'workers',
    NODES: 'nodes',
    LEDGERS: 'ledgers',
};

export const LEDGER_TYPES = {
    system: 'SYSTEM',
    accountholder: 'ACCOUNT_HOLDER',
};
export const LEDGER_TYPES_LABEL = {
    internal: 'internalLedgers',
    accountholder: 'accountHolderLedgers',
};
export const LOCALES = {
    INDIA_EN: 'en-IN',
    INDIA_HI: 'hi-IN',
    PHILIPPINES_EN: 'en-PH',
    US_EN: 'en-US',
    UK_EN: 'en-UK',
    PHILIPPINES_FIL: 'fil-PH',
    ITALY_IT: 'it-IT',
    SPAIN_ES: 'es-ES',
    BRAZIL_PR: 'pt-BR',
    VIETNAM_VI: 'vi-VN',
    FRANCE_FR: 'fr-FR',
};

export const NUMBER_FORMATS = {
    [LOCALES.INDIA_EN]: {
        currency: {
            style: 'currency',
            currency: 'INR',
            currencyDisplay: 'symbol',
        },
    },
    [LOCALES.INDIA_HI]: {
        currency: {
            style: 'currency',
            currency: 'INR',
            currencyDisplay: 'symbol',
        },
    },
    [LOCALES.PHILIPPINES_EN]: {
        currency: {
            style: 'currency',
            currency: 'PHP',
            currencyDisplay: 'symbol',
        },
    },
};

const longDateTimeFormat: DateTimeFormatOptions = {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    second: 'numeric',
};
export const DATETIME_FORMATS: DateTimeFormats = {
    [LOCALES.INDIA_EN]: {
        short: {
            month: 'short',
            year: 'numeric',
            day: 'numeric',
        },
        long: {
            ...longDateTimeFormat,
            hour12: false,
        },
        long12h: {
            ...longDateTimeFormat,
            hour12: true,
        },
    },
    [LOCALES.US_EN]: {
        short: {
            month: 'short',
            year: 'numeric',
            day: 'numeric',
        },
        long: {
            ...longDateTimeFormat,
            hour12: false,
        },
        long12h: {
            ...longDateTimeFormat,
            hour12: true,
        },
    },
    [LOCALES.UK_EN]: {
        short: {
            month: 'short',
            year: 'numeric',
            day: 'numeric',
        },
        long: {
            ...longDateTimeFormat,
            hour12: false,
        },
        long12h: {
            ...longDateTimeFormat,
            hour12: true,
        },
    },
    [LOCALES.PHILIPPINES_EN]: {
        short: {
            month: 'short',
            year: 'numeric',
            day: 'numeric',
        },
        long: {
            ...longDateTimeFormat,
            hour12: false,
        },
        long12h: {
            ...longDateTimeFormat,
            hour12: true,
        },
    },
    [LOCALES.PHILIPPINES_FIL]: {
        short: {
            month: 'short',
            year: 'numeric',
            day: 'numeric',
        },
        long: {
            ...longDateTimeFormat,
            hour12: false,
        },
        long12h: {
            ...longDateTimeFormat,
            hour12: true,
        },
    },
    [LOCALES.ITALY_IT]: {
        short: {
            month: 'short',
            year: 'numeric',
            day: 'numeric',
        },
        long: {
            ...longDateTimeFormat,
            hour12: false,
        },
        long12h: {
            ...longDateTimeFormat,
            hour12: true,
        },
    },
    [LOCALES.SPAIN_ES]: {
        short: {
            month: 'short',
            year: 'numeric',
            day: 'numeric',
        },
        long: {
            ...longDateTimeFormat,
            hour12: false,
        },
        long12h: {
            ...longDateTimeFormat,
            hour12: true,
        },
    },
    [LOCALES.BRAZIL_PR]: {
        short: {
            month: 'short',
            year: 'numeric',
            day: 'numeric',
        },
        long: {
            ...longDateTimeFormat,
            hour12: false,
        },
        long12h: {
            ...longDateTimeFormat,
            hour12: true,
        },
    },
    [LOCALES.VIETNAM_VI]: {
        short: {
            month: 'short',
            year: 'numeric',
            day: 'numeric',
        },
        long: {
            ...longDateTimeFormat,
            hour12: false,
        },
        long12h: {
            ...longDateTimeFormat,
            hour12: true,
        },
    },
    [LOCALES.FRANCE_FR]: {
        short: {
            month: 'short',
            year: 'numeric',
            day: 'numeric',
        },
        long: {
            ...longDateTimeFormat,
            hour12: false,
        },
        long12h: {
            ...longDateTimeFormat,
            hour12: true,
        },
    },
};

export const clusterSpec = 'eod-center-webapp';
export const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
export const API_TEXT = () => '/api/v1/ifi/ifiID';
export const errorStateUrl = getCommonAssetUrl('images/error-states/generic-error.svg');
export const emptyStateUrl = getCommonAssetUrl('images/error-states/no-data.png');
export const sessionExpiredUrl = getCommonAssetUrl('images/error-states/session-expired.svg');
export const emptyStateDashboardUrl = getCommonAssetUrl('images/states-new/empty-folder1.svg');
export const errorStateNotFoundUrl = getCommonAssetUrl('images/states-new/error-page-not-found.svg');

export const LIST_TILE_OFFSET_THRESHOLD = 80;
export const DEFAULT_TIMEZONE = 'Asia/Kolkata';
export enum CALENDAR_MODE {
    WEEKLY = 'weekly',
    MONTHLY = 'monthly',
}
export const DEFAULT_BOOK_DATE_FORMAT = 'DD MMM YYYY';
export const CALENDAR_DATE_FORMAT = 'MMMM D, YYYY';
export const COA_SUMMARY_DATE_FORMAT = 'YYYY-MM-DD';
export const DATE_FORMAT_YYYY_MM_DD = 'YYYY-MM-DD';
export const DATE_FORMAT_WITH_TIMEZONE = 'YYYY-MM-DDT[00:00]Z';
export const CLOCK_START_TIME_FORMAT = 'D MMM YYYY, h:mm:ss a';
export const STATUS_TYPES = {
    SUCCESSFUL: 'Successful',
    FAILED: 'Failed',
    IN_PROGRESS: 'In Progress',
};

export const DASHBOARD_STATUS_TYPES = {
    SUCCESS: 'Successful',
    FAILED: 'Failed',
    PROGRESS: 'In Progress',
};

export const cbuStatusTriggerMap: {
    [key: string]: { currentTrigger: string | null; nextTrigger: string; nextPhase: string };
} = {
    INACTIVE: {
        currentTrigger: null,
        nextTrigger: 'initiateBOP',
        nextPhase: 'BOPI',
    },
    BOPI: {
        currentTrigger: 'initiateBOP',
        nextTrigger: 'initiateBOF',
        nextPhase: 'BOFI',
    },
    BOFI: {
        currentTrigger: 'initiateBOF',
        nextTrigger: 'activate',
        nextPhase: 'ACTIVE',
    },
    ACTIVE: {
        currentTrigger: 'activate',
        nextTrigger: 'initiateEOP',
        nextPhase: 'EOPI',
    },
    EOPI: {
        currentTrigger: 'initiateEOP',
        nextTrigger: 'initiateEOF',
        nextPhase: 'EOFI',
    },
    EOFI: {
        currentTrigger: 'initiateEOF',
        nextTrigger: 'close',
        nextPhase: 'CLOSED',
    },
    CLOSED: {
        currentTrigger: 'close',
        nextTrigger: 'initiateBOP', //For next period
        nextPhase: 'BOPI',
    },
};

export const STATUS = {
    SUCCEEDED: 'SUCCEEDED',
    FAILED: 'FAILED',
    FAILED_RETRIED: 'FAILED_RETRIED',
    COMPLETED: 'COMPLETED',
    SUCCESS: 'SUCCESS',
    PROGRESS: 'PROGRESS',
    'In PROGRESS': 'PROGRESS',
    SUCCESSFUL: 'SUCCESS',
};

export const ACTIVE_PHASE = 'ACTIVE';
export const PHASES_START = ['ACTIVE', 'EOPI', 'EOFI', 'CLOSED'];
export const PHASES_END = ['BOPI', 'BOFI'];
export const WORKER_START_PHASES = ['ACTIVE', 'EOPI', 'EOFI', 'EOP'];
export const GOD_TENANT_ID = 0;
export const INACTIVE = 'INACTIVE';
export const HEADER_MENU_IDS: {
    [k: string]: string;
} = {
    DASHBOARD: 'dashboard',
    CALENDARS: 'calendars',
    REQUESTS: 'requests',
    LIVEDASHBOARD: 'livedashboard',
};

export const DEFAULT_CURRENCY = 'INR';
