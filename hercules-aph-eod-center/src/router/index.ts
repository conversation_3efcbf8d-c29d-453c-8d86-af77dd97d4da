import NotFound from '@/views/NotFound.vue';
import CalendarRoutes from '@/views/modules/calendar/routes';
import DashboardRoutes from '@/views/modules/dashboard/routes';
import RequestsRoutes from '@/views/modules/requests/routes';
import { getRouterBasePath } from '@hercules/context';
import Vue from 'vue';
import Router from 'vue-router';

Vue.use(Router);

const router = new Router({
    routes: [
        ...DashboardRoutes,
        ...RequestsRoutes,
        {
            path: '*',
            name: NotFound.name,
            component: NotFound,
        },
        ...CalendarRoutes,
    ],
    base: getRouterBasePath(),
    mode: 'history',
});

export default router;
