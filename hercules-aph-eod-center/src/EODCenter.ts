/* eslint-disable @typescript-eslint/no-explicit-any */
import { Component } from 'vue-property-decorator';
import Vue from 'vue';
import { getURLParameter } from '@zeta/utils';
import { context } from '@hercules/context';
import { LOCALES } from './common/constants';
import { ROOT_STATE_CONST, SET_STATE_DATA, GET_STATE_DATA, StatePayload, RootState } from './store/types';
import { Action, Getter } from 'vuex-class';
const GLOBAL: any = window;
import { IndexedDBService } from '@zeta-business/orchestra-common';
import { Calendar, UserPreferences, UserProfile } from './types';
import { DEFAULT_DB_PARAMS } from './views/modules/dashboard/constants';
import { pauseResumeCutoffContext, manualCutoffContext, showCutoffContext} from "./views/modules/dashboard/utils";

// eslint-disable-next-line
@Component({})
export default class AuraFinanceCenter extends Vue {
    protected dbService = new IndexedDBService(this.dbName as string);
    @Action(SET_STATE_DATA)
    public setStateData!: (_: StatePayload) => Promise<void>;
    @Getter(GET_STATE_DATA)
    // eslint-disable-next-line
    public getStateData!: (_: keyof RootState) => any;
    protected goBack() {
        this.$router.back();
    }

    protected get dbName() {
        return this.dashboardConfig?.dbName || DEFAULT_DB_PARAMS.NAME;
    }

    protected get $ifiId() {
        const ifiId = context?.tenantId || getURLParameter('ifiId');
        this.setStateData({
            key: ROOT_STATE_CONST.TENANT_ID,
            value: ifiId,
        });
        return ifiId;
    }

    protected get $locale() {
        return context?.getAttribute('locale') || getURLParameter('locale');
    }

    protected get $modulePath() {
        const [module] = this.$route.fullPath.split('/').filter((item) => !!item);
        return `/${module}`;
    }

    protected get omsBaseUrl() {
        return context?.getAttribute('OMS_BASIC');
    }
    protected get herculesBaseUrl() {
        return context?.getAttribute('herculesServiceBaseUrl');
    }
    protected get herculesServiceClientBaseUrl() {
        return context?.getAttribute('herculesServiceClientBaseUrl');
    }
    protected get auraAppBaseUrl() {
        return context?.getAttribute('auraAppBaseUrl');
    }
    protected get enableEodBulkProcessing() {
        return context?.getAttribute('enableEodBulkProcessing')|| false;
    }
    protected get bookkeeperUrl() {
        return context?.getAttribute('BOOKKEEPER_URL');
    }
    protected get bookkeeperTriggerUrl() {
        // First look in the day zero config for bookeeper trigger url
        return (
            context?.getAttribute('appConfig.endpoints.bookkeeperTriggerUrl') ||
            context?.getAttribute('BOOKKEEPER_TRIGGER_URL')
        );
    }
    protected get ledgerManagerUrl() {
        return context?.getAttribute('LEDGER_MANAGER_URL');
    }
    protected get angelosBaseUrl() {
        return context?.getAttribute('ANGELOS_URL');
    }
    protected get atlantaUrl() {
        return context?.getAttribute('ATLANTA_URL');
    }
    protected get atlantaOrchestraUrl() {
        return context?.getAttribute('ATLANTA_ORCHESTRA_URL');
    }
    protected get grafanaUrl() {
        return context?.getAttribute('GRAFANA_URL');
    }
    protected get grafanaClearingStatusUrl() {
        return context?.getAttribute('GRAFANA_CLEARING_STATUS_URL');
    }
    protected get auraUrl() {
        return context?.getAttribute('AURA_URL');
    }
    protected get auraInternalUrl() {
        return context?.getAttribute('AURA_INTERNAL_URL');
    }
    protected get workflowUrl() {
        return context?.getAttribute('WORKFLOW_URL');
    }
    protected get orchestraUrl() {
        return context?.getAttribute('ORCHESTRA_URL');
    }
    protected get rheaRequestUrl() {
        return context?.getAttribute('RHEA_REQUEST_URL');
    }
    protected get omsUrl() {
        return context?.getAttribute('OMS_URL');
    }
    protected get lockerServiceBaseUrl() {
        return context?.getAttribute('LOCKER_SERVICE_URL');
    }
    protected get rheaBaseUrl() {
        return context?.getAttribute('RHEA_BASE_URL');
    }
    protected get tachyonUrl() {
        return context?.getAttribute('TACHYON_URL');
    }
    protected get auraAthenaManagerUrl() {
        return context?.getAttribute('AURA_ATHENA_MANAGER_URL');
    }
    protected get tachyonCalendarUrl() {
        return context?.getAttribute('TACHYON_CALENDAR_URL');
    }
    protected get locale() {
        return context?.getAttribute('locale') || LOCALES.INDIA_EN;
    }
    protected get userAuthProfile(): UserProfile {
        return this.getStateData(ROOT_STATE_CONST.USER_PROFILE);
    }

    protected get tenantCountryCode() {
        return (
            GLOBAL.__HERCULES__ &&
            GLOBAL.__HERCULES__.$tenant &&
            GLOBAL.__HERCULES__.$tenant.attributes &&
            GLOBAL.__HERCULES__.$tenant.attributes.tenantCountryCode
        );
    }
    protected get workBenchId() {
        return context?.getAttribute('viewConfig.modules.calendar.workBenchId');
    }
    protected get rdMap() {
        return context?.getAttribute('viewConfig.modules.calendar.rdMap');
    }
    protected get catalogId() {
        return context?.getAttribute('viewConfig.modules.calendar.catalogId');
    }
    protected get enableRequestHistoryPolling() {
        return context?.getAttribute('viewConfig.modules.calendar.requestHistoryEnablePolling');
    }
    protected get dashboardConfig() {
        return context?.getAttribute('viewConfig.dashboard');
    }
    protected get enableRD() {
        return context?.getAttribute('viewConfig.modules.calendar.enableRD');
    }
    protected get headerConfig() {
        return context?.getAttribute('viewConfig.header');
    }
    protected get appConfig() {
        return context?.getAttribute('appConfig');
    }
    protected get allCalendars(): Calendar[] {
        return this.getStateData(ROOT_STATE_CONST.ALL_CALENDARS);
    }
    protected get enableScheduleCutoff() {
        return context?.getAttribute('viewConfig.dashboard.filters.enableScheduleCutoff') || false;
    }
    protected get enablePauseResumeCutoff() {
        return pauseResumeCutoffContext();
    }
    protected get enableManualCutoff() {
        return manualCutoffContext()
    }
    protected get showCutoff() {
        return showCutoffContext()
    }
    protected get enableScheduleEOD() {
        return context?.getAttribute('viewConfig.dashboard.filters.enableScheduleEOD') || false;
    }
    protected get enableRealTimeWorkers() {
        return context?.getAttribute('viewConfig.dashboard.filters.enableRealTimeWorkers') || false;
    }
    protected get tenantCode() {
        return context?.getAttribute('tenantCode') || '';
    }

    /**
     * Function to insert preferences in database if config allows
     * @param userPreferences object containing dashboard related preferences
     * excluding auth profile and tenant id
     */
    public async cachePreferences(userPreferences: UserPreferences = {}) {
        if (this.dashboardConfig?.enablePreferenceCache) {
            const value: UserPreferences = {
                authProfile: this.userAuthProfile.authProfileId,
                tenantId: this.$ifiId as string,
                ...userPreferences,
            };
            // Set db entity
            try {
                await this.dbService.updateEntity({
                    key: DEFAULT_DB_PARAMS.ENTITIES.USER_PREFERENCES,
                    value,
                });
            } catch (error) {
                this.dbService.deleteEntity(DEFAULT_DB_PARAMS.ENTITIES.USER_PREFERENCES).finally(async () => {
                    await this.dbService.insertEntity({
                        key: DEFAULT_DB_PARAMS.ENTITIES.USER_PREFERENCES,
                        value,
                    });
                });
            }
        }
    }

    /**
     * Function to fetch a particular value from preferences entity
     * @param key key of the value to be fetched
     */
    public async fetchPreferencesFromCache(key: keyof UserPreferences) {
        let entity;
        if (this.dashboardConfig?.enablePreferenceCache) {
            try {
                const preferencesEntity = await this.dbService.fetchEntity(DEFAULT_DB_PARAMS.ENTITIES.USER_PREFERENCES);
                if (preferencesEntity) {
                    const userPreferencesValue: UserPreferences = preferencesEntity?.value;
                    // if auth profile and tenant id match
                    if (
                        userPreferencesValue.authProfile === this.userAuthProfile?.authProfileId &&
                        userPreferencesValue.tenantId === this.$ifiId &&
                        key in userPreferencesValue
                    ) {
                        entity = userPreferencesValue[key];
                    } else {
                        throw new Error(
                            `Either auth profile or tenant-id does not match OR entity: ${key} is not present in user preferences`,
                        );
                    }
                }
            } catch (error) {
                console.log(error);
            }
            return entity;
        }
    }

    /**
     * Function to delete database
     */
    public deleteDatabase() {
        // delete periods entities
        this.dbService
            .deleteEntity(DEFAULT_DB_PARAMS.ENTITIES.ALLPERIODS)
            .catch((err) => console.error('Error while deleting periods entity', err));
    }

    // Delete db if created on component destruction
    public async destroyed() {
        this.deleteDatabase();
    }
}
