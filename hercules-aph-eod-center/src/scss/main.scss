
@import '../assets/font-icons/z-icon.css';
// TODO: Remove @zeta/components dependecy
@import '~@zeta/components/dist/index.css';
@import '~@zeta/components/dist/base.css';

:root,
:host {
    --primary: #4545e9;
    --text-neutral-1: #020d4d;
    --globalheader-z-index: 10001;
    --error-codes-height: 100vh;
    --error-codes-height-offset: 326px;
    --details-page-max-width: 1200px;
    --page-height-with-header: calc(100vh - 48px);
    --card-content-width: 300px;
    --card-content-height: 100vh;
    --attachments-height: 100vh;
    --attachments-height-offset: 105px;
    --bottomsheet-height-with-header: calc(100vh - 108px);
    --bottomsheet-height-without-header: calc(100vh - 58px);
    --phase-cycle-widget-height: 208px;
    --coa-summary-widget-height: 208px;
    --worker-summary-widget-height: 490px;
    --failed-ledger-table-calc-height: 300px;
    --all-periods-table-height: 256px;
    --coa-balance-summary-height: 100vh;
    --coa-balance-summary-height-offset: 510px;
    --ledger-list-height: 100vh;
    --ledger-list-table-height-offset: 230px;
    --ledger-list-height-offset: 105px;
    --ledger-view-height: calc(100vh - 145px);
}
html,
body {
    overflow: hidden;
}
html,
body,
.app-container {
    min-height: 100%;
    --bottom-sheet-background: #e9e9e9;
    //table sticky

    .z-component.z-data-grid .z-table table {
        position: sticky;
        thead tr th {
            position: sticky;
            top: 0;
        }
    }
    .z-tile:hover {
        box-shadow: 0px -2px 8px rgba(51, 51, 51, 0.05), 0px 4px 12px rgba(51, 51, 51, 0.1);
    }
}

.z-icon-node {
    color: #64cd6d;

    & ~ .mdi {
        display: none;
    }
}

.app-container .app-page-header .title {
    font-weight: 500;
    margin: 0;
}

.page-content .section-heading {
    font-weight: 400;
}

.app-page-header.paddingless.is-attached {
    padding-top: 20px;
    padding-left: 32px;
    padding-bottom: 30px;
    padding-right: 32px;
    margin-bottom: -10px !important;
}

.b-table {
    .table {
        table-layout: fixed;

        td,
        th {
            vertical-align: middle;
            word-break: break-all;

            &:first-child {
                padding-left: 2.25rem;
            }

            &:last-child {
                padding-right: 2.25rem;
            }
        }

        th {
            background-color: #f3f2f4;
        }
    }

    .pagination {
        padding-right: 2.25rem;
    }
}

.m-t-5 {
    margin-top: 5px;
}

.m-t-36 {
    margin-top: 36px;
}

.p-y-1r {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.m-t-1\.5r {
    margin-top: 1.5rem;
}

.m-t-2r {
    margin-top: 2rem;
}

.m-r-1r {
    margin-right: 1rem;
}

.va-middle {
    vertical-align: middle;
}

.loading-overlay {
    z-index: 100;
    background: #fff;
}

.border-bottom {
    border-bottom: 1px solid gray;
}

.datepicker {
    .datepicker-content {
        height: auto;
    }
}

.table-title {
    padding: 0 2.25rem 1rem;
    font-size: 16px;
}

.divider {
    display: block;
    border-top: 1px solid #dbdbdb;
    margin: 1rem 0;
    padding: 0 !important;
}

.coa-form {
    width: 500px;

    .field-group {
        display: flex;

        & .form-field:not(:last-child) {
            margin-right: 16px;
        }
    }

    .form-field {
        width: 242px;
        margin-bottom: 16px;

        .field {
            .label {
                font-size: 14px;
                line-height: 20px;
                color: #999999;
                margin-bottom: 4px;
                font-weight: normal;
            }

            .input {
                border-color: #c2c2c2;
            }
        }
    }
}

.coa-tree-header {
    .title {
        color: #363636;
        font-size: 20px;
        font-weight: 600;
        line-height: 1.125;
    }
}

.slider-container {
    padding: 0rem 1.5rem;
    height: 100vh;
    z-index: 100000;
    .app-page-header .title {
        margin: 0;
    }

    .columns.app-page-header {
        margin: 0;
    }

    .app-page-content {
        margin: 0 !important;

        & > div {
            padding: 32px;
        }

        .control input[type='text']:disabled,
        .control textarea:disabled,
        .control select:disabled {
            padding: calc(0.5em - 1px) calc(0.75em - 1px);
            background-color: #f3f3f3;
        }
    }

    .footer-cta {
        width: calc(100% - 3rem);
        margin-bottom: 0;
        padding: 1rem 32px;
    }
}

.b-table {
    font-size: 1rem;

    .table {
        font-weight: normal;
        color: #333;

        td,
        th {
            &:first-child {
                padding-left: 2em;
            }

            &:last-child {
                padding-right: 2em;
            }
        }

        th,
        th.is-current-sort {
            border-bottom: none;
            font-weight: 400;
        }

        .button {
            &.b-r-5 {
                width: 24px;
                height: 24px;
            }
        }
    }

    .table-wrapper ~ .level {
        margin-right: 1rem;
    }

    .table {
        thead {
            background-color: #f3f2f4;
        }

        th:first-child,
        td:first-child {
            padding-left: 32px;
        }

        th .th-wrap {
            .button {
                .icon {
                    margin-left: 0;
                    margin-right: 0;
                }

                &.is-small {
                    .icon {
                        font-size: 1rem;
                    }
                }

                &.is-medium {
                    .icon {
                        font-size: 1.25rem;
                    }
                }

                &.is-large {
                    .icon {
                        font-size: 1.5rem;
                    }
                }
            }
        }
    }
}

//Success notification CSS

.notices .notification {
    &.is-success {
        background-color: #d9ffdb;
        border: 1px solid #64cd6d;
        border-radius: 4px;
        color: #666;
        padding-top: 14px;
        padding-bottom: 12px;

        .delete {
            background: none;
            top: auto;

            &::before {
                background-color: #666;
                width: 60%;
            }

            &::after {
                background-color: #666;
                height: 60%;
            }
        }

        .text {
            font-weight: 400;
            max-width: 300px;

            b {
                font-weight: 500;
            }
        }

        .icon {
            height: 1rem;
            width: 1rem;
            margin-top: 4px;

            .mdi-48px.mdi:before {
                font-size: 20px;
                color: #64cd6d;
            }
        }
    }
}

.message-body {
    .message-state {
        .error-message .title.is-4 {
            font-size: 16px;
            color: #333;
            margin-bottom: 10px;
        }

        .error-message .columns,
        .error-message .column {
            padding: 0;
            margin: 0;
            text-align: center;
            color: #666;
            font-size: 14px;
        }

        .error-message .column.is-offset-one-quarter {
            margin-left: 25%;
            word-break: normal;
        }
    }
}

.tab-item {
    .title.is-5 {
        font-size: 16px;
        color: #333;
        font-weight: 400;
        margin-bottom: 8px;

        & ~ .title.is-5 {
            margin-top: 32px;
        }
    }
}

.acc-tag {
    .tag {
        margin-right: 5px;
    }
}

.z-outline {
    padding: 1px;
}

.notices .notification.is-warning.is-top-right {
    max-width: 400px;
    padding: 16px;
    background: #fef1be;
    color: #666666;
    .media .media-left .icon.is-large {
        height: 2rem;
        width: 2rem;
    }
    i::before {
        font-size: 24px;
        color: #ffc053;
    }
}
/* 
    Flexbox Utilties
    To be removed when bulma is updated to 0.9
*/
.is-flex {
    display: flex;
    &.is-flex-direction-column {
        flex-direction: column;
    }
    &.is-justify-content-center {
        justify-content: center;
    }
    &.is-justify-content-space-between {
        justify-content: space-between;
    }
    &.is-align-items-center {
        align-items: center;
    }
    &.is-align-items-flex-end {
        align-items: flex-end;
    }
    &.is-align-content-center {
        align-content: center;
    }
    &.is-flex-wrap-wrap {
        flex-wrap: wrap;
    }
    &.column-gap-10 {
        column-gap: 10px;
    }
    @for $i from 0 through 5 {
        .is-flex-grow-#{$i} {
            flex-grow: $i;
        }
    }
}

/* 
    Spacing Utilties
    To be removed when bulma is updated to 0.9
*/
$spaceamounts: (
    0: 0,
    1: 0.25,
    2: 0.5,
    3: 0.75,
    4: 1,
    5: 1.5,
    6: 3,
);
$sides: (top, bottom, left, right);

@each $space, $val in $spaceamounts {
    @each $side in $sides {
        .m#{str-slice($side, 0, 1)}-#{$space} {
            margin-#{$side}: #{$val}rem;
        }

        .p#{str-slice($side, 0, 1)}-#{$space} {
            padding-#{$side}: #{$val}rem;
        }
    }
}

// CSS added to support GDS Migration

.page-loading {
    margin-left: var(--sidebar-primary-width, 244px);
    &.loading-overlay {
        z-index: 1000;
    }
}
.z-layout-main {
    height: var(--page-height-with-header);
}
.page-banner {
    padding: 12px 24px;
}