import { getAuthToken } from '@hercules/context';
import {
    COATachyonService,
    AccessManagementService,
    CalendarTachyonService,
    AtalantaService,
    OperationsCenterCommonService,
    RheaService,
    RequestExecutorService,
} from '@zeta/service-clients';
import { createAxiosInstance } from '@zeta/http-client';
import { GetAllPeriodsEODResponse, GetAllPeriodsForADateRange, PeriodDetails, SetCalendarStateModel, TimePeriod } from './types';
import Vue from 'vue';
import Component from 'vue-class-component';
import { DATE_FORMAT_WITH_TIMEZONE, DATE_FORMAT_YYYY_MM_DD, PHASES_END } from './common/constants';
import dayJs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { CalendarTypes } from '@zeta/service-clients/lib/tachyon/calendar-management/types';
dayJs.extend(timezone);
dayJs.extend(utc);

export const COAServiceManager = () => {
    let instance: any;
    return function (baseURL: string) {
        instance =
            instance ||
            new COATachyonService({
                baseURL,
                resolveAuthToken: () => {
                    return {
                        Authorization: `Bearer ${getAuthToken()}` as string,
                    };
                },
            });
        return instance;
    };
};

export const AccessManagementServiceManager = () => {
    let instance: any;
    return function (baseURL: string) {
        instance =
            instance ||
            new AccessManagementService({
                baseURL,
                resolveAuthToken: () => {
                    return {
                        Authorization: `Bearer ${getAuthToken()}` as string,
                    };
                },
            });
        return instance;
    };
};

export const calendarServiceManager = () => {
    let instance: any;
    const resolveAuthToken = () => {
        return { Authorization: 'Bearer ' + getAuthToken() };
    };
    return function (baseURL: string) {
        instance =
            instance ||
            new CalendarTachyonService({
                baseURL,
                resolveAuthToken,
            });
        return instance;
    };
};

export const kaleServiceManager = () => {
    let instance: any;
    const resolveAuthToken = () => {
        return { Authorization: 'Bearer ' + getAuthToken() };
    };
    return function (baseURL: string) {
        instance =
            instance ||
            new RequestExecutorService({
                baseURL: baseURL,
                resolveAuthToken,
            });
        return instance;
    };
};
export const atalantaServiceManager = () => {
    let instance: any;
    const resolveAuthToken = () => {
        return { Authorization: 'Bearer ' + getAuthToken() };
    };
    return function (baseURL: string) {
        instance =
            instance ||
            new AtalantaService({
                baseURL,
                resolveAuthToken,
            });
        return instance;
    };
};

export const operationCenterServiceManager = () => {
    let instance: OperationsCenterCommonService;
    const resolveAuthToken = () => {
        return { Authorization: 'Bearer ' + getAuthToken() };
    };
    return function (baseURL: string) {
        instance = instance || new OperationsCenterCommonService({ baseURL, resolveAuthToken });
        return instance;
    };
};

export const rheaServiceManager: () => (baseURL: string) => RheaService = () => {
    let instance: RheaService;
    const resolveAuthToken = () => {
        return { Authorization: 'Bearer ' + getAuthToken() };
    };
    return function (baseURL: string) {
        instance = instance || new RheaService({ baseURL, resolveAuthToken });
        return instance;
    };
};
// eslint-disable-next-line
@Component({})
export class OrchestraEODService extends Vue {
    private calendarService = calendarServiceManager();

    /**
     * Function to fetch periods for a given calendar
     * containing the periods date range
     * @param periodParameters parameters to get all periods
     */
    public async fetchPeriodsForCalendar(
        periodParameters: GetAllPeriodsForADateRange,
    ): Promise<GetAllPeriodsEODResponse | null> {
        try {
            //Set the timezone to dayjs instance
            const timezoneName = periodParameters.timezone;
            dayJs.tz.setDefault(timezoneName);

            // Prepare the date str for start and end periods
            const startDateStr = dayJs(periodParameters.periodStartDate).format(DATE_FORMAT_YYYY_MM_DD);
            const endDateStr = dayJs(periodParameters.periodEndDate).format(DATE_FORMAT_YYYY_MM_DD);

            // Add days in end Date to calculate the range
            const rangeEndDate = periodParameters.isSelectedDateNotCBUDate
                ? dayJs(periodParameters.periodEndDate).add(2, 'day')
                : dayJs(periodParameters.periodEndDate).add(1, 'day');
            const rangeEndDateStr = dayJs(rangeEndDate).format(DATE_FORMAT_YYYY_MM_DD);
            const periods = await this.calendarService(periodParameters.auraUrl).getPeriodsForCalendar({
                tenantId: periodParameters.tenantId,
                calendarId: periodParameters.calendarId,
                clockId: periodParameters.clockId,
                cycleId: periodParameters.cycleId,
                startDate: dayJs.tz(startDateStr).format(DATE_FORMAT_WITH_TIMEZONE),
                endDate: dayJs.tz(rangeEndDateStr).format(DATE_FORMAT_WITH_TIMEZONE),
            });
            // Timestamp to pick the correct period from data
            const periodStartTimestamp = dayJs.tz(startDateStr).valueOf();
            const periodEndTimestamp = dayJs.tz(endDateStr).valueOf();
            dayJs.tz.setDefault();
            return {
                startPeriod: periods?.data?.find((period: PeriodDetails) => {
                    return period.startTime === periodStartTimestamp;
                }),
                endPeriod: periods?.data?.find((period: PeriodDetails) => {
                    return period.startTime === periodEndTimestamp;
                }),
                periods: periods?.data,
            };
        } catch (error) {
            return null;
        }
    }

    /**
     * Function to get the current CBU and next CBU data
     * @param calendarStatePayload calendar state payload
     */
    public async fetchCurrentCBUForCalendar(
        calendarStatePayload: SetCalendarStateModel,
    ): Promise<GetAllPeriodsEODResponse | null> {
        try {
            const selectedDate = calendarStatePayload.selectedDate;
            const { data: cbuResponse } = await this.calendarService(calendarStatePayload.auraUrl).getCurrentCBU({
                tenantId: calendarStatePayload.tenantId,
                calendarId: calendarStatePayload.selectedCalendar.id,
            });
            const currentCbuResponse = cbuResponse as CalendarTypes.GetCurrentCBUResponse;

            if (!calendarStatePayload.updateCBUPeriodOnly) {
                /**
                 * We need to check what is the status of the currentCBU, if it is either "BOPI" or "BOFI" then we need to
                 * store the CURRENT_CBU/periodId to previous day's periodId and show data of previous day and current day
                 * otherwise use periodId of currrent day and next day
                 */
                const isPhaseBoPiOrBoFi = PHASES_END.includes(currentCbuResponse?.status);
                /**
                 * Start date and end date are used to fetch periodId when EOD run started and ended.
                 * In case a date is already pre-selected (for example via route), we need to fetch the
                 * start and end periodId of the selected date as well
                 */
                let periodStartDate: number, periodEndDate: number;
                const currentCBUDateStartTime = dayJs(currentCbuResponse?.startTime).valueOf();
                // This is for selected date start and end period id
                if (selectedDate && selectedDate < currentCBUDateStartTime) {
                    periodStartDate = selectedDate;
                    periodEndDate = dayJs(periodStartDate).add(1, 'day').valueOf();
                } else {
                    // Based on the current CBU response
                    if (isPhaseBoPiOrBoFi) {
                        /*
                         * In such case, the previous date would be startperiod and endperiod would be the preious data of current CBU
                         */
                        periodStartDate = dayJs(currentCbuResponse?.startTime).subtract(1, 'day').valueOf();
                        periodEndDate = currentCBUDateStartTime;
                    } else {
                        /*
                         * In such case, the current date would be startperiod and endperiod would be the next data of current CBU
                         */
                        periodStartDate = currentCBUDateStartTime;
                        periodEndDate = dayJs(currentCbuResponse?.startTime).add(1, 'day').valueOf();
                    }
                }
                // Get periods for the given range, this is required to get the start and end period id
                // mainly for these 2 states - updatedDateCBU and updatedNextCBU
                // Saving the formatted period start date cause it will again be used to set query params in the request payload
                const periodData = await this.fetchPeriodsForCalendar({
                    tenantId: calendarStatePayload.tenantId,
                    calendarId: calendarStatePayload.selectedCalendar?.id,
                    auraUrl: calendarStatePayload.auraUrl,
                    clockId: currentCbuResponse?.clockID as string,
                    cycleId: currentCbuResponse?.cycleID as string,
                    timezone: calendarStatePayload.selectedCalendar?.timezone,
                    periodStartDate,
                    periodEndDate, // Add one more day to get the right period with the right date range
                    isSelectedDateNotCBUDate: selectedDate && selectedDate < currentCBUDateStartTime ? true : false,
                });
                // Next based on the period response, pick the right start period, end period and currentCBU response;
                return {
                    startPeriod: periodData?.startPeriod as PeriodDetails,
                    endPeriod: periodData?.endPeriod as PeriodDetails,
                    cbuPeriod: currentCbuResponse as PeriodDetails,
                    periods: periodData?.periods as TimePeriod[],
                };
            } else {
                return {
                    cbuPeriod: currentCbuResponse as PeriodDetails,
                };
            }
        } catch (error) {
            return null;
        }
    }
}
