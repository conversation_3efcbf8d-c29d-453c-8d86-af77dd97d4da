import { DashboardTabs } from '@/views/modules/dashboard/constants';
import { CalendarTypes } from '@zeta/service-clients/lib/tachyon/calendar-management/types';

export declare type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;

export interface AphroditeHeaderAttributes {
    appHeaderName: string;
    showAppSwitcher: boolean;
    fromCollection: boolean;
    omsBaseUrl: string;
    tenantLogoUrl: string;
    herculesServiceClientBaseUrl?: string;
    sandboxId?: string;
    supportedLocales?: string;
    selectedLocale?: string;
    showLocaleSwitcher?: boolean;
    showProfileManagementOptions?: boolean;
    cipherBaseUrl?: string;
    productName?: string;
    theme?: string;
}

export interface COATreeModel {
    aggregatedBalance: number;
    children: COATreeModel[];
    nodeID: string | number;
    nodeType: string;
    nodeValue: string;
    path?: string;
}

export type ZONES = 'PREPROD' | 'PROD' | 'STAGE' | 'HDFC_BETA' | 'HDFC_LZ' | 'HDFC_UAT';

export type GRAFANA_PARAMS =
    | 'from'
    | 'to'
    | 'var-tenant_id'
    | 'var-coa_id'
    | 'var-cbu_start_date'
    | 'var-cbu_id'
    | 'var-period_id'
    | 'var-phase'
    | 'var-orchestra_pg_datasource'
    | 'var-limit'
    | 'refresh';

export interface MenuList {
    id: string;
    name: string;
    icon?: string;
    listObj?: {
        listItems: Array<Calendar> | Array<Coa>;
    };
}

export interface MenuItem extends MenuList {
    slotName: string;
    createBtn: {
        name: string;
    };
}

export interface Calendar {
    id: string;
    tenantID: string;
    code: string;
    name: string;
    timezone: string;
    status: string;
    weekStartOffset: number;
    yearStartDate: string;
    calendarID?: string;
    currentBookDate?: string;
    lastBookDate?: string;
}

export interface Coa {
    attributes: string;
    calendarID: string;
    code: string;
    currency: string;
    description: string;
    id: string;
    name: string;
    rootNodeID: string;
    status: string;
    tenantID: string;
    disabled?: boolean;
}

export interface SetCalendarStateModel {
    tenantId: string;
    auraUrl: string;
    selectedCalendar: Calendar;
    selectedDate?: number;
    updateCBUPeriodOnly?: boolean;
}

export interface SetCalendarCoAPhaseModel {
    calendarId: string;
    coaList?: Coa[];
}

export interface SetSelectedDateModel {
    selectedDate: number;
    selectedCalendar: Calendar;
    currentCBU: CalendarTypes.GetCurrentCBUResponse | null;
    auraUrl: string;
    timezone: string;
}

export interface GetAllPeriodsForADateRange {
    tenantId: string;
    calendarId: string;
    auraUrl: string;
    cycleId: string;
    clockId: string;
    timezone: string;
    periodStartDate: number;
    periodEndDate: number;
    isSelectedDateNotCBUDate?: boolean;
}

export interface GetAllPeriodsEODResponse {
    cbuPeriod?: PeriodDetails;
    startPeriod?: PeriodDetails;
    endPeriod?: PeriodDetails;
    periods?: TimePeriod[];
}

export interface DashboardState {
    imgSrc: string;
    title: string;
    description: string;
}

export interface WorkerDetails {
    sequenceNumber: number;
    entityType: string;
    periodicity: string;
    workerName: string;
    startTime: string;
    updatedTime: string;
    timeTaken: string;
    totalEntityCount: number;
    successfulEntityCount: number;
    failedEntityCount: number;
    status: string;
    periodId: string;
}

export interface CoaDetails {
    calendarID: string;
    code: string;
    currency: string;
    description: string;
    endTime: string;
    id: string;
    name: string;
    phaseStatus: string;
    rootNodeID?: string;
    sequenceNumber: number;
    startTime: string;
    status: string;
    tenantID: string;
    activePhase: any;
    currentDate: number;
}
export interface RunEodProcessModalConfig {
    'orchestra-url'?: string;
    'service-url'?: string;
    'service-url-v2'?: string;
    'hercules-base-url'?: string;
    'tenant-id'?: string;
    locale?: string;
    'calendar-id'?: string;
    'period-id'?: string;
    'next-period-id'?: string;
    'current-book-date'?: string;
    phase?: string;
    open?: boolean;
    'phase-timeline-status'?: string;
    'requested-by'?: string;
    'calendar-name'?: string;
    'base-url'?: string;
    config?: string;
    'rd-code'?: string;
    'enable-rd'?: boolean;
    'enable-rerun-data'?: boolean;
    'enable-eod-bulk-processing'?: boolean;
}

export interface CutoffModalConfig {
    'service-url'?: string;
    'hercules-base-url'?: string;
    'base-url'?: string;
    'tenant-id'?: string;
    locale?: string;
    'calendar-id'?: string;
    'calendar-name'?: string;
    'requested-by'?: string;
    'period-id'?: string;
    'current-book-date'?: string;
    'rd-code'?: string;
    config?: string;
    open?: boolean;
    'schedule-id'?: string;
    'schedule-name'?: string;
    'schedule-code'?: string;
    'schedule-description'?: string;
    'schedule-cron-expression'?: string;
    'clock-id'?: string;
    'cbu-cycle-id'?: string;
    'action-type'?: string;
    'is-approval-required'?: string | boolean;
}

export interface SkipLedgersFormConfig {
    'service-url'?: string;
    'tenant-id'?: string;
    locale?: string;
    theme?: string;
    'period-id'?: string;
    'current-book-date'?: string;
    phase?: string;
    'phase-timeline-status'?: string;
    config?: string;
    open?: boolean;
}

export interface WorkerDetails {
    phase: string;
    sequenceNumber: number;
    entityType: string;
    workerName: string;
    startTime: string;
    updatedTime: string;
    timeTaken: string;
    totalEntityCount: number;
    successfulEntityCount: number;
    failedEntityCount: number;
    status: string;
    periodId: string;
    periodicity: string;
    runID: string;
    lastSuccessfulExecutionID: string;
    open?: boolean;
}
export interface DashboardActionListItem {
    id: number;
    name: string;
    leftIcon: string;
    action: string;
    disabled?: boolean;
}

export interface SetCoaDetailsStateModel {
    calendarId?: string;
    tenantId?: string;
    atalantaUrl?: string;
    periodId?: string;
    triggerValue?: string;
}

export interface SetUserRolesModel {
    omsBaseUrl: string;
}

export interface UserProfile {
    authProfileId?: string;
    name?: string;
}

export interface AllPeriodsConfig {
    defaultFilters?: {
        [key: string]: unknown;
    };
    enableDefaultFilters?: boolean;
    enableCache?: boolean;
    cacheEntityName?: string;
    dbName?: string;
    cachePeriod?: number;
}

export interface UserPreferences {
    authProfile?: string;
    tenantId?: string;
    calendarId?: string;
    coaId?: string;
    currentCBUDate?: number;
    selectedDate?: number;
    [key: string]: any;
}

export interface PeriodDetails {
    id: string;
    tenantID: number;
    startCbuSequenceNumber: number;
    status: string;
    calendarID: string;
    clockID?: string;
    cycleID?: string;
    endCbuSequenceNumber?: number;
    sequenceNumber?: number;
    tags?: {
        [key: string]: string;
    };
    startTime: number;
    nextPeriodStartTime?: number;
    formattedStartTime?: string;
    formattedNextPeriodStartTime?: string;
}

export interface EODRunInfo {
    startTime?: number;
    status?: string;
    phase?: string;
    endTime?: number;
}

export type WORKER_TABS_TYPE = 'workerInfo' | 'workerDashboard';
export type BATCH_RUN_TABS_TYPE = 'batchRun' | 'trackerInfo';

export interface Checklist {
    checklistCode: string;
    name: string;
    system: string;
    type: string;
    lastUpdated: string;
    status: string;
    phase: string;
    report: Report;
    currentPhase?: string;
}
export interface Report {
    version: number;
    name: string;
    description: string;
    status: Status;
    statusVerifierCode: string;
    provider: string;
    tasks?: TasksEntity[] | null;
    attributes: { [key: string]: string };
    clientProvidedCreatedAt: string;
    clientProvidedModifiedAt: string;
    createdAt: string;
    modifiedAt: string;
}
export interface Status {
    value: string;
    reason: string;
    reasonCode: string;
}
export interface TasksEntity {
    name: string;
    description: string;
    displaySequenceNumber: number;
    code: string;
    status: Status;
    kpis?: KpisEntity[] | null;
    additionalDetailsUri: string;
    attributes: { [key: string]: string };
    clientProvidedCreatedAt: string;
    clientProvidedModifiedAt: string;
    createdAt: string;
    modifiedAt: string;
}
export interface KpisEntity {
    code: string;
    name: string;
    description: string;
    metric: Metric;
}
export interface Metric {
    value: number;
    unit: string;
}
export interface CalendarNavigationConfig {
    name: string;
    key: string;
    icon?: string;
    module?: string;
    items?: any;
    badge?: string;
}

export interface CalendarSideNavigationConfig {
    listItems: {
        name: string;
        id: string;
    }[];
    selectedIndex: string;
}

export interface RequestParams {
    tenantId: string;
    processDefinitionKey?: string;
    requestType?: string;
}

export interface CBUResponse extends CalendarTypes.GetCurrentCBUResponse {
    phaseTransitionHistory: PhaseTransitionHistory;
}

interface PhaseTransitionHistory {
    [key: string]: { startTime?: number; endTime?: number };
}

export type AlertType = {
    [key in DashboardTabs]?: { message?: string; strict?: boolean };
};

export interface AppHeaderMenuConfig {
    [k: string]: {
        show: boolean;
        label: string;
    };
}

export interface SystemLedger {
    ledgerID: string;
}

export interface Sandbox {
    sandboxId: string;
    domainId: string;
}

export interface VoucherCodesData {
    description: string;
    disabledUntil: number;
    status: string;
    tenantId: string;
    transactionCode: string;
}
export interface PostingCategoriesTree {
    key: string;
    label: string;
    children?: PostingCategoriesTree[];
}

export interface VoucherCode {
    tenantID: string;
    transactionCode: string;
    description: string;
    status: string;
    disabledUntil: number;
}

export interface BasicParametersEventPayload {
    clockId?: string;
    cycleId?: string;
    periodsData?: Period[];
    appendMonth?: boolean;
    appendYear?: boolean;
}

export interface Period {
    id: string;
    startTime: string;
    nextPeriodStartTime?: string;
    status?: string;
    sequenceNumber: number;
}

export interface RowsTabEventPayload {
    coaNodes?: string[];
    postingCategories?: string[];
    voucherCodes?: string[];
    balanceCodes?: { [key: string]: string[] };
}

export interface ColumnsTabEventPayload {
    [key: string]: {
        [key: string]: string[];
    };
}

export interface BalanceExplorerSettings extends Vue {
    isApplyBtnLoading: boolean;
}

export interface CutoffScheduleDetails {
    data: CalendarTypes.GetCutoffScheduleResponse | null;
    error: unknown | null;
    isLoading: boolean | null;
}
export interface ScheduleDetail {
    id: string;
    status: string;
    scheduleTime: string;
}
export interface EODRunDetails {
    data: ScheduleDetail | null;
    error: unknown | null;
    isLoading: boolean | null;
}

export interface PhaseTransition {
    startTime: number;
    endTime: number;
}

export interface TimePeriod {
    id: number;
    tenantID: string;
    startCbuSequenceNumber: number;
    status: string;
    calendarID: number;
    clockID: number;
    cycleID: number;
    endCbuSequenceNumber: number;
    sequenceNumber: number;
    tags: string[];
    startTime: number;
    nextPeriodStartTime: number;
    formattedStartTime: string;
    formattedNextPeriodStartTime: string;
    liveProcessingState: string;
    phaseTransitionHistory: PhaseTransitionHistory;
}
