import { Buefy } from '@zeta-design/galaxy/core';
import Vue, { PluginObject, PluginFunction } from 'vue';

type PluginList = [PluginObject<any> | PluginFunction<any>, any];

const plugins: PluginList[] = [[Buefy, null]];

export const installGlobalPlugins = () => {
    plugins.forEach((item) => {
        const [plugin, options] = item;
        if (options) {
            Vue.use(plugin, options);
        } else {
            Vue.use(plugin);
        }
    });
};
