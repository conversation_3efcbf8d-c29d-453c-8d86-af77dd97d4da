<template>
    <ZLayoutAside>
        <ZSideNavigation
            v-if="calendarSideNavigationConfig && calendarNavigation"
            @item-click="navigateToItemSelected"
            :items="calendarNavigation"
            :parentEllipsisWidth="210"
            :selectedKey="selectedKey"
        >
            <template v-slot:header>
                <div class="menu-switcher">
                    <ZSelect
                        :multiSelect="false"
                        :isSelector="true"
                        toolTipPosition="is-bottom-right"
                        v-bind="calendarSideNavigationConfig"
                        @change="onCalendarDropdownChange"
                        :minEllipsisWidth="180"
                    ></ZSelect>
                </div>
            </template>
        </ZSideNavigation>
    </ZLayoutAside>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import AuraFinanceCenter from '@/EODCenter';
import { Calendar, Coa, CalendarNavigationConfig, CalendarSideNavigationConfig } from '@/types';
import { ROOT_STATE_CONST } from '@/store/types';
import { MODULES_PATH, LEDGER_TYPES } from '@/common/constants';
import { CALENDAR_MODULES } from '../constants';
@Component({
    name: 'CalendarSideNavigation',
    components: {},
})
export default class CalendarSideNavigation extends AuraFinanceCenter {
    private calendarNavigation : CalendarNavigationConfig[] | null = null;
    private calendarSideNavigationConfig: CalendarSideNavigationConfig | null = null;
    private get selectedCalendar(): Calendar {
        return this.getStateData(ROOT_STATE_CONST.SELECTED_CALENDAR_DETAILS);
    }
    private get calendarCoaList (): Coa[] {
        return this.getStateData(ROOT_STATE_CONST.CALENDAR_COAS);
    }
    private get allCoAs(): Coa[] {
        return this.getStateData(ROOT_STATE_CONST.ALL_COAS);
    }
    /**
     * Watch all coas and construct the coa list in menu
     * after all coas
     */
    @Watch('allCoAs')
    private allCoasUpdated() {
        this.prepareCalendarSideNavigation();
    }
    private selectedKey = MODULES_PATH.OVERVIEW;
    created() {
        this.setKeyForSideNavigation();
        this.checkCalendarSelected();
    }

    private setKeyForSideNavigation() {
        const {name = '', params = {}} = this.$route;
        const ledgerType = params.ledgerType === LEDGER_TYPES.accountholder ? MODULES_PATH.ACCOUNT_HOLDER_LEDGERS
                    : MODULES_PATH.INTERNAL_LEDGERS;
        switch(name) {
            case CALENDAR_MODULES.COA_OVERVIEW:
                this.selectedKey = `${MODULES_PATH.OVERVIEW}-${params?.coaId}`;
                break;
            case CALENDAR_MODULES.CALENDAR_OVERVIEW:
                this.selectedKey = MODULES_PATH.OVERVIEW;
                break;
            case CALENDAR_MODULES.COA_TREE_VIEW:
                this.selectedKey = `${MODULES_PATH.NODES}-${params?.coaId}`;
                break;
            case CALENDAR_MODULES.COA_LEDGER_LIST:
            case CALENDAR_MODULES.COA_LEDGER_VIEW:
                this.selectedKey = `${ledgerType}-${params?.coaId}`;
                break;
            case CALENDAR_MODULES.WORKER_MASTER_LIST:
                this.selectedKey = `${MODULES_PATH.WORKERS}-${params?.coaId}`;
                break;
        }
    }

    /**
     * Function called when the calendar dropdown is changed
     */
    private async onCalendarDropdownChange(item: Calendar) {
        const path = `${MODULES_PATH.CALENDAR}/${item.id}/${MODULES_PATH.OVERVIEW}`;
        await this.setStateData({
            key: ROOT_STATE_CONST.SELECTED_CALENDAR_DETAILS,
            value: item || {},
        });
        this.$router.push({ path });
    }

    /**
     * Function to prepare calendar side navigation data
     */
    private prepareCalendarMenu () {
        const calendarDropdown = this.allCalendars?.map((calendar: Calendar)=>{
            return {
                name: calendar.name,
                id: calendar.id
            }
        });
        this.calendarSideNavigationConfig = {
            listItems: calendarDropdown ?? [],
            selectedIndex: this.selectedCalendar?.id
        }
        // Add calendar navigation to calendar menu
        this.prepareCalendarSideNavigation();
    }

    /**
     * Function to navigate to selected item
     */
    private navigateToItemSelected(item: any) {
        // Navigate to the route
        let path = '';
        this.selectedKey = item.key;
        switch(item.module) {
            case MODULES_PATH.OVERVIEW:
                path = `${MODULES_PATH.CALENDAR}/${this.selectedCalendar.id}/${MODULES_PATH.OVERVIEW}`;
                break;
            case `${MODULES_PATH.COA}-${MODULES_PATH.OVERVIEW}`:
                path = `${MODULES_PATH.CALENDAR}/${item.calendarId}/${MODULES_PATH.COA}/${item.coaId}/${MODULES_PATH.OVERVIEW}`;
                break;
            case MODULES_PATH.NODES:
                path = `${MODULES_PATH.CALENDAR}/${item.calendarId}/${MODULES_PATH.COA}/${item.coaId}/${MODULES_PATH.NODES}`;
                break;
            case MODULES_PATH.INTERNAL_LEDGERS:
                path = `${MODULES_PATH.CALENDAR}/${item.calendarId}/${MODULES_PATH.COA}/${item.coaId}/${MODULES_PATH.LEDGERS}/${LEDGER_TYPES.system}`;
                break;
            case MODULES_PATH.ACCOUNT_HOLDER_LEDGERS:
                path = `${MODULES_PATH.CALENDAR}/${item.calendarId}/${MODULES_PATH.COA}/${item.coaId}/${MODULES_PATH.LEDGERS}/${LEDGER_TYPES.accountholder}`;
                break;
            case MODULES_PATH.WORKERS:
                path = `${MODULES_PATH.CALENDAR}/${item.calendarId}/${MODULES_PATH.COA}/${item.coaId}/${MODULES_PATH.WORKERS}`;
                break;
        }
        this.$router.push({ path });
    }

    /**
     * Function to prepare the coa list for side navigation
     */
    private async prepareCalendarSideNavigation() {
        // If page is refreshed and coas are not populated
        if(!this.calendarCoaList) {
            await this.setStateData({
                key: ROOT_STATE_CONST.CALENDAR_COAS,
                value: {
                    calendarId: this.selectedCalendar.id,
                    coaList: this.allCoAs
                },
            });
        }
        if(!this.calendarCoaList && !Array.isArray(this.calendarCoaList)) {
            return;
        }
        const coaListNavigation = this.calendarCoaList?.map((coa: Coa)=> {
            return {
                name: coa.name,
                key: coa.id,
                icon: '',
                items:[
                {
                    name: this.$i18n.t('calendarManagement.coa.overview.title') as string,
                    key: `${MODULES_PATH.OVERVIEW}-${coa.id}`,
                    coaId: coa.id,
                    module: `${MODULES_PATH.COA}-${MODULES_PATH.OVERVIEW}`,
                    calendarId: coa.calendarID,
                    icon: 'summarize',
                },{
                    name: this.$i18n.t('calendarManagement.coa.nodes.title') as string,
                    key: `${MODULES_PATH.NODES}-${coa.id}`,
                    coaId: coa.id,
                    module: MODULES_PATH.NODES,
                    calendarId: coa.calendarID,
                    icon: 'account-balance-wallet',
                },{
                    name: this.$i18n.t('calendarManagement.coa.internalLedgers.title') as string,
                    coaId: coa.id,
                    calendarId: coa.calendarID,
                    module: MODULES_PATH.INTERNAL_LEDGERS,
                    key: `${MODULES_PATH.INTERNAL_LEDGERS}-${coa.id}`,
                    icon: 'folder',
                },{
                    name: this.$i18n.t('calendarManagement.coa.accountHolderLedgers.title') as string,
                    coaId: coa.id,
                    calendarId: coa.calendarID,
                    module: MODULES_PATH.ACCOUNT_HOLDER_LEDGERS,
                    key: `${MODULES_PATH.ACCOUNT_HOLDER_LEDGERS}-${coa.id}`,
                    icon: 'supervised-user-circle',
                },{
                    name: this.$i18n.t('calendarManagement.coa.workers.title') as string,
                    key:  `${MODULES_PATH.WORKERS}-${coa.id}`,
                    coaId: coa.id,
                    calendarId: coa.calendarID,
                    icon: 'task',
                    module:  MODULES_PATH.WORKERS,
                }]
            }
        });
        this.calendarNavigation = [
            {
                name: this.$i18n.t('calendarManagement.overview.title') as string,
                key: MODULES_PATH.OVERVIEW,
                icon: 'summarize',
                module: MODULES_PATH.OVERVIEW,
            },
            {
                name: this.$i18n.t('calendarManagement.coa.title') as string,
                key:  MODULES_PATH.COA,
                icon: 'account-tree',
                module:  MODULES_PATH.COA,
                items: coaListNavigation,
            }
        ];
    }

    /**
     * Function to prepare calendar side navigation data
     */
    private async checkCalendarSelected() {
        // If the selected calendar is null
        if(!this.selectedCalendar) {
            const calendarId = this.$route.params.calendarId;
            const selectedCalendar = this.allCalendars?.find((calendar: Calendar)=> calendar.id === calendarId);
            await this.setStateData({
                key: ROOT_STATE_CONST.SELECTED_CALENDAR_DETAILS,
                value: selectedCalendar || {},
            });
            await this.setStateData({
                key: ROOT_STATE_CONST.CALENDAR_COAS,
                value: {
                    calendarId,
                    coaList: this.allCoAs
                },
            });
        }
        this.prepareCalendarMenu();
    }
}
</script>

<style lang="scss">
.menu-switcher {
    background: var(--sidebar-primary-switcher-bg-color);
    .custom-trigger {
        .computed-name {
            color: white;
        }
        background: transparent;
        color: white;
        padding: 10px;
        border-bottom: none;
    }
}
</style>
