<template>
    <!-- Container -->
    <section class="dashboard-container">
        <!-- Header Containing Title, badges and timer context -->
        <!-- Not part of new design, commenting for now-->
        <!--   <header class="dashboard-container__header">
            <dashboard-header @update_dashboard="updateDashboard"></dashboard-header>
        </header> -->
        <!-- Calender and COA selectors -->
        <section class="dashboard-container__filters">
            <dashboard-filter
                v-if="allCalendars && allCalendars.length"
                :eod-run-start-date="eodRunStartDate"
                :refresh-timestamp="refreshTimestamp"
                :updated-book-date-on-e-o-d-completion="updatedBookDateOnEODCompletion"
                @update_widgets="updateDashboard"
                @update_dashboard="updateDashboard"
                @open_start_run="openStartRun"
                @open_phase_run="openStartRun"
                @open_cutoff="openCutOff"
                @open_eod_schedule_popover="openEOD"
                @open_skip_ledgers_form="openSkipLedgersForm"
                @view_all_periods="openPeriodsSideSheet = !openPeriodsSideSheet"
                @open_worker_master_list="openCoaWorkerMasterListSideSheet = !openCoaWorkerMasterListSideSheet"
            ></dashboard-filter>
        </section>
        <!-- Spinner to invoke when filters are being reset -->
        <div class="dashboard-container__loader" :class="{ active: isCalendarLoading }">
            <z-spinner
                :canCancel="true"
                :isLoading="isCalendarLoading"
                :isFullPage="false"
                size="is-small"
                :overlay="false"
            >
            </z-spinner>
        </div>
        <!-- Error/empty State -->
        <section v-if="!(currentCBU && selectedCoA)" class="dashboard-container__state">
            <z-state v-if="dashboardState && !isCalendarLoading" v-bind="{ ...dashboardState }"></z-state>
        </section>

        <section v-if="currentCBU && selectedCoA" class="dashboard-container__widget-content">
            <!-- phase-timeline -->
            <div v-if="updatedDateCBU && updatedDateNextCBU" class="dashboard-container__phase-timeline">
                <zwe-phase-cycle
                    :tenant-id="$ifiId"
                    :key="`phase-cycle-${viewUpdateKey}`"
                    :service-url="tachyonUrl"
                    :current-phase="getCurrentPhase"
                    :start-period-id="updatedDateCBU && updatedDateCBU.id"
                    :end-period-id="updatedDateNextCBU && updatedDateNextCBU.id"
                    :coa-id="selectedCoA && selectedCoA.id"
                    :locale="locale"
                    @view_phase_cycle_details="viewPhaseCycleDetails"
                    @phase_cycle_timer="(e) => (phaseCycleIntervalId = e.detail.length && e.detail[0])"
                />
            </div>
            <div class="coa-tabs" :key="`coa-tabs-${viewUpdateKey}`">
                <z-segment-control
                    v-if="_calendarCoAPhaseList && coaList.length > 1"
                    isBordered
                    :multiple="false"
                    :options="coaList"
                    :selectedItem="selectedCoA && selectedCoA.id"
                    @Selected="coaClicked"
                />
                <!-- Segment control is not needed if there is only one COA  -->
                <div v-else class="z-component dashboard-container__coa-header">
                    <p>
                        {{ `${$t('monitoringDashboard.coaName')}:` }} <strong>{{ `${coaList[0].label}` }} </strong>
                    </p>
                </div>
            </div>
            <div class="alerts-container">
                <transition name="fade">
                    <custom-alert
                        v-if="dangerAlertMessages"
                        type="error"
                        :title="$t('monitoringDashboard.alerts.phaseBlocked', { phase: getCurrentPhase })"
                    >
                        <div v-for="(message, index) in dangerAlertMessagesArray" :key="index" class="alert-item">
                            <template v-if="message.includes(':')">
                                {{ message.split(':')[0] }}:
                                <span class="error-text">{{ message.split(':')[1] }}</span>
                            </template>
                            <template v-else>
                                {{ message }}
                            </template>
                        </div>
                    </custom-alert>
                </transition>
                <transition name="fade">
                    <z-alert
                        v-if="warningAlertMessages"
                        alertType="inline"
                        :description="warningAlertMessages"
                        :closeButton="false"
                        position="is-bottom-right"
                        type="warning"
                        icon="warning"
                        class="warning-alert"
                    />
                </transition>
            </div>
            <!-- dashboard tabs -->
            <div class="dashboard-container__tabs">
                <z-layout-main>
                    <z-row>
                        <z-column :gutterLess="true">
                            <z-row>
                                <z-tabs
                                    size="is-large"
                                    :activeTab="activeTab"
                                    position="is-left"
                                    :tabArray="tabArray"
                                    type="default"
                                    @tab-click="tabClicked"
                                >
                                    <div class="coa-balances-summary" v-show="activeTab === dashboardTabs.OVERVIEW">
                                        <template v-if="updatedDateCBU && updatedDateNextCBU">
                                            <zwe-dashboard-overview-tiles
                                                :tenant-id="$ifiId"
                                                :orchestra-url="orchestraUrl"
                                                :key="`dashboard-tiles-${viewUpdateKey}`"
                                                :service-url="atlantaUrl"
                                                :coa-id="selectedCoA && selectedCoA.id"
                                                :start-period-id="updatedDateCBU && updatedDateCBU.id"
                                                :end-period-id="updatedDateNextCBU && updatedDateNextCBU.id"
                                                :calendar-id="updatedDateCBU && updatedDateCBU.calendarID"
                                                @phase-runs="phaseRunTileClicked"
                                                @failed-ledgers="openCoaFailedLedgersSideSheet = true"
                                                :phase-id="currentCBU && currentCBU.status"
                                                @workers="workerTileClicked"
                                                :config="
                                                    JSON.stringify({
                                                        startPeriodDetail: updatedDateCBU,
                                                        endPeriodDetail: updatedDateNextCBU,
                                                    })
                                                "
                                            >
                                            </zwe-dashboard-overview-tiles>
                                            <template v-if="showBalanceExplorer">
                                                <BalanceExplorer
                                                    :coa-id="selectedCoA && selectedCoA.id"
                                                ></BalanceExplorer>
                                            </template>
                                        </template>
                                    </div>

                                    <div v-if="enableRealTimeWorkers"
                                        class="real-time-workers"
                                        v-show="activeTab === dashboardTabs.REAL_TIME_WORKERS"
                                    >
                                        <zwe-worker-list
                                            :atalanta-service-url="atlantaUrl"
                                            :tenantId="this.$ifiId"
                                            :coaid="selectedCoA && selectedCoA.code"
                                            :cbuid="currentCBU && currentCBU.startTime"
                                        ></zwe-worker-list>
                                    </div>
                                    <div
                                        class="worker-list-container"
                                        v-show="activeTab === dashboardTabs.WORKERS && !cbuLoading"
                                    >
                                        <zwe-angelos-data-grid
                                            v-if="updatedDateCBU && updatedDateNextCBU"
                                            entity-id="eod-center-workers-list"
                                            :tenant-id="GOD_TENANT_ID"
                                            :key="`worker-list-${viewUpdateKey}`"
                                            :params="
                                                JSON.stringify({
                                                    orchestraUrl: orchestraUrl,
                                                    ATALANTA_BASE_URL: atlantaUrl,
                                                    tenantId: $ifiId,
                                                    coaId: selectedCoA && selectedCoA.id,
                                                    startPeriodId: updatedDateCBU && updatedDateCBU.id,
                                                    endPeriodId: updatedDateNextCBU && updatedDateNextCBU.id,
                                                })
                                            "
                                            @worker-row-clicked="onWorkerRowClicked"
                                            :context="workerListContext"
                                            @on-data="getWorkersData"
                                        ></zwe-angelos-data-grid>
                                    </div>
                                    <div
                                        class="checklist-container"
                                        v-if="showChecklist"
                                        v-show="activeTab === dashboardTabs.CHECKLISTS && !cbuLoading"
                                    >
                                        <zwe-angelos-data-grid
                                            entity-id="eod-center-checklists"
                                            :tenant-id="GOD_TENANT_ID"
                                            :key="`checklists-${viewUpdateKey}`"
                                            :params="
                                                JSON.stringify({
                                                    BASE_URL: auraUrl,
                                                    tenantId: $ifiId,
                                                    coas: selectedCoA && selectedCoA.code,
                                                    updatedCBUDate: checklistCBUDate && checklistCBUDate.updatedCBUDate,
                                                    updatedNextCBUDate:
                                                        checklistCBUDate && checklistCBUDate.updatedNextCBUDate,
                                                    calanderId: selectedCalendar.id,
                                                })
                                            "
                                            @checklistItem-row-clicked="handleChecklistClicked"
                                            @allow-clicked="handleChecklistAllowClicked"
                                            @on-data="getChecklists"
                                        ></zwe-angelos-data-grid>
                                        <!-- @checklist-row-clicked="onChecklistRowClicked" -->
                                    </div>
                                    <div
                                        class="checklist-container"
                                        v-if="showATDChecklist"
                                        v-show="activeTab === dashboardTabs.ATD_CHECKLISTS && !cbuLoading"
                                    >
                                        <zwe-angelos-data-grid
                                            entity-id="eod-center-atd-checklists"
                                            :tenant-id="GOD_TENANT_ID"
                                            :key="`checklists-${viewUpdateKey}`"
                                            :params="
                                                JSON.stringify({
                                                    BASE_URL: auraUrl,
                                                    tenantId: $ifiId,
                                                    coas: selectedCoA && selectedCoA.code,
                                                    updatedCBUDate: checklistCBUDate && checklistCBUDate.updatedCBUDate,
                                                })
                                            "
                                            @checklistItem-row-clicked="handleChecklistClicked"
                                            @allow-clicked="handleChecklistAllowClicked"
                                            @on-data="onATDChecklistData"
                                        ></zwe-angelos-data-grid>
                                    </div>
                                </z-tabs>
                            </z-row>
                        </z-column>
                    </z-row>
                </z-layout-main>
            </div>
        </section>
        <!-- Trigger EOD modal component -->
        <zwe-run-eod-process-modal v-bind="{ ...runEodProcessConfig }" @on_close="onEodModalClose" />
        <z-modal class="eod-modal__error" :isModalActive="isEoDRunError">
            <z-modal-content headerType="plain">
                <template #header-right-item>
                    <z-button @click="isEoDRunError = false" icon-left="close" type="tertiary" />
                </template>
                <z-state
                    :errorStatusCode="500"
                    variant="small"
                    align="is-left"
                    :title="$t(`error.${isSinglePhaseRun ? 'cantStartPhase' : 'cantStartEoD'}.header`)"
                    :description="
                        $t(`error.${isSinglePhaseRun ? 'cantStartPhase' : 'cantStartEoD'}.message`, {
                            scheduleTime: cutoffScheduleTime,
                        })
                    "
                ></z-state>
            </z-modal-content>
        </z-modal>
        <!-- TODO: skip ledgers form is not in side sheet for now, keeping it here for future  -->
        <!-- <SideSheet
            type="side"
            :appendToBody="false"
            :open="skipLedgersFormConfig.open"
            @on_sheet_close="onSkipLedgersFormClose"
        >
            <template v-slot:content>
                <zwe-skip-ledgers-form
                    v-bind="{ ...skipLedgersFormConfig }"
                    @cancel_skip_ledgers="onSkipLedgersFormClose"
                    @submit_success="onSkipLedgersFormClose"
                ></zwe-skip-ledgers-form
            ></template>
        </SideSheet>-->
        <!-- TODO: Sourav/Siddharth - This should be a separate layout component with dynamic component injection -->
        <!-- Worker details side sheet  -->
        <side-sheet
            type="side"
            :appendToBody="false"
            :hideArrowActions="true"
            :hideOpenInNewTab="true"
            :open="workerDetails && workerDetails.open"
            @on_sheet_close="onWorkerDetailsSheetClose"
        >
            <template v-slot:content>
                <section class="worker-details-sheet">
                    <zwe-worker-details
                        :config="JSON.stringify(workerDetails)"
                        :current-c-b-u-date="currentCBU && currentCBU.startTime"
                        :selected-date="updatedDateCBU && updatedDateCBU.startTime"
                        :locale="locale"
                    >
                    </zwe-worker-details>
                    <z-tabs
                        size="is-large"
                        :activeTab="activeWorkerTab"
                        position="is-left"
                        :tabArray="workerTabArray"
                        type="default"
                        @tab-click="workerTabClicked"
                    >
                        <div class="coa-phase-runs" v-show="activeWorkerTab === workerTabs.PHASE_RUNS">
                            <z-utility-header
                                :title="$t('monitoringDashboard.allRuns')"
                                type="transparent"
                                paddingless
                                class="phase-run-header"
                                size="small"
                                :isBordered="false"
                            ></z-utility-header>
                            <zwe-coa-batch-run-list
                                :tenant-id="$ifiId"
                                :service-url="atlantaUrl"
                                :orchestra-url="orchestraUrl"
                                :coa-id="selectedCoA && selectedCoA.id"
                                :locale="locale"
                                :phase-id="workerDetails && workerDetails.phase"
                                :cbu-id="workerDetails && workerDetails.cbuId"
                                :config="
                                    JSON.stringify({
                                        table: {
                                            hideActions: true,
                                            disablePagination: false,
                                            hideSequence: false,
                                        },
                                        hideTitle: true,
                                    })
                                "
                                :worker-id="workerDetails && workerDetails.workerId"
                            >
                            </zwe-coa-batch-run-list>
                        </div>
                        <div class="coa-failed-ledgers" v-show="activeWorkerTab === workerTabs.FAILED_LEDGERS">
                            <zwe-failed-ledger-table
                                :tenant-id="$ifiId"
                                :atalanta-url="atlantaUrl"
                                :coa-id="selectedCoA && selectedCoA.id"
                                :coA-name="selectedCoA && selectedCoA.name"
                                :period-id="workerDetails && workerDetails.periodId"
                                :phase-id="workerDetails && workerDetails.phase"
                                :worker-id="workerDetails && workerDetails.workerName"
                                :disable-skip-ledgers="!isAdminRole"
                                :orchestra-run-id="workerDetails && workerDetails.runID"
                                :workflow-url="workflowUrl"
                                :workflow-url-v2="rheaRequestUrl"
                                :exception-raised-by="authProfileName"
                                :current-book-date="currentBookDate"
                                :calendar-name="selectedCalendar && selectedCalendar.name"
                                @failed_ledger_success="onFailedLedgerLoad"
                                :rd-code="rdMap['skipLedgers']"
                                :enable-rd="enableRD"
                                :config="
                                    JSON.stringify({
                                        table: {
                                            disableFilter: false,
                                            paginationPageSizes: [10],
                                        },
                                    })
                                "
                            >
                            </zwe-failed-ledger-table>
                        </div>
                    </z-tabs>
                </section>
            </template>
        </side-sheet>
        <!-- Batch Run List  side sheet  -->
        <side-sheet
            type="side"
            :appendToBody="false"
            :hideArrowActions="true"
            :hideOpenInNewTab="true"
            :open="batchRunListConfig && batchRunListConfig.open"
            @on_sheet_close="onBatchRunListSideSheetClose"
            class="batch-run-list-side-sheet"
        >
            <template v-slot:content>
                <batch-run-list
                    :tenantId="$ifiId"
                    :currentCBU="currentCBU"
                    :selectedCoA="selectedCoA"
                    :updatedDateCBU="updatedDateCBU"
                    :updatedDateNextCBU="updatedDateNextCBU"
                ></batch-run-list>
            </template>
        </side-sheet>
        <!-- All Periods side sheet  -->
        <side-sheet
            type="side"
            :appendToBody="false"
            :hideArrowActions="true"
            :hideOpenInNewTab="true"
            :open="openPeriodsSideSheet"
            @on_sheet_close="openPeriodsSideSheet = !openPeriodsSideSheet"
        >
            <template v-slot:content>
                <!-- Main container -->
                <main class="periods-list-wrapper">
                    <zwe-periods-list
                        v-if="selectedCalendar && currentCBU"
                        :tenant-id="$ifiId"
                        :service-url="atlantaUrl"
                        :locale="$locale"
                        :calendar-id="selectedCalendar.id"
                        :cbu-date-time="currentCBU && currentCBU.startTime"
                        :current-book-date-time="currentCBU.startTime"
                        :show-heading="true"
                        :config="
                            JSON.stringify({
                                props: periodsConfig,
                            })
                        "
                    ></zwe-periods-list>
                </main>
            </template>
        </side-sheet>
        <!-- COA Failed Ledger table -->
        <side-sheet
            type="side"
            :appendToBody="false"
            :hideArrowActions="true"
            :hideOpenInNewTab="true"
            class="coa-failed-ledger-sidesheet"
            :open="openCoaFailedLedgersSideSheet"
            @on_sheet_close="openCoaFailedLedgersSideSheet = !openCoaFailedLedgersSideSheet"
        >
            <template v-slot:content>
                <!-- Main container -->
                <main class="coa-failed-ledger-wrapper">
                    <failed-ledgers
                        v-if="selectedCoA && selectedCalendar && phaseCycleData"
                        :tenantId="$ifiId"
                        :calendarId="selectedCalendar.id"
                        :phase="getCurrentPhase"
                        :phaseStatus="phaseCycleData.coaPhaseStatus"
                        :coaName="selectedCoA.name"
                        :periodDate="currentPeriodDate"
                        :periodId="getCurrentPeriodId"
                        :coaId="selectedCoA.id"
                    ></failed-ledgers>
                </main>
            </template>
        </side-sheet>
        <!-- COA Worker Master table -->
        <side-sheet
            type="side"
            class="checklist-detail-side-sheet"
            :appendToBody="false"
            :hideArrowActions="true"
            :hideOpenInNewTab="true"
            :open="openCoaWorkerMasterListSideSheet"
            @on_sheet_close="openCoaWorkerMasterListSideSheet = !openCoaWorkerMasterListSideSheet"
        >
            <template v-slot:content>
                <!-- Main container -->
                <section class="worker-master-list-sheet">
                    <z-utility-header
                        :title="`${$t('monitoringDashboard.workersForCoa.title')}: ${
                            selectedCoA ? selectedCoA.name : ''
                        }`"
                        :description="$t('monitoringDashboard.workersForCoa.description')"
                        type="transparent"
                        paddingless
                        class="phase-run-header"
                        size="small"
                        :isBordered="false"
                    ></z-utility-header>
                    <main class="coa-worker-master-list-wrapper">
                        <zwe-angelos-data-grid
                            entity-id="eod-center-workers-master-list"
                            :tenant-id="GOD_TENANT_ID"
                            :params="
                                JSON.stringify({
                                    orchestraUrl: orchestraUrl,
                                    ATALANTA_BASE_URL: atlantaUrl,
                                    tenantId: $ifiId,
                                    coaId: selectedCoA && selectedCoA.id,
                                })
                            "
                        ></zwe-angelos-data-grid>
                    </main>
                </section>
            </template>
        </side-sheet>
        <!-- Checklist Detail -->
        <side-sheet
            type="side"
            :appendToBody="false"
            :hideArrowActions="true"
            :hideOpenInNewTab="true"
            :open="openChecklistDetailSideSheet"
            @on_sheet_close="openChecklistDetailSideSheet = !openChecklistDetailSideSheet"
            id="checklist-detail-side-sheet"
        >
            <template v-slot:content>
                <!-- Main container -->
                <z-container-header
                    v-if="checklistClickedDetail"
                    :title="checklistClickedDetail.report.name"
                    :description="checklistClickedDetail.report.description"
                    type="plain"
                    isBordered
                >
                    <template #right-item>
                        <z-tooltip
                            active
                            appendToBody
                            :isCustomContainer="false"
                            position="is-left"
                            placement="start"
                            textAlignment="justify"
                            :isOpen="false"
                        >
                            <template #default>
                                <!-- Disable button if checklist item is already in allowed state (i.e. manual or automated checklist) or
                                 any of the tasks are failed. User needs to move the task to other state to allow a checklist item -->
                                <z-button
                                    :label="
                                        checklistClickedDetail.type === CHECKLIST_TYPE.MANUAL
                                            ? $t('monitoringDashboard.checklist.allow')
                                            : $t('monitoringDashboard.checklist.forceAllow')
                                    "
                                    :disabled="isAllowBtnDisabled"
                                    type="secondary"
                                    @click="isChecklistModalActive = true"
                                />
                            </template>
                            <template #content>
                                {{
                                    checklistClickedDetail.type === CHECKLIST_TYPE.MANUAL
                                        ? $t('monitoringDashboard.checklist.sidesheet.tooltip.allowManual')
                                        : $t('monitoringDashboard.checklist.sidesheet.tooltip.allowAutomated')
                                }}
                            </template>
                        </z-tooltip>
                    </template>
                </z-container-header>
                <main class="checklist-detail-wrapper">
                    <zwe-checklist-tasks
                        v-if="checklistClickedDetail"
                        :tenant-id="$ifiId"
                        :locale="$locale"
                        :config="
                            JSON.stringify({
                                checklistDetail: checklistClickedDetail,
                            })
                        "
                        @checklist-update="updateChecklistDetail"
                    />
                </main>
            </template>
        </side-sheet>

        <!-- Checklist Allow Modal -->
        <z-modal
            v-if="checklistClickedDetail"
            :is-modal-active="isChecklistModalActive"
            class="dashboard-container__checklist-modal"
        >
            <z-modal-content
                :key="checklistClickedDetail.name"
                :title="
                    (checklistClickedDetail &&
                        (checklistClickedDetail.type === CHECKLIST_TYPE.MANUAL
                            ? $t('monitoringDashboard.checklist.allow')
                            : $t('monitoringDashboard.checklist.forceAllow'))) +
                    ': ' +
                    checklistClickedDetail.name
                "
                item-position="left"
                header-type="colored"
                :description="
                    $t('monitoringDashboard.checklist.modal.description') +
                    ' ' +
                    (checklistClickedDetail.type === CHECKLIST_TYPE.MANUAL
                        ? $t('monitoringDashboard.checklist.modal.complete')
                        : $t('monitoringDashboard.checklist.modal.skipped'))
                "
                :is-shadowed="false"
            >
                <template #header-right-item>
                    <z-button
                        @click="
                            isChecklistModalActive = false;
                            removeForceAllowEventListeners();
                        "
                        icon-left="close"
                        type="tertiary"
                    />
                </template>
                <template>
                    <section class="dashboard-container__checklist-modal__body">
                        <div class="dashboard-container__checklist-modal__form__group">
                            <z-field
                                direction="vertical"
                                :label="$t('monitoringDashboard.checklist.modal.reasonCode')"
                                :rulesArray="isReasonCodeEntered"
                                rules="required"
                                :immediate="true"
                                v-if="Object.keys(checklistClickedDetail).length"
                            >
                                <span class="dashboard-container__checklist-modal__form__group__input">
                                    <z-input-text
                                        v-model="checklistClickedDetail.report.status.reasonCode"
                                        :placeholder="$t('monitoringDashboard.checklist.reasonCodePlaceholder')"
                                        type="text"
                                        :disabled="false"
                                        :readonly="false"
                                    />
                                </span>
                            </z-field>
                        </div>
                        <div class="dashboard-container__checklist-modal__form__group">
                            <z-field
                                direction="vertical"
                                :label="$t('monitoringDashboard.checklist.modal.additionalComments')"
                                v-if="Object.keys(checklistClickedDetail).length"
                            >
                                <span class="dashboard-container__checklist-modal__form__group__input">
                                    <z-text-area
                                        v-model="checklistClickedDetail.report.status.reason"
                                        :placeholder="$t('monitoringDashboard.checklist.additionalCommentsPlaceholder')"
                                        :disabled="false"
                                        :readonly="false"
                                    />
                                </span>
                            </z-field>
                        </div>
                        <div class="dashboard-container__checklist-modal__footer">
                            <z-action-group :maxButtons="2">
                                <z-action
                                    @click="submitAllowChecklist"
                                    :disabled="getAllowButtonDisabled"
                                    type="primary"
                                >
                                    {{ $t('monitoringDashboard.checklist.allow') }}
                                </z-action>
                                <z-action @click="handleGoToTask" type="secondary">{{
                                    $t('monitoringDashboard.checklist.modal.goToTasks')
                                }}</z-action>
                            </z-action-group>
                        </div>
                    </section>
                </template>
            </z-modal-content>
        </z-modal>

        <!-- EOD Completion Modal -->
        <z-modal :is-modal-active="isEODCompletionModalActive" class="dashboard-container__eod-modal">
            <z-modal-content
                :is-modal-header="false"
                :item-position="'center'"
                :header-type="'plain'"
                :is-shadowed="false"
            >
                <template>
                    <section class="dashboard-container__eod-modal__body">
                        <div class="dashboard-container__eod-modal__body__image-container">
                            <img :src="eodSuccessURL" :alt="$t('monitoringDashboard.eodSuccess.title')" />
                        </div>
                        <z-utility-header
                            type="plain"
                            :is-bordered="false"
                            :title="$t('monitoringDashboard.eodSuccess.title')"
                            :description="getCurrentBookDateLabel($t('monitoringDashboard.eodSuccess.description'))"
                        >
                        </z-utility-header>
                    </section>
                </template>
                <template #footer-action-items>
                    <z-action-group :max-buttons="2">
                        <z-action type="primary" @click="goToCurrentBookDate">{{
                            $t('monitoringDashboard.eodSuccess.toCurrentBookDate')
                        }}</z-action>
                        <z-action type="secondary" @click="closeEODSuccessModal">{{
                            $t('monitoringDashboard.eodSuccess.stayOnThisPage')
                        }}</z-action>
                    </z-action-group>
                </template>
            </z-modal-content>
        </z-modal>

        <z-modal type="small" :is-modal-active="isEODScheduledModal" class="dashboard-container__eod-modal">
            <z-modal-content
                :title="
                    scheduleEODActionType === 'resume'
                        ? $t('monitoringDashboard.scheduledEODModel.resumeTitle')
                        : $t('monitoringDashboard.scheduledEODModel.pauseTitle')
                "
            >
                <template #header-left-item>
                    <z-icon icon="info" size="is-medium" type="is-neutral"></z-icon>
                </template>

                <template #header-right-item>
                    <z-button @click="resetModal" icon-left="close" type="tertiary" />
                </template>
                <template>
                    <div class="eod-process__modal__content">
                        <span
                            v-if="scheduleEODActionType === 'resume'"
                            class="scheduled-cutoff-section__popover__content__section__text"
                            v-html="$t('monitoringDashboard.scheduledEODModel.resumeDescription', { scheduleEODTime })"
                        ></span>

                        <span
                            v-else
                            class="scheduled-cutoff-section__popover__content__section__text"
                            v-html="$t('monitoringDashboard.scheduledEODModel.pauseDescription', { scheduleEODTime })"
                        ></span>
                    </div>
                </template>

                <template #footer-action-items>
                    <z-action-group :max-buttons="2">
                        <z-action type="primary" @click="pauseOrResumeTrigger">{{
                            $t('monitoringDashboard.scheduledEODModel.submit')
                        }}</z-action>
                        <z-action type="secondary" @click="closeEODSuccessModal">{{
                            $t('monitoringDashboard.scheduledEODModel.cancel')
                        }}</z-action>
                    </z-action-group>
                </template>
            </z-modal-content>
        </z-modal>

        <zwe-cutoff-modal
            v-if="cutoffConfig.open"
            v-bind="{ ...cutoffConfig }"
            @action_success="closeCutoffModal"
            @on_close="closeCutoffModal"
        />
    </section>
</template>

<script lang="ts">
import AuraFinanceCenter from '@/EODCenter';
import {
    COA_SUMMARY_DATE_FORMAT,
    DASHBOARD_STATUS_TYPES,
    DATE_FORMAT_YYYY_MM_DD,
    MODULES_PATH,
    PHASES_START,
    WORKER_START_PHASES,
    cbuStatusTriggerMap,
    emptyStateDashboardUrl,
    GOD_TENANT_ID,
    clusterSpec,
} from '@/common/constants';
import SideSheet from '@/components/SideSheet.vue';
import i18n from '@/i18n';
import { ROOT_STATE_CONST } from '@/store/types';
import {
    AlertType,
    AllPeriodsConfig,
    Calendar,
    Checklist,
    Coa,
    CutoffModalConfig,
    DashboardState,
    EODRunInfo,
    RequestParams,
    RunEodProcessModalConfig,
    SetCalendarStateModel,
    SkipLedgersFormConfig,
    UserProfile,
    WorkerDetails,
} from '@/types';
import { context } from '@hercules/context';
import { ZAction, ZActionGroup, ZSpinner, ZUtilityHeader, ZIcon } from '@zeta-design/galaxy/core';
import { ZModal, ZModalContent } from '@zeta-design/galaxy/modal';
import { ZToast } from '@zeta-design/galaxy/toast';
import CustomAlert from '../components/CustomAlert.vue';
import { Atalanta } from '@zeta/service-clients/lib/atalanta/types';
import { CalendarTypes } from '@zeta/service-clients/lib/tachyon/calendar-management/types';
import dayjs from 'dayjs';
import { Component } from 'vue-property-decorator';
import { COAServiceManager, operationCenterServiceManager, rheaServiceManager } from '../../../../Services';
import { getLocaleFromCurrency } from '../../../../common/utils';
import { ADMIN_ROLE, periodStatusValues } from '../../calendar/constants';
import BatchRunList from '../../calendar/pages/BatchRunList.vue';
import FailedLedgers from '../../calendar/pages/FailedLedgers.vue';
import DashboardFilter from '../components/DashboardFilter.vue';
import DashboardHeader from '../components/DashboardHeader.vue';
import BalanceExplorer from '../components/BalanceExplorer.vue';
import {
    CHECKLIST_STATUS,
    CHECKLIST_TASK_STATUS,
    CHECKLIST_TYPE,
    DASHBOARD_REFRESH_INTERVAL,
    EOD_CHECKLIST_PROCESS_DEFINITION_KEY,
    DashboardTabs,
    WorkerTabs,
    eodSuccessURL,
    REQUEST_TYPE,
    CHECKLIST_STATUS_MAP,
    WORKER_STATUS,
    CHECKLIST_ITEM_PAYLOAD_DETAILS,
    ACTION_MANUAL_CUTOFF,
    ACTION_CREATE_CUTOFF_SCHEDULE,
    ACTION_EDIT_CUTOFF_SCHEDULE,
    ACTION_DELETE_CUTOFF_SCHEDULE,
    ACTION_PAUSE_CUTOFF_SCHEDULE,
    ACTION_RESUME_CUTOFF_SCHEDULE,
    CUTOFF_ACTION_RD_KEY_MAP,
} from '../constants';
import { RheaOperationsCenterTypes } from '@zeta/service-clients/lib/operations-center/types';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { RheaTypes } from '@zeta/service-clients/lib/rhea/types';
import * as _ from 'lodash';

dayjs.extend(utc);
dayjs.extend(timezone);

const coaService = COAServiceManager();
const opsService = operationCenterServiceManager();
const rheaService = rheaServiceManager();

@Component({
    name: 'MonitoringDashboard',
    components: {
        DashboardHeader,
        DashboardFilter,
        SideSheet,
        ZSpinner,
        ZToast,
        ZModal,
        ZModalContent,
        ZUtilityHeader,
        ZActionGroup,
        ZAction,
        BatchRunList,
        FailedLedgers,
        BalanceExplorer,
        ZIcon,
        CustomAlert,
    },
})
export default class MonitoringDashboard extends AuraFinanceCenter {
    runEodProcessConfig: RunEodProcessModalConfig = { open: false };
    cutoffConfig: CutoffModalConfig = { open: false };
    skipLedgersFormConfig: SkipLedgersFormConfig = { open: false };
    workerDetails: WorkerDetails | { open: boolean } = { open: false };
    batchRunListConfig = { open: false };
    alertMessagesMap: AlertType | null = null;
    isEoDRunError = false;
    isSinglePhaseRun = false;
    private failureAlertActive = false;
    private failureAlertMessage = '';
    private eodRunStartDate: number | null = null;
    private refreshTimestamp = new Date();
    private timer = 0;
    private updatedBookDateOnEODCompletion: number | null = null;
    private checklistClickedDetail: Checklist | null = null;
    // For sending original data to ops checklist workflow
    private checklistClickedDetailCached: Checklist | null = null;
    private viewUpdateKey = `${new Date().getTime()}`;
    private emptyStateUrl = emptyStateDashboardUrl;
    private dashboardTabs = DashboardTabs;
    private workerTabs = WorkerTabs;
    private activeTab = this.dashboardTabs.OVERVIEW;
    private activeWorkerTab = this.workerTabs.PHASE_RUNS;
    private workerListContext!: string;
    requestHistoryRefreshCounter = 0;
    private showChecklist = context.getAttribute('viewConfig.dashboard.tabs.showChecklist') || false;
    private showATDChecklist = context.getAttribute('viewConfig.dashboard.tabs.showATDChecklist') || false;
    // Hiding balances under overview tabs
    private showBalanceExplorer = context.getAttribute('viewConfig.dashboard.balanceExplorer.show') || false;

    private enableRerunData = context.getAttribute('viewConfig.modules.calendar.enableRerunData') || false;
    // Hiding certain tabs as part of TCP-1964
    private tabArray = [
        {
            value: this.dashboardTabs.OVERVIEW,
            label: i18n.t('monitoringDashboard.dashboardTabs.overview'),
        },
        {
            value: this.dashboardTabs.WORKERS,
            label: i18n.t('monitoringDashboard.dashboardTabs.workers'),
        },
        this.showChecklist && {
            value: this.dashboardTabs.CHECKLISTS,
            label: i18n.t('monitoringDashboard.dashboardTabs.checklist'),
        },
        this.showATDChecklist && {
            value: this.dashboardTabs.ATD_CHECKLISTS,
            label: i18n.t('monitoringDashboard.dashboardTabs.atdChecklist'),
        },
        // { value: this.dashboardTabs.SUPERVISORS, label: i18n.t('monitoringDashboard.dashboardTabs.supervisors') },
        // { value: this.dashboardTabs.OBSERVATIONS, label: i18n.t('monitoringDashboard.dashboardTabs.observations') },
        // { value: this.dashboardTabs.EXCEPTIONS, label: i18n.t('monitoringDashboard.dashboardTabs.exceptions') },
    ];
 

    // Add real-time workers tab if enabled
     created() {
        if (this.enableRealTimeWorkers) {
            this.tabArray.splice(1, 0, {
                value: this.dashboardTabs.REAL_TIME_WORKERS,
                label: i18n.t('monitoringDashboard.dashboardTabs.realTimeWorkers'),
            });
        }
    }
   
    private workerTabArray = [
        {
            value: this.workerTabs.PHASE_RUNS,
            label: i18n.t('monitoringDashboard.workerTabs.phaseRuns'),
            icon: 'fact-check',
        },
        {
            value: this.workerTabs.FAILED_LEDGERS,
            label: i18n.t('monitoringDashboard.workerTabs.failedLedgers'),
            icon: 'dark-mode',
        },
    ];
    private coaBalanceSummaryDate = dayjs().format(COA_SUMMARY_DATE_FORMAT);
    private selectedCalendarDate!: number;
    private openPeriodsSideSheet = false;
    private openCoaFailedLedgersSideSheet = false;
    private openCoaWorkerMasterListSideSheet = false;
    private openChecklistDetailSideSheet = false;
    private phaseCycleData: Atalanta.BatchRunPhaseCycleData | null = null;
    private scheduleEODTime: string | null = null;
    private scheduleEODActionType: string | null = null;
    private periodsConfig = {
        ...(context.getAttribute('viewConfig.modules.calendar.periods') as AllPeriodsConfig),
        dbName: this.dbName,
    };
    private eodSuccessURL = eodSuccessURL;
    private isEODCompletionModalActive = false;
    private isEODScheduledModal = false;
    private isChecklistModalActive = false;
    private isReasonCodeEntered = [
        {
            name: 'required',
            compute: {
                message: (field: any) => this.$t('monitoringDashboard.checklist.ruleReasonCodeMandatory'),
                validate: (value: string) => {
                    return value.length;
                },
            },
        },
    ];
    private isAllowBtnTrue = false;
    private phaseCycleIntervalId: number | null = null;
    private CHECKLIST_TYPE = CHECKLIST_TYPE;
    private CHECKLIST_STATUS = CHECKLIST_STATUS;
    private CHECKLIST_TASK_STATUS = CHECKLIST_TASK_STATUS;
    private GOD_TENANT_ID = GOD_TENANT_ID;
    private coaList: Coa[] = [];
    private failedWorkersCount = 0;
    // GETTERS
    private get selectedCalendar(): Calendar {
        return this.getStateData(ROOT_STATE_CONST.SELECTED_CALENDAR);
    }
    private get selectedCoA(): Coa {
        return this.getStateData(ROOT_STATE_CONST.SELECTED_CALENDAR_COA);
    }
    private get currentCBU(): CalendarTypes.GetCurrentCBUResponse & { liveProcessingState: string } {
        return this.getStateData(ROOT_STATE_CONST.CURRENT_CBU);
    }
    private get isCalendarLoading(): boolean {
        return this.getStateData(ROOT_STATE_CONST.CALENDAR_LOADING);
    }
    private get isCalendarCoALoading(): boolean {
        return this.getStateData(ROOT_STATE_CONST.CALENDAR_COA_LOADING);
    }
    private get updatedDateCBU(): CalendarTypes.GetCurrentCBUResponse {
        return this.getStateData(ROOT_STATE_CONST.UPDATED_DATE_CBU);
    }
    private get updatedDateNextCBU(): CalendarTypes.GetCurrentCBUResponse {
        return this.getStateData(ROOT_STATE_CONST.UPDATED_DATE_NEXT_CBU);
    }
    private get dashboardState(): DashboardState | null {
        return this.getStateData(ROOT_STATE_CONST.DASHBOARD_STATE);
    }
    private get cbuLoading(): any {
        return this.getStateData(ROOT_STATE_CONST.CBU_LOADING);
    }
    private get authProfileName(): string {
        const userProfile: UserProfile = this.getStateData(ROOT_STATE_CONST.USER_PROFILE);
        return userProfile && (userProfile.name || '');
    }
    private get isAdminRole(): boolean {
        const userRoles = this.getStateData(ROOT_STATE_CONST.USER_ROLES);
        return userRoles && userRoles.has(ADMIN_ROLE);
    }
    private get getCurrentPeriodId() {
        return PHASES_START.includes(this.getCurrentPhase) ? this.updatedDateCBU.id : this.updatedDateNextCBU.id;
    }
    private get currentPeriodDate() {
        return PHASES_START.includes(this.getCurrentPhase)
            ? this.updatedDateCBU?.startTime
            : this.updatedDateNextCBU?.startTime;
    }
    private get calendarPhaseCoAs(): Coa[] {
        return this.getStateData(ROOT_STATE_CONST.CALENDAR_PHASE_COAS) || [];
    }
    // EOD Completion flag -> clearly means that current cbu has moved to the next period (end period) and status has changed to active
    private get isEODCompletedForCurrentBookDate(): boolean {
        return (
            this.currentCBU?.id === this.updatedDateNextCBU?.id &&
            this.currentCBU?.status.toUpperCase() === periodStatusValues.active.toUpperCase()
        );
    }

    private get _calendarCoAPhaseList() {
        this.coaList = this.calendarPhaseCoAs.map((item: Coa) => {
            return {
                ...(this.failureAlertActive && { rightIcon: 'circle' }),
                label: item.name,
                ...item,
            };
        });
        return this.coaList;
    }
    /**
     * If the orchestra is running then get status from currentCBU else for previos days
     * if updatedDateNextCBU is beyond BOFI into next days PHASES_START then use phase as BOFI
     * else use updatedDateNextCBU.status
     */

    private get getCurrentPhase() {
        // this means that the current cbu has moved to next book date, but we need to show the phase as BOFI until the user moves to next book date
        if (this.isEODCompletedForCurrentBookDate) {
            return periodStatusValues.bofi.toUpperCase();
        }
        return this.currentCBU?.status;
    }
    private get localeFromCurrency() {
        return getLocaleFromCurrency(this.selectedCoA?.currency) || this.locale;
    }
    private get getAllowButtonDisabled() {
        return this.checklistClickedDetail?.report?.status?.reasonCode?.length === 0;
    }
    private get checklistCBUDate() {
        return {
            updatedCBUDate: dayjs(this.updatedDateCBU?.startTime).format(DATE_FORMAT_YYYY_MM_DD),
            updatedNextCBUDate: dayjs(this.updatedDateNextCBU?.startTime).format(DATE_FORMAT_YYYY_MM_DD),
        };
    }
    // Disabling btn if checklist item is already in allowed state (i.e. manual or automated checklist)
    // or the checklist is denied
    private get isAllowBtnDisabled() {
        return (
            (this.checklistClickedDetail?.report?.status?.value &&
                [CHECKLIST_STATUS.ALLOW, CHECKLIST_STATUS.FORCE_ALLOWED].includes(
                    this.checklistClickedDetail?.report?.status?.value,
                )) ||
            (this.checklistClickedDetail?.type === CHECKLIST_TYPE.MANUAL &&
                this.checklistClickedDetail?.report?.status?.value === CHECKLIST_STATUS.DENY)
        );
    }

    get cutoffScheduleTime(): string | null {
        const { data, error, isLoading } = this.getStateData(ROOT_STATE_CONST.CUTOFF_SCHEDULE_DETAILS) || {};
        if (isLoading || error) {
            return null;
        } else {
            const { scheduleTime = '' } = data || {};
            return scheduleTime;
        }
    }

    get cutoffScheduleDetails() {
        const { isLoading = true, error, data } = this.getStateData(ROOT_STATE_CONST.CUTOFF_SCHEDULE_DETAILS) || {};
        return { isLoading, error, data };
    }

    get isCutoffScheduled() {
        return this.cutoffScheduleDetails?.data;
    }

    get isCutoffScheduleActive() {
        return this.cutoffScheduleDetails?.data?.state === 'ACTIVE';
    }

    get activeTabAlertMessage() {
        return this.alertMessagesMap?.[this.activeTab]?.message ?? '';
    }

    get isActiveTabAlertStrict() {
        return this.alertMessagesMap?.[this.activeTab]?.strict ?? false;
    }

    get dangerAlertMessagesArray() {
        if (!this.alertMessagesMap) return [];
        return Object.values(this.alertMessagesMap)
            .filter((alert) => alert.strict === true)
            .map((alert) => alert.message)
            .filter(Boolean);
    }

    get dangerAlertMessages() {
        return this.dangerAlertMessagesArray.length > 0;
    }

    get warningAlertMessages() {
        if (!this.alertMessagesMap) return '';
        const warningMessages = Object.values(this.alertMessagesMap)
            .filter((alert) => alert.strict === false)
            .map((alert) => alert.message)
            .filter(Boolean);

        return warningMessages.length ? warningMessages.join(', ') : '';
    }

    get isEoDTriggerTimeBeforeScheduledCutoff() {
        const { scheduleTimestamp } = this.cutoffScheduleDetails?.data || {};
        if (scheduleTimestamp) {
            return Date.now() < scheduleTimestamp;
        }
        return false;
    }

    private async coaClicked(selectEvent: Coa[]) {
        // Set coa loader flag true
        await this.setStateData({
            key: ROOT_STATE_CONST.CALENDAR_COA_LOADING,
            value: true,
        });
        await this.setStateData({
            key: ROOT_STATE_CONST.SELECTED_CALENDAR_COA,
            value: selectEvent[0] || null,
        });
        await this.setStateData({
            key: ROOT_STATE_CONST.CALENDAR_COA_LOADING,
            value: false,
        });
        /**
         * Event to update the widgets
         */
        this.updateDashboard();
    }

    private getCurrentBookDateLabel(message: string) {
        // return 'EOD completed successfully for this book date';
        return message.replace(
            /{{bookDate}}/g,
            i18n.d(dayjs(this.updatedDateCBU?.startTime).valueOf(), 'short', this.localeFromCurrency),
        );
    }
    private onFailedLedgerLoad(event: CustomEvent) {
        if (event?.detail[0]) {
            this.workerTabArray[1].label = `${i18n.t('monitoringDashboard.workerTabs.failedLedgers')} (${
                event.detail[0].length
            })`;
        }
    }

    /**
     * Function to open the concerned COA clicked
     * in COA Summary
     */
    private viewCoADetails(coaDetailsEvent: CustomEvent) {
        const selectedCoA: Atalanta.BatchRunCoASummary = coaDetailsEvent?.detail?.[0];
        if (selectedCoA) {
            const routeData = this.$router.resolve({
                path: `${MODULES_PATH.CALENDAR}/id/${this.selectedCalendar?.id}/coas/id/${selectedCoA?.coaId}`,
                query: {
                    selectedDate: this.currentCBU?.startTime as string,
                    phase: this.currentCBU?.status,
                    phaseStatus: (DASHBOARD_STATUS_TYPES as never)[selectedCoA.coaPhaseStatus as string] || '',
                    periodId: this.currentCBU?.id as string,
                },
            });
            window.open(routeData.href, '_blank');
        }
    }

    /**
     * Function to open the calendar associated with
     * phase cycle widget
     */
    private viewCalendarDetails(calendarEvent: CustomEvent) {
        const selectedCalendar = calendarEvent?.detail?.[0];
        if (selectedCalendar) {
            const routeData = this.$router.resolve({
                path: `${MODULES_PATH.CALENDAR}/id/${selectedCalendar.id}`,
                query: {
                    calendarCode: selectedCalendar.code,
                    selectedDate: this.currentCBU?.startTime as string,
                },
            });
            window.open(routeData.href, '_blank');
        }
    }

    /**
     * Function to update cbu
     */
    private async updateCBUandPeriods(updateParams: { updatePeriods: boolean } = { updatePeriods: false }) {
        /* Fetch the start and end period data only if
         * 1. Current CBU is same as updatedDateNextCBU
         * 2. Current CBU is active cause that would mean EOD has ended for that book date and has moved to next book date
         */
        await this.setStateData({
            key: ROOT_STATE_CONST.CALENDAR_LOADING,
            value: true,
        });
        // Set cbu and periods value -> this in turn sets currentCBU, updatedDateCBU and updatedNextDateCBU state
        await this.setStateData({
            key: ROOT_STATE_CONST.CURRENT_CBU,
            value: {
                selectedCalendar: this.selectedCalendar,
                tenantId: this.$ifiId as string,
                auraUrl: `${this.auraUrl}`,
                updateCBUPeriodOnly: updateParams.updatePeriods ? false : true,
            } as SetCalendarStateModel,
        });
        // Set calendar loader flag as false
        await this.setStateData({
            key: ROOT_STATE_CONST.CALENDAR_LOADING,
            value: false,
        });
        if (this.showCutoff) {
            await this.fetchCutoffScheduleDetails();
        }
        if(this.enableScheduleEOD){
            await this.fetchScheduleEODDetails();
        }
       
    }
    /**
     * Method to fetch cutoff schedule details
     */

    private async fetchCutoffScheduleDetails() {
        const { calendarID, clockID, cycleID } = this.currentCBU;
        const requestParams = {
            serviceUrl: this.auraUrl,
            calendarId: calendarID,
            clockId: clockID,
            cycleId: cycleID,
            tenantId: this.$ifiId,
        };
        await this.setStateData({
            key: ROOT_STATE_CONST.CUTOFF_SCHEDULE_DETAILS,
            value: requestParams,
        });
    }

    private async fetchScheduleEODDetails() {
        const { calendarID } = this.currentCBU;
        const requestParams = {
            serviceUrl: this.atlantaUrl,
            calendarId: calendarID,
            tenantId: this.$ifiId,
            tenantCode: this.tenantCode,
        };
        await this.setStateData({
            key: ROOT_STATE_CONST.SCHEDULED_EOD_DETAILS,
            value: requestParams,
        });
    }

    private async pauseOrResumeTrigger() {
        try {
            const request: RequestParams = {
                tenantId: this.$ifiId as string,
                requestType: REQUEST_TYPE.BR,
            };
            const payload = this.createScheduledEODflowPayload({
                tenantId: this.$ifiId as string,
                baseUrl: `${this.auraInternalUrl}`,
                herculesBaseUrl: this.herculesBaseUrl as unknown as string,
                eodHostName: window.location.hostname,
                requestedTime: this.$d(new Date(), 'long12h', this.locale as string),
                requestedBy: this.authProfileName,
                calendarId: this.selectedCalendar?.id,
                isResume: this.scheduleEODActionType === 'resume' ? true : false,
                scheduledJobId: `${this.tenantCode}.${clusterSpec}.${this.selectedCalendar?.id}`,
            });
            const serviceUrl = this.rheaRequestUrl as unknown as string;

            const response = await rheaService(serviceUrl).submitBusinessRequest(
                request as RheaTypes.SubmitRequestParams,
                payload as RheaTypes.SubmitRequestPayload,
            );
            const { requestId = '' } = response.data;

            this.showSuccessToast(
                this.$t(`monitoringDashboard.scheduledEODModel.${this.scheduleEODActionType}Success`) as string,
                requestId
                    ? (this.$t(`monitoringDashboard.scheduledEODModel.requestIdMessage`, {
                          requestId,
                      }) as string)
                    : '',
            );
        } catch (error: any) {
            this.showErrorToast(new Error('An error occured while Submitting the request'));
        } finally {
            this.isEODScheduledModal = false;
        }
    }

    /**
     * Method executed when user choses to move
     * to next book date upon EOD run completion
     */
    private async goToCurrentBookDate() {
        this.isEODCompletionModalActive = false;
        // Even though we have received the current CBU, we need to update periods as well
        // Hence invoke the service method to update cbu as well as periods
        // Redundant API call is okay since it is not a big overhead
        await this.updateCBUandPeriods({ updatePeriods: true });
        this.updatedBookDateOnEODCompletion = this.currentCBU?.startTime as number;
        this.resetDashboardWidgets();
    }

    /**
     * Method executed when user choses to stay on the current
     * selected book date despite EOD run completion
     * Since we already have received the updated CBU, just close the modal
     */
    private closeEODSuccessModal() {
        this.isEODCompletionModalActive = false;
        this.isEODScheduledModal = false;
    }

    resetModal() {
        this.isEODScheduledModal = false;
    }
    /**
     * Function to edit the key that would re-render the dashboard components
     * @param loadParams if invokeManualRefresh is true, that means refresh was invoked manually
     */
    private async resetDashboardWidgets(
        loadParams: { invokeManualRefresh: boolean } = {
            invokeManualRefresh: false,
        },
    ) {
        await this.updateCBUandPeriods();
        this.selectedCalendarDate = this.getStateData(ROOT_STATE_CONST.SELECTED_CALENDAR_DATE);
        const selectedDate = this.selectedCalendarDate ?? this.updatedDateCBU?.startTime;
        this.coaBalanceSummaryDate = dayjs(selectedDate).format(COA_SUMMARY_DATE_FORMAT);
        this.workerListContext = JSON.stringify({
            currentPhase: this.currentCBU?.status,
            locale: this.locale,
        });
        this.refreshTimestamp = new Date();
        this.setStateData({ key: ROOT_STATE_CONST.WORKER_TABLE_DATA, value: null });
        this.setStateData({ key: ROOT_STATE_CONST.CHECKLIST_TABLE_DATA, value: null });
        this.viewUpdateKey = `${this.currentCBU?.calendarID}-${this.selectedCoA?.id}-${
            this.updatedDateCBU?.id
        }-${new Date().getTime()}`;
        if (loadParams.invokeManualRefresh) {
            this.displayUpdatedNotification();
        }
    }

    /**
     * Update key to update the widgets
     * @param loadParams if invokeManualRefresh is true, that means refresh was invoked manually
     */
    private async updateDashboard(
        loadParams: { invokeManualRefresh: boolean } = {
            invokeManualRefresh: false,
        },
    ) {
        clearInterval(this.timer);
        // clear phase cycle timer interval
        this.phaseCycleIntervalId && clearInterval(this.phaseCycleIntervalId);
        try {
            if (this.dashboardConfig?.autoRefresh) {
                this.autoRefreshData();
            }
            // Reverted changes for TCU-612 - sync currentCBU data on each refresh cycle
            // if (loadParams.invokeManualRefresh) {
            //     // if manual refresh is invoked, then update the CBU and periods
            //     await this.updateCBUandPeriods();
            //     /**
            //      * If the current cbu status has moved to ACTIVE of the next book date,
            //      * but the current period data (updatedDateNextCBU) says that the status is still in some other phase apart from ACTIVE,
            //      * that means the period data phases are not updated yet, hence we need to update the periods
            //      * It also means EOD run has completed and we need to show the EOD completion modal
            //      */
            //     if (this.isEODCompletedForCurrentBookDate && this.updatedDateNextCBU?.status?.toUpperCase() !== periodStatusValues.active.toUpperCase()) {
            //         this.isEODCompletionModalActive = true;
            //     }
            // }
            this.resetDashboardWidgets();
            this.resetDashboardWidgets(loadParams);
        } catch (error) {
            ZToast.open({
                heading: i18n.t('monitoringDashboard.dashboardUpdateFailed'),
                type: 'danger',
                icon: 'error',
            });
        }
    }

    /**
     * Function to set the EOD run start date that can be used
     * for parameterizing grafana dashboard start timestamp
     * @event event emitted from phase cycle widget
     */
    private async viewPhaseCycleDetails(event: CustomEvent) {
        const phaseCycleData: Atalanta.BatchRunPhaseCycleData = event.detail?.[0];
        if (phaseCycleData) {
            this.phaseCycleData = phaseCycleData;
            // fetch Active Phase data
            const activePhaseData = phaseCycleData?.phaseDetails?.find(
                (phase) => phase?.phaseName?.toUpperCase() === periodStatusValues.active.toUpperCase(),
            );
            this.eodRunStartDate = activePhaseData?.initiatedStartTime || activePhaseData?.completedStartTime || 0;
            // Get the status of the current phase running
            await this.setStateData({
                key: ROOT_STATE_CONST.EOD_RUN_INFO,
                value: {
                    startTime: this.eodRunStartDate,
                    // Get the status of the current phase running
                    status: phaseCycleData?.coaPhaseStatus,
                    endTime:
                        phaseCycleData?.phaseDetails?.find(
                            (phase) => phase?.phaseName?.toUpperCase() === periodStatusValues.bofi.toUpperCase(),
                        )?.completedStartTime || 0,
                } as EODRunInfo,
            });
        } else {
            // fallback
            this.eodRunStartDate = this.currentCBU?.startTime as number;
            await this.setStateData({
                key: ROOT_STATE_CONST.EOD_RUN_INFO,
                value: null,
            });
        }
    }

    /**
     * Function to display a toast on dashboard update
     */
    private displayUpdatedNotification() {
        ZToast.open({
            heading: i18n.t('monitoringDashboard.dashboardUpdated'),
        });
    }

    private autoRefreshData() {
        this.timer = setInterval(async () => {
            await this.updateDashboard({ invokeManualRefresh: true });
        }, this.dashboardConfig?.autoRefreshInterval || DASHBOARD_REFRESH_INTERVAL);
    }

    private phaseRunTileClicked() {
        this.batchRunListConfig.open = true;
    }
    private workerTileClicked() {
        this.activeTab = this.dashboardTabs.WORKERS;
    }
    private onBatchRunListSideSheetClose() {
        this.batchRunListConfig = { open: false };
    }

    /* View worker details */
    viewWorkerCardDetails(workerEvent: CustomEvent) {
        const { coaId, createdAt, updatedAt, executionID, jobId, workerName, status } = workerEvent?.detail?.[0];
        if (coaId && this.selectedCalendar && this.currentCBU && this.selectedCoA) {
            const phaseStatus = (DASHBOARD_STATUS_TYPES as never)[status as string] || '';
            const routeData = this.$router.resolve({
                path: `${MODULES_PATH.CALENDAR}/id/${this.selectedCalendar.id}/coas/id/${this.selectedCoA.id}/workers/id/${jobId}`,
                query: {
                    selectedDate: this.currentCBU.startTime as string,
                    sequenceNumber: `${this.currentCBU.sequenceNumber}`,
                    phase: this.currentCBU.status,
                    createdAt: createdAt,
                    executionID: executionID,
                    updatedAt: updatedAt,
                    workerNameToSearch: workerName,
                    status: phaseStatus,
                },
            });
            window.open(routeData.href, '_blank');
        }
    }
    /**
     * Function to set active tab on tab change
     * @event event emitted from ZTabs containing active
     * tab information
     */
    private tabClicked(event: { value: DashboardTabs }) {
        this.activeTab = event.value;
    }
    private workerTabClicked(event: { value: any }) {
        this.activeWorkerTab = event.value;
    }
    /* Gets called when the user clicks on the skip ledgers in actions dropdown
     */
    openSkipLedgersForm(phaseStatus: string) {
        /* TODO : If UX asks for skip ledgers in side sheet we can use this code */
        /*  this.skipLedgersFormConfig = {
            ...this.skipLedgersFormConfig,
            'service-url': this.bookkeeperUrl as unknown as string,
            'tenant-id': this.$ifiId as string,
            locale: this.locale as string,
            'period-id': this.currentCBU?.id as string,
            'current-book-date': this.currentBookDate,
            phase: this.currentCBU?.status,
            theme: 'aphrodite',
            'phase-timeline-status': '', // TODO: add phase timeline status which will be emitted by new phase component,
            config: JSON.stringify({ coaList: this.selectedCalendarPhaseCoAs }),
            open: true,
        }; */
        this.$router.push({
            path: MODULES_PATH.SKIP_LEDGERS_FORM,
            query: {
                periodId: this.currentCBU?.id as string,
                calendarId: this.selectedCalendar?.id,
                routeLabel: i18n.t('monitoringDashboard.dashboard') as string,
                phase: this.currentCBU?.status,
                coaId: this.selectedCoA?.id || '',
                coaName: this.selectedCoA?.name || '',
                currentBookDate: this.currentBookDate,
                triggerValue: cbuStatusTriggerMap[this.currentCBU?.status].currentTrigger,
                phaseTimelineStatus: phaseStatus,
            },
        });
    }
    /**
     * Gets called when the user clicks on the start EoD Run in actions dropdown
     */
    openStartRun(startEODPhaseRunEvent: { phaseStatus: string; runSinglePhase: boolean }) {
        const { phaseStatus, runSinglePhase = false } = startEODPhaseRunEvent;
        this.isSinglePhaseRun = runSinglePhase;
        this.isEoDRunError = false;
        if (this.isCutoffScheduleActive && this.isEoDTriggerTimeBeforeScheduledCutoff) {
            this.isEoDRunError = true;
            return;
        }
        this.runEodProcessConfig = {
            ...this.runEodProcessConfig,
            'enable-rerun-data': this.enableRerunData as boolean,
            'orchestra-url': this.orchestraUrl as unknown as string,
            'base-url': this.bookkeeperTriggerUrl as unknown as string,
            'service-url': this.workflowUrl as unknown as string,
            'service-url-v2': this.rheaRequestUrl as unknown as string,
            'hercules-base-url': this.herculesBaseUrl as unknown as string,
            'tenant-id': this.$ifiId as string,
            locale: this.locale as string,
            'calendar-id': this.selectedCalendar?.id as string,
            'period-id': this.currentCBU?.id as string,
            'next-period-id': this.updatedDateNextCBU?.id as string,
            'current-book-date': this.currentBookDate,
            phase: this.currentCBU?.status,
            'phase-timeline-status': phaseStatus,
            'calendar-name': this.selectedCalendar?.name as string,
            'requested-by': this.authProfileName,
            open: true,
            'rd-code': this.rdMap?.['startEoD'] as unknown as string,
            'enable-rd': !!this.enableRD as boolean,
            'enable-eod-bulk-processing': this.enableEodBulkProcessing as boolean,
            config: JSON.stringify({
                coaList: [
                    { id: this.selectedCoA?.id, name: this.selectedCoA?.name },
                    ...this.calendarPhaseCoAs.filter((coa) => coa.id !== this.selectedCoA?.id),
                ],
                flags: {
                    runSinglePhase,
                },
            }),
        };
    }
    // current book date of selected calendar
    public get currentBookDate() {
        return this.currentCBU ? dayjs(this.currentCBU.startTime).format(DATE_FORMAT_YYYY_MM_DD) : '';
    }

    private get scheduleDetails(): (CalendarTypes.GetCutoffScheduleResponse & { scheduleTime: string }) | null {
        const { data } = this.getStateData(ROOT_STATE_CONST.CUTOFF_SCHEDULE_DETAILS);
        return data;
    }
    /**
     * Gets called when the user closes the start run modal or when modal is automatically closed after successful api
     */
    onEodModalClose() {
        this.runEodProcessConfig = { open: false };
    }
    /**
     * Gets called when the closes the side sheet or when cancel the forms or when API is successful
     */
    onSkipLedgersFormClose() {
        this.skipLedgersFormConfig = { ...this.skipLedgersFormConfig, open: false };
    }
    onWorkerRowClicked(row: any) {
        let cbuId;
        // If the phase exist in phase start list i.e. it is ACTIVE, EOFI, EOPI, EOP then pick from start period id
        if (WORKER_START_PHASES.includes(row?.detail?.phase)) {
            cbuId = this.updatedDateCBU.id;
        } else {
            cbuId = this.updatedDateNextCBU.id;
        }
        // Reset tab to phase runs
        this.activeWorkerTab = this.workerTabs.PHASE_RUNS;
        this.workerDetails = { ...row?.detail, open: true, cbuId };
        this.workerTabArray[1].label = `${i18n.t('monitoringDashboard.workerTabs.failedLedgers')}`;
    }
    onWorkerDetailsSheetClose() {
        this.workerDetails = { ...this.workerDetails, open: false };
    }
    /**
     * Gets called when the retry run is executed successfully
     */
    /*  retryRunSuccess() {TODO} */

    /* Gets called when run eod or retry eod is successfull, so we have to refresh the request history tab to fetch the latest task and status
     */
    refreshRequestHistory() {
        this.requestHistoryRefreshCounter++;
    }
    /**
     * Gets called when route is changed or browser reloaded
     * Basically clear all async events running in the background
     */
    beforeDestroy() {
        if (this.timer) {
            clearInterval(this.timer);
        }
        this.phaseCycleIntervalId && clearInterval(this.phaseCycleIntervalId);
    }

    private getChecklistAlertMessage(checklists: Checklist[] | undefined) {
        const pendingChecklists = checklists?.filter(
            (checklist) =>
                ![CHECKLIST_STATUS.ALLOW, CHECKLIST_STATUS.FORCE_ALLOWED].includes(checklist?.report?.status?.value) &&
                checklist?.phase === this.getCurrentPhase,
        );
        if (pendingChecklists && pendingChecklists?.length !== 0) {
            this.failureAlertActive = true;
            // Grouping checklist based on status
            const groupChecklistByStatus = pendingChecklists.reduce(
                (groupedChecklist: { [k: string]: Checklist[] }, checklist) => {
                    const { status } = checklist;
                    groupedChecklist[status] = groupedChecklist[status] || [];
                    groupedChecklist[status].push(checklist);
                    return groupedChecklist;
                },
                {},
            );

            const checklistStatusArray: Array<string> = [];
            // The groupedChecklist is then iterated over to form an alert message for checklist status updates.
            Object.keys(groupChecklistByStatus).forEach((groupedChecklistStatus: string, index: number) => {
                const statusCount = groupChecklistByStatus[groupedChecklistStatus].length;
                index === 0
                    ? checklistStatusArray.push(
                          `${statusCount} ${i18n.tc(
                              'monitoringDashboard.checklistItem',
                              statusCount,
                          )} ${groupedChecklistStatus.toLowerCase()}`,
                      )
                    : checklistStatusArray.push(`${statusCount} ${groupedChecklistStatus.toLowerCase()}`);
            });
            return checklistStatusArray.join(', ');
        }
    }
    // Get the pending checklists which are not in allowed or force allowed state only for current phase
    private async getChecklists(event: CustomEvent) {
        const checklists: Checklist[] = event?.detail?.[0];
        // set checklist data in the store
        await this.setStateData({
            key: ROOT_STATE_CONST.CHECKLIST_TABLE_DATA,
            value: checklists,
        });
        const alertMessage = this.getChecklistAlertMessage(checklists);
        if (alertMessage) {
            this.alertMessagesMap = {
                ...this.alertMessagesMap,
                [DashboardTabs.CHECKLISTS]: {
                    message: `${this.$t('monitoringDashboard.dashboardTabs.checklist')}: ${alertMessage}`,
                    strict: true,
                },
            };
        } else {
            this.removeAlertMessage(DashboardTabs.CHECKLISTS);
        }
    }

    async onATDChecklistData(event: CustomEvent) {
        const checklists: Checklist[] = event?.detail?.[0];
        // set checklist data in the store
        await this.setStateData({
            key: ROOT_STATE_CONST.ATD_CHECKLIST_TABLE_DATA,
            value: checklists,
        });
        const alertMessage = this.getChecklistAlertMessage(checklists);
        if (alertMessage) {
            this.alertMessagesMap = {
                ...this.alertMessagesMap,
                [DashboardTabs.ATD_CHECKLISTS]: {
                    message: `${alertMessage} ${this.$t('monitoringDashboard.checklist.nonBlocker')}`,
                    strict: false,
                },
            };
        } else {
            this.removeAlertMessage(DashboardTabs.ATD_CHECKLISTS);
        }
    }

    private async getWorkersData(event: CustomEvent) {
        const workersData: WorkerDetails[] = event?.detail?.[0];
        if (workersData) {
            await this.setStateData({
                key: ROOT_STATE_CONST.WORKER_TABLE_DATA,
                value: workersData,
            });
        }
        //Filter the workers which have failed and get its count
        this.failedWorkersCount = workersData?.filter((worker: WorkerDetails) =>
            [WORKER_STATUS.FAILED].includes(worker.status),
        ).length;
        if (this.failedWorkersCount !== 0) {
            this.alertMessagesMap = {
                ...this.alertMessagesMap,
                [DashboardTabs.WORKERS]: {
                    message: `${this.$t('monitoringDashboard.dashboardTabs.workers')}: ${this.$t(
                        'monitoringDashboard.workersFailedMessage',
                        { failedWorkersCount: this.failedWorkersCount },
                    )}`,
                    strict: true,
                },
            };
        } else {
            this.removeAlertMessage(DashboardTabs.WORKERS);
        }
    }

    private removeAlertMessage(tab: keyof AlertType) {
        if (this.alertMessagesMap && this.alertMessagesMap[tab]) {
            delete this.alertMessagesMap[tab];
            this.alertMessagesMap = { ...this.alertMessagesMap };
        }
    }

    private handleChecklistClicked(event: CustomEvent) {
        const eventDetail = _.merge(CHECKLIST_ITEM_PAYLOAD_DETAILS, event?.detail);
        this.checklistClickedDetail = eventDetail;
        this.checklistClickedDetailCached = eventDetail;
        (this.checklistClickedDetail as Checklist)['currentPhase'] = this.currentCBU?.status || '';
        this.openChecklistDetailSideSheet = true;
    }

    private removeForceAllowEventListeners() {
        window.removeEventListener('ForceAllowChecklistItem', this.submitAllowChecklist);
        window.removeEventListener('GoToChecklistTask', this.handleGoToTask);
    }

    private handleChecklistAllowClicked(event: CustomEvent) {
        const eventDetail = _.merge(CHECKLIST_ITEM_PAYLOAD_DETAILS, event?.detail);
        window.addEventListener('ForceAllowChecklistItem', this.submitAllowChecklist);
        window.addEventListener('GoToChecklistTask', this.handleGoToTask);
        this.checklistClickedDetail = eventDetail;
        this.checklistClickedDetailCached = eventDetail;
        this.isChecklistModalActive = true;
    }

    private handleGoToTask() {
        this.isChecklistModalActive = false;
        this.openChecklistDetailSideSheet = true;
        this.removeForceAllowEventListeners();
    }

    /**
     * Function to display an error toast
     * @param error error of type Error or HTTPErrorResponse
     */
    private showErrorToast(error: { response?: { data: { message: string } }; message?: string }) {
        ZToast.open({
            heading: this.$t('error.generic.header'),
            description: error?.response?.data?.message || error?.message || this.$t('error.generic.message'),
            position: 'is-bottom-right',
            type: 'danger',
        });
    }
    // Function to show success toast
    private showSuccessToast(heading: string, description: string) {
        ZToast.open({
            heading: heading,
            description: description,
            position: 'is-bottom-right',
            type: 'success',
            pauseOnHover: true,
            icon: 'check-circle',
        });
    }

    private createChecklistWorkflowPayload(payload: { [key: string]: string | boolean | undefined }) {
        const {
            tenantId = '',
            baseUrl = '',
            bookDate = '',
            requestedTime = '',
            requestedBy = '',
            calendarId = '',
            calendarName = '',
            coaCode = '',
            coAName = '',
            phase = '',
            action = '',
            checklistCode = '',
            checklistName = '',
            system = '',
            originalStatus = '',
            lastUpdated = '',
            reasonCode,
            additionalComments,
            requestPayload,
            originalChecklist = '',
            actionType = '',
            cbuDate = '',
            processKey = '',
            herculesBaseUrl = '',
            eodHostName = '',
            checklistType = '',
        } = payload;
        const basePayload = {
            checklistType: {
                value: checklistType,
                type: 'String',
            },
            herculesBaseUrl: {
                value: herculesBaseUrl,
                type: 'String',
            },
            eodHostName: {
                value: eodHostName,
                type: 'String',
            },
            tenantId: {
                value: tenantId,
                type: 'String',
            },
            baseUrl: {
                value: baseUrl,
                type: 'String',
            },
            bookDate: {
                value: bookDate,
                type: 'String',
            },
            requestedTime: {
                value: requestedTime,
                type: 'String',
            },
            requestedBy: {
                value: requestedBy,
                type: 'String',
            },
            calendarId: {
                value: calendarId,
                type: 'String',
            },
            calendarName: {
                value: calendarName,
                type: 'String',
            },
            coaCode: {
                value: coaCode,
                type: 'String',
            },
            coAName: {
                value: coAName,
                type: 'String',
            },
            phase: {
                value: phase,
                type: 'String',
            },
            action: {
                value: action,
                type: 'String',
            },
            checklistCode: {
                value: checklistCode,
                type: 'String',
            },
            checklistName: {
                value: checklistName,
                type: 'String',
            },
            system: {
                value: system,
                type: 'String',
            },
            originalStatus: {
                value: originalStatus,
                type: 'String',
            },
            lastUpdated: {
                value: lastUpdated,
                type: 'String',
            },
            reasonCode: {
                value: reasonCode,
                type: 'String',
            },
            additionalComments: {
                value: additionalComments,
                type: 'String',
            },
            requestPayload: {
                value: requestPayload,
                type: 'String',
            },
            originalChecklist: {
                value: originalChecklist,
                type: 'String',
            },
            actionType: {
                value: actionType,
                type: 'String',
            },
            cbuDate: {
                value: cbuDate,
                type: 'String',
            },
            processKey: {
                value: processKey,
                type: 'String',
            },
        };
        if (this.enableRD) {
            return {
                initiatorForm: basePayload,
                rdCode: this.rdMap?.['checklist'] as string,
            };
        } else {
            return {
                variables: basePayload,
            };
        }
    }

    private createScheduledEODflowPayload(payload: { [key: string]: string | boolean | undefined }) {
        const {
            tenantId = '',
            baseUrl = '',
            herculesBaseUrl = '',
            eodHostName = '',
            requestedTime = '',
            requestedBy = '',
            calendarId = '',
            isResume = false,
            scheduledJobId = '',
        } = payload;

        const basePayload = {
            tenantId: {
                value: tenantId,
                type: 'String',
            },
            baseUrl: {
                value: baseUrl,
                type: 'String',
            },
            herculesBaseUrl: {
                value: herculesBaseUrl,
                type: 'String',
            },
            eodHostName: {
                value: eodHostName,
                type: 'String',
            },
            requestedTime: {
                value: requestedTime,
                type: 'String',
            },
            requestedBy: {
                value: requestedBy,
                type: 'String',
            },
            calendarId: {
                value: calendarId,
                type: 'String',
            },
            isResume: {
                value: isResume,
                type: 'Boolean',
            },
            scheduledJobId: {
                value: scheduledJobId,
                type: 'String',
            },
        };

        if (this.enableRD) {
            return {
                initiatorForm: basePayload,
                rdCode: this.rdMap?.['scheduledEOD'] as string,
            };
        } else {
            return {
                variables: basePayload,
            };
        }
    }

    private async createChecklistWorkflow(
        action: string,
        reasonCode: string,
        additionalComments: string,
        requestPayload: string,
    ) {
        try {
            const request: RequestParams = {
                tenantId: this.$ifiId as string,
                ...(this.enableRD
                    ? { requestType: REQUEST_TYPE.BR }
                    : { processDefinitionKey: EOD_CHECKLIST_PROCESS_DEFINITION_KEY }),
            };
            const payload = this.createChecklistWorkflowPayload({
                tenantId: this.$ifiId as string,
                baseUrl: `${this.auraInternalUrl}`,
                herculesBaseUrl: this.herculesBaseUrl as unknown as string,
                eodHostName: window.location.hostname,
                bookDate: this.$d(
                    this.checklistClickedDetail?.phase && PHASES_START.includes(this.checklistClickedDetail?.phase)
                        ? dayjs(this.updatedDateCBU?.startTime).toDate()
                        : dayjs(this.updatedDateNextCBU?.startTime).toDate(),
                    'short',
                    this.locale as string,
                ),
                requestedTime: this.$d(new Date(), 'long12h', this.locale as string),
                requestedBy: this.authProfileName,
                calendarId: this.selectedCalendar?.id,
                calendarName: this.selectedCalendar?.name,
                coaCode: this.selectedCoA?.code,
                coAName: this.selectedCoA?.name,
                phase: this.checklistClickedDetail?.phase,
                action,
                checklistType:
                    this.checklistClickedDetail?.type === CHECKLIST_TYPE.MANUAL
                        ? CHECKLIST_TYPE.MANUAL
                        : CHECKLIST_TYPE.AUTOMATED,
                checklistCode: this.checklistClickedDetail?.checklistCode,
                checklistName: this.checklistClickedDetail?.name,
                system: this.checklistClickedDetail?.system,
                originalStatus: this.checklistClickedDetail?.status,
                lastUpdated: this.$d(
                    dayjs(this.checklistClickedDetail?.report?.clientProvidedModifiedAt).toDate(),
                    'long12h',
                    this.locale as string,
                ),
                reasonCode,
                additionalComments,
                requestPayload,
                originalChecklist: JSON.stringify(this.checklistClickedDetailCached),
                actionType: this.$t('monitoringDashboard.checklist.actionType') as string,
                cbuDate:
                    (this.checklistClickedDetail?.phase && PHASES_START.includes(this.checklistClickedDetail?.phase)
                        ? dayjs(this.updatedDateCBU?.startTime).format(DATE_FORMAT_YYYY_MM_DD)
                        : dayjs(this.updatedDateNextCBU?.startTime).format(DATE_FORMAT_YYYY_MM_DD)) ?? '-',
                processKey: `eodChecklist_${dayjs(this.currentBookDate).format('YYYYMMDD')}_${this.$d(
                    new Date(),
                    'long12h',
                    this.locale as string,
                )}`,
            });
            const serviceUrl = (this.enableRD ? this.rheaRequestUrl : this.workflowUrl) as unknown as string;
            let requestId = '';
            if (this.enableRD) {
                const response = await rheaService(serviceUrl).submitBusinessRequest(
                    request as RheaTypes.SubmitRequestParams,
                    payload as RheaTypes.SubmitRequestPayload,
                );
                requestId = response.data.requestId || '';
            } else {
                await opsService(serviceUrl).startProcess(
                    request as RheaOperationsCenterTypes.StartProcessParams,
                    payload as RheaOperationsCenterTypes.StartProcessPayload,
                );
            }

            this.showSuccessToast(
                this.$t('monitoringDashboard.checklist.success.tasksCompleted') as string,
                requestId
                    ? (this.$t('monitoringDashboard.scheduledEODModel.requestIdMessage', { requestId }) as string)
                    : '',
            );
        } catch (error: any) {
            this.showErrorToast(new Error(this.$t('monitoringDashboard.checklist.error.description') as string));
        }
    }

    private async submitAllowChecklist() {
        this.openChecklistDetailSideSheet = false;
        this.isChecklistModalActive = false;
        this.removeForceAllowEventListeners();
        try {
            const action = `${
                this.checklistClickedDetail?.type === CHECKLIST_TYPE.MANUAL
                    ? CHECKLIST_STATUS.ALLOW
                    : CHECKLIST_STATUS.FORCE_ALLOWED
            }_${this.checklistClickedDetail?.phase}_${this.checklistClickedDetail?.name}`;
            const reasonCode = this.checklistClickedDetail?.report?.status?.reasonCode || '';
            const additionalComments = this.checklistClickedDetail?.report?.status?.reason || '';
            /**
             * Manual checklist: mark all tasks as success and update the status as allow
             * Auto checklist: mark all tasks as skipped and update the status as force allow
             */
            if (this.checklistClickedDetail?.type === CHECKLIST_TYPE.MANUAL) {
                this.checklistClickedDetail?.report?.tasks?.forEach((task) => {
                    if (task?.status?.value) {
                        task.status.value = CHECKLIST_TASK_STATUS.SUCCESS;
                    }
                });
            } else {
                this.checklistClickedDetail?.report?.tasks?.forEach((task) => {
                    if (task?.status?.value) {
                        task.status.value = CHECKLIST_TASK_STATUS.SKIPPED;
                    }
                });
            }
            const requestPayload = JSON.stringify({
                ...this.checklistClickedDetail?.report,
                version: +(this.checklistClickedDetail?.report?.version || 0) + 1,
                status: {
                    ...this.checklistClickedDetail?.report?.status,
                    value:
                        this.checklistClickedDetail?.type === CHECKLIST_TYPE.MANUAL
                            ? CHECKLIST_STATUS.ALLOW
                            : CHECKLIST_STATUS.FORCE_ALLOWED,
                },
                attributes: {
                    ...this.checklistClickedDetail?.report?.attributes,
                    requestedBy: this.authProfileName,
                },
                clientProvidedModifiedAt: dayjs().utc().format(), // Need to send time in UTC format
            });
            await this.createChecklistWorkflow(action, reasonCode, additionalComments, requestPayload);
        } catch (error: any) {
            this.showErrorToast(new Error(this.$t('monitoringDashboard.checklist.error.description') as string));
        }
    }

    private async updateChecklistDetail(event: CustomEvent) {
        const checklist: Checklist = event.detail[0];
        // close the side sheet, otherwise it will have stale data
        this.openChecklistDetailSideSheet = false;
        try {
            // If all tasks are success, will go through the checklist workflow route to update the status
            if (
                checklist.report?.tasks?.every((task) =>
                    [CHECKLIST_TASK_STATUS.SUCCESS, CHECKLIST_TASK_STATUS.SKIPPED].includes(task.status.value),
                )
            ) {
                const action = `ALLOW_${checklist.phase}_${checklist.name}`;
                const reasonCode = this.$t('monitoringDashboard.checklist.reasonCode.allowedReasonCode') as string;
                const additionalComments = '';
                const requestPayload = JSON.stringify({
                    ...checklist.report,
                    version: +checklist.report.version + 1,
                    status: {
                        reasonCode: this.$t('monitoringDashboard.checklist.reasonCode.allowedReasonCode') as string,
                        reason: '',
                        // As this will always be for Manual checklist we assign it as allow
                        value: CHECKLIST_STATUS.ALLOW,
                    }, // Need to send time in UTC format
                    attributes: {
                        ...checklist.report.attributes,
                        requestedBy: this.authProfileName,
                    },
                    clientProvidedModifiedAt: dayjs().utc().format(),
                });
                await this.createChecklistWorkflow(action, reasonCode, additionalComments, requestPayload);
            } else {
                /** This will directly update the status by calling the verification api if there are some task which are not in terminal state
                 *   If there are some tasks which are failed move checklist status as Denied
                 */
                const isChecklistDenied = checklist?.report?.tasks?.some(
                    (task) => task?.status?.value === CHECKLIST_TASK_STATUS.FAILED,
                );
                await coaService(String(this.auraUrl)).updateStatusVerificationReport({
                    tenantId: this.$ifiId,
                    coaId: this.selectedCoA?.code,
                    cbu:
                        (this.checklistClickedDetail?.phase && PHASES_START.includes(this.checklistClickedDetail?.phase)
                            ? dayjs(this.updatedDateCBU.startTime).format(DATE_FORMAT_YYYY_MM_DD)
                            : dayjs(this.updatedDateNextCBU.startTime).format(DATE_FORMAT_YYYY_MM_DD)) ?? '-',
                    phase: checklist.phase,
                    payload: {
                        ...checklist.report,
                        version: +checklist.report.version + 1,
                        status: {
                            value: isChecklistDenied ? CHECKLIST_STATUS.DENY : CHECKLIST_STATUS.IN_PROGRESS,
                            reasonCode: isChecklistDenied
                                ? (this.$t('monitoringDashboard.checklist.reasonCode.failedReasonCode') as string)
                                : '-',
                            reason: '-',
                        },
                        attributes: {
                            ...checklist.report.attributes,
                            requestedBy: this.authProfileName,
                        },
                        // Need to send time in UTC format
                        clientProvidedModifiedAt: dayjs().utc().format(),
                    },
                });
                this.showSuccessToast(
                    this.$t('monitoringDashboard.checklist.success.checklistUpdated') as string,
                    checklist.name,
                );
            }
            await this.updateDashboard();
        } catch (error: any) {
            this.showErrorToast(new Error(this.$t('monitoringDashboard.checklist.error.description') as string));
        }
    }

    async openEOD(actionType: string, scheduleEODTime: string) {
        await this.fetchScheduleEODDetails();
        this.isEODScheduledModal = true;
        this.scheduleEODTime = scheduleEODTime;
        this.scheduleEODActionType = actionType;
    }

    /**
     * Cut off action can be manual cutoff, pause and resume cutoff schedule
     */

    isCutoffActionApprovalRequired(actionType: string) {
        const rdMapKey = CUTOFF_ACTION_RD_KEY_MAP[actionType as keyof typeof CUTOFF_ACTION_RD_KEY_MAP];
        const approvalMap = context.getAttribute('viewConfig.modules.calendar.makerCheckerRequiredMap') || {};
        return approvalMap[rdMapKey] || false;
    }
    /**
     * Gets called when the user clicks on the cut-off in actions dropdown
     */
    openCutOff(actionType: string) {
        this.cutoffConfig = {
            ...this.cutoffConfig,
            'base-url': this.tachyonCalendarUrl as unknown as string,
            'hercules-base-url': this.herculesBaseUrl as unknown as string,
            'service-url': this.rheaRequestUrl as unknown as string,
            'tenant-id': this.$ifiId as string,
            locale: this.locale as string,
            'calendar-id': this.selectedCalendar?.id as string,
            'period-id': this.currentCBU?.id as string,
            'current-book-date': this.currentBookDate,
            'calendar-name': this.selectedCalendar?.name as string,
            'requested-by': this.authProfileName,
            open: true,
            'rd-code': this.getCutoffRDCode(actionType),
            'action-type': actionType,
            'is-approval-required': this.isCutoffActionApprovalRequired(actionType),
            ...(this.getCutoffScheduleProps() as unknown as Record<string, string>),
        };
    }

    getCutoffScheduleProps() {
        const {
            id: scheduleId = '',
            name: scheduleName = '',
            code: scheduleCode = '',
            description: scheduleDescription = '',
            cronExpression: scheduleCronExpression = '',
            state: scheduleStatus = '',
            scheduleTime = '',
        } = this.scheduleDetails || {};
        const { clockID: clockId = '', cycleID: cbuCycleId = '', liveProcessingState = '' } = this.currentCBU || {};
        return {
            'schedule-id': scheduleId,
            'schedule-code': scheduleCode,
            'schedule-description': scheduleDescription,
            'schedule-cron-expression': scheduleCronExpression,
            'schedule-name': scheduleName,
            'clock-id': clockId,
            'cbu-cycle-id': cbuCycleId,
            'schedule-status': scheduleStatus,
            'live-processing-state': liveProcessingState,
            'schedule-time': scheduleTime,
        };
    }
    getCutoffRDCode(actionType: string) {
        switch (actionType) {
            case ACTION_MANUAL_CUTOFF:
                return this.rdMap?.['cutoff'] as string;
            case ACTION_CREATE_CUTOFF_SCHEDULE:
                return this.rdMap?.['createCutoffSchedule'] as string;
            case ACTION_EDIT_CUTOFF_SCHEDULE:
                return this.rdMap?.['editCutoffSchedule'] as string;
            case ACTION_DELETE_CUTOFF_SCHEDULE:
                return this.rdMap?.['deleteCutoffSchedule'] as string;
            case ACTION_PAUSE_CUTOFF_SCHEDULE:
                return this.rdMap?.['pauseCutoffSchedule'] as string;
            case ACTION_RESUME_CUTOFF_SCHEDULE:
                return this.rdMap?.['resumeCutoffSchedule'] as string;
            default:
                return this.rdMap?.['cutoff'] as string;
        }
    }
    closeCutoffModal() {
        this.cutoffConfig = { ...this.cutoffConfig, open: false };
    }
}
</script>

<style lang="scss">
@mixin center-align {
    display: flex;
    justify-content: center;
    align-items: center;
}
.error-text {
    color: #c50c0c;
}

.eod-process__modal__content {
    padding: 8px;
}
.eod-modal__error {
    .modal-card {
        width: 450px;
        min-height: 225px;
    }
    .modal-card-body {
        //padding-top: 25px;
    }
    .z-utility-header {
        padding-bottom: 0px;
    }
}
.dashboard-container {
    &__tabs {
        .z-layout-main {
            height: auto;
        }
    }
    .warning-alert {
        padding: 0 !important;
    }

    .alerts-container {
        padding: 1rem 2rem 0 2rem;

        .alert-item {
            margin-bottom: 8px;
            color: #434b79;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
    &__state {
        margin-top: 1em;
        display: flex;
        height: calc(100vh - 350px);
    }
    &__widget-content {
        height: calc(100vh - 115px);
        position: relative;

        &__worker-details {
            margin-top: 1.5rem;
        }
        .coa-tabs {
            padding: 2rem 0 0 2rem;
        }
        .z-segmented-control {
            .icon {
                color: #f04f6d;
                .gds {
                    height: 10px;
                    width: 10px;
                    font-size: 10px;
                    line-height: 10px;
                }
            }
        }
        .z-alert {
            padding: 1rem 2rem 0 2rem;
            .media {
                align-items: center;
                .media-content {
                    display: flex;
                }
            }
            .alert-title {
                color: var(--alert-description-text-color, #434b79);
                font: var(--body-medium-long-r, 400 14px/20px IBM Plex Sans);
            }
        }
    }
    &__loader {
        position: absolute;
        height: 0;
        width: 0;
        &.active {
            width: 100%;
            display: flex;
            justify-content: center;
            padding-top: 200px;
            position: absolute;
            z-index: 10;
            height: calc(100vh - 115px);
            background: rgba(255, 255, 255, 0.5);
        }
    }
    &__eod-modal {
        &__body {
            @include center-align();
            flex-direction: column;
            &__image-container {
                @include center-align();
                background: #ffefdf;
                width: 100%;
            }
            .z-component.z-utility-header .utility-mid-section {
                @include center-align();
                flex-direction: column;
            }
        }
        .z-utility-footer.z-component {
            box-shadow: unset;
        }
    }
    &__checklist-modal {
        &__body {
            padding: 24px;
        }
        &__footer {
            padding-top: 24px;
        }
    }
    .coa-balances-summary {
        margin-top: var(--space-5);
        .coa-balances-summary-header {
            margin-top: var(--space-5);
            font-size: var(--font-size-l);
            font-family: var(--family-default);
            font-weight: var(--weight-medium);
            line-height: var(--line-height-l);
            margin-bottom: var(--space-5);
        }
    }
    .worker-list-container,
    .request-history-container {
        height: calc(100vh - 330px);
        overflow-y: auto;
    }
    .worker-details-sheet,
    .worker-master-list-sheet {
        padding: 0 24px;
    }
    .worker-details-sheet {
        --coa-batch-run-list-height-offset: 425px;
    }
    .batch-run-list-side-sheet {
        --coa-batch-run-list-height-offset: 210px;
    }
    .periods-list-wrapper {
        overflow: hidden;
        width: 100%;
        padding: 12px 24px;
    }
    .coa-failed-ledger-sidesheet {
        .sheet-body {
            overflow: hidden;
        }
    }
    /* Define the enter/leave transitions */
    .fade-enter-active,
    .fade-leave-active {
        transition: opacity 0.3s;
    }
    .fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
        opacity: 0;
    }
}
section.z-default-tab.z-component {
    width: 100%;
}
#checklist-detail-side-sheet {
    .sheet-content {
        width: 65%;
    }
}
</style>
