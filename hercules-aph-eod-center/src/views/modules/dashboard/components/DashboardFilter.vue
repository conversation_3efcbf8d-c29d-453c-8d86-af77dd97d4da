<template>
    <div>
        <section class="filter-container">
            <div class="calendar-selector">
                <!-- When calendars are loading, disable the filter selector -->
                <!-- TODO: Add a disabled attribute: https://zeta-tm.atlassian.net/browse/WIR-187 -->
                <z-field :disabled="isCalendarDisabled">
                    <z-auto-complete
                        :data="filteredCalendars"
                        :placeholder="$t('monitoringDashboard.selectCalendar')"
                        v-model="autoCompleteName"
                        field="name"
                        @select="calendarSelected"
                        @focus="calendarInputFocus"
                        @blur="calendarInputFocused = false"
                        :rightIcon="calendarInputFocused ? 'arrow-drop-up' : 'arrow-drop-down'"
                    ></z-auto-complete>
                </z-field>
            </div>
            <div class="date-selector">
                <z-row :gutterLess="true">
                    <b-button
                        class="z-button z-component default previous"
                        type="secondary"
                        size="is-medium"
                        iconLeft="keyboard-arrow-left"
                        :disabled="isDatePickerDisabled"
                        @click="previousDate"
                    />
                    <z-tooltip :active="true" :isCustomContainer="false" position="is-top" textAlignment="center">
                        <template slot="default">
                            <z-date-picker
                                :maxDate="getMaxDate"
                                :inline="false"
                                placeholder="Select a date"
                                locale=""
                                closeOnClick
                                name="datepicker"
                                :value="getDate()"
                                :disabled="isDatePickerDisabled"
                                :dateFormatter="dateFormatter"
                                @input="setSelectedDate"
                            />
                        </template>
                        <template slot="content">
                            {{ $t('common.bookDate') }}
                        </template>
                    </z-tooltip>
                    <b-button
                        class="z-button z-component default next"
                        type="secondary"
                        size="is-medium"
                        iconLeft="keyboard-arrow-right"
                        :disabled="isDatePickerDisabled || nextDateButtonDisabled"
                        @click="nextDate"
                    />
                </z-row>
            </div>
            <div v-if="currentCBU" class="current-book-date-selector">
                <div v-if="showLabel" class="current-book-date-selector__badge">
                    <z-badge :label="$t('monitoringDashboard.currentBookDateBadge')" type="neutral" />
                </div>
                <div v-else class="current-book-date-selector__button">
                    <z-component>
                        <b-button
                            class="z-button z-component default"
                            type="secondary"
                            size="is-medium"
                            :label="$t('monitoringDashboard.currentBookDate') + ': ' + getButtonLabel"
                            :disabled="isDatePickerDisabled"
                            @click="switchToCurrentBookDate()"
                        />
                    </z-component>
                </div>
            </div>
            <div
                v-if="!(cutoffScheduleDetails && cutoffScheduleDetails.isLoading) && showCutoff && isAdminRole"
                class="scheduled-cutoff-section"
            >
                <z-pop-over
                    class="scheduled-cutoff-section__popover"
                    v-bind="cutoffActionsPopOverConfig"
                    v-if="isCutoffScheduled && showLabel"
                    @activeChange="cutoffActionsPopOverChange"
                >
                    <template #trigger-element>
                        <span class="scheduled-cutoff-section__popover__trigger-element" v-if="isCutoffExecuted">
                            <z-icon type="is-success" icon="check-circle-outline"></z-icon>
                            {{ $t('monitoringDashboard.cutoff') }}: {{ $t('monitoringDashboard.executed') }}
                        </span>
                        <span class="scheduled-cutoff-section__popover__trigger-element" v-else>
                            <z-icon v-if="isCutoffScheduleActive" type="is-neutral" icon="access-time"></z-icon>
                            <z-icon
                                v-else-if="isCutoffSchedulePaused"
                                type="is-danger"
                                icon="pause-circle-outline"
                            ></z-icon>
                            {{ $t('monitoringDashboard.cutoff') }}:
                            {{ isCutoffScheduleActive ? $t('monitoringDashboard.scheduledAt') : '' }}
                            {{ cutoffScheduleTime }}
                        </span>
                    </template>
                    <template #content>
                        <div
                            v-if="isCutoffPopoverOpen"
                            class="scheduled-cutoff-section__popover__content"
                            :class="popoverContentClass"
                        >
                            <section class="scheduled-cutoff-section__popover__content__section">
                                <template v-if="isCutoffExecuted">
                                    <span
                                        class="scheduled-cutoff-section__popover__content__section__text"
                                        v-if="isCutoffScheduleActive"
                                        v-html="
                                            $t('monitoringDashboard.scheduleActiveCutOffExecutedText', {
                                                scheduleTime: cutoffScheduleTime,
                                                nextBookDate,
                                            })
                                        "
                                    ></span>
                                    <span
                                        class="scheduled-cutoff-section__popover__content__section__text"
                                        v-else-if="isCutoffSchedulePaused"
                                        v-html="
                                            $t('monitoringDashboard.schedulePausedCutOffExecutedText', {
                                                scheduleTime: cutoffScheduleTime,
                                            })
                                        "
                                    ></span>
                                </template>
                                <template v-else>
                                    <span
                                        class="scheduled-cutoff-section__popover__content__section__text"
                                        v-if="isCutoffScheduleActive"
                                        v-html="
                                            $t('monitoringDashboard.scheduleActiveCutOffPendingText', {
                                                scheduleTime: cutoffScheduleTime,
                                                currentBookDate,
                                                nextBookDate,
                                            })
                                        "
                                    ></span>
                                    <span
                                        class="scheduled-cutoff-section__popover__content__section__text"
                                        v-else-if="isCutoffSchedulePaused"
                                        v-html="
                                            $t('monitoringDashboard.schedulePausedCutOffPendingText', {
                                                scheduleTime: cutoffScheduleTime,
                                            })
                                        "
                                    ></span>
                                </template>
                            </section>
                            <footer v-if="enableManualCutoff || enablePauseResumeCutoff" class="scheduled-cutoff-section__popover__content__footer scheduled-cutoff-footer">
                                <div class="scheduled-cutoff-section__popover__content__footer__actions-primary">
                                    <template v-if="isCutoffExecuted">
                                        <z-button
                                            v-if="isCutoffScheduleActive && enablePauseResumeCutoff"
                                            @click="() => onCutoffActionClick(ACTION_PAUSE_CUTOFF_SCHEDULE)"
                                            icon-left="pause-circle-filled"
                                            type="primary"
                                            size="medium"
                                            >{{ $t('monitoringDashboard.pauseSchedule') }}</z-button
                                        >
                                        <z-button
                                            v-else-if="isCutoffSchedulePaused && enablePauseResumeCutoff"
                                            @click="() => onCutoffActionClick(ACTION_RESUME_CUTOFF_SCHEDULE)"
                                            icon-left="play-circle-filled"
                                            type="primary"
                                            size="medium"
                                            >{{ $t('monitoringDashboard.resumeSchedule') }}</z-button
                                        >
                                    </template>
                                    <template v-else>
                                        <z-button
                                            @click="() => onCutoffActionClick(ACTION_MANUAL_CUTOFF)"
                                            v-if="isLiveProcessingOpen && enableManualCutoff"
                                            icon-left="stop-circle"
                                            type="primary   
                                        "
                                            size="medium"
                                            >{{ $t('monitoringDashboard.cutoffNow') }}</z-button
                                        >
                                    </template>
                                </div>
                                <div class="scheduled-cutoff-section__popover__content__footer__actions-secondary">
                                    <template v-if="!isCutoffExecuted">
                                        <z-button
                                            v-if="isCutoffScheduleActive && enablePauseResumeCutoff"
                                            @click="() => onCutoffActionClick(ACTION_PAUSE_CUTOFF_SCHEDULE)"
                                            icon-left="pause-circle-outline"
                                            type="secondary"
                                            size="medium"
                                            >{{ $t('monitoringDashboard.pauseSchedule') }}</z-button
                                        >
                                        <z-button
                                            v-else-if="isCutoffSchedulePaused && enablePauseResumeCutoff"
                                            @click="() => onCutoffActionClick(ACTION_RESUME_CUTOFF_SCHEDULE)"
                                            icon-left="play-circle-outline"
                                            type="secondary"
                                            size="medium"
                                            >{{ $t('monitoringDashboard.resumeSchedule') }}</z-button
                                        >
                                    </template>
                                </div>
                            </footer>
                        </div>
                    </template>
                </z-pop-over>
                <div v-else-if="selectedPeriodLiveProcessingState" class="scheduled-cutoff-section__execution-state">
                    <template v-if="selectedPeriodLiveProcessingState === 'CUTOFF'">
                        <z-icon type="is-success" icon="check-circle-outline"></z-icon>
                        <span class="scheduled-cutoff-section__execution-state__text">
                            {{ $t('monitoringDashboard.cutoff') }}: {{ $t('monitoringDashboard.executed') }}</span
                        >
                    </template>
                </div>
            </div>
            <div class="refresh-section" v-if="selectedCalendar && currentCBU && selectedCoA">
                <!-- Timer text -->
                <z-text class="countdown-timer"
                    >{{ $t('calendarManagement.lastUpdated') }} {{ lastUpdatedTimestamp }}</z-text
                >
                <!-- Refresh icon -->
                <z-button icon-left="refresh" type="tertiary" @click="updateDashboard" />
            </div>
            <!-- Actions dropdown -->
            <div class="filter-container__actions" v-if="selectedCalendarID && currentCBU && selectedCoA">
                <!-- TODO: Disabled attribute is not working properly here as well -->
                <!-- <z-select
                :listItems="actionsDropdownList"
                :isSelector="true"
                :appendToBody="false"
                position="is-bottom-left"
                v-model="selectedActionItem"
                @change="actionItemClickHandler"
            >
                <template v-slot:trigger-element>
                    <z-component
                        ><z-button type="primary" size="medium" @click="isActionsOpen = !isActionsOpen"
                            >{{ $t('monitoringDashboard.actionsTitle') }}
                        </z-button>
                    </z-component>
                </template></z-select
            > -->
                <z-pop-over v-bind="actionsPopOverConfig" @activeChange="actionsPopOverChange">
                    <template #trigger-element="{ active }"
                        ><z-component
                            ><z-button
                                type="primary"
                                size="medium"
                                :icon-right="active ? 'arrow-drop-up' : 'arrow-drop-down'"
                                >{{ $t('monitoringDashboard.actionsTitle') }}</z-button
                            ></z-component
                        ></template
                    >
                    <template #content>
                        <z-list
                            v-if="isActionsOpen"
                            :listItems="actionsDropdownList"
                            :selectedItems="actionsSelectedItem"
                            @selectedItem="actionItemClickHandler"
                        >
                        </z-list>
                    </template>
                </z-pop-over>
            </div>
        </section>

        <div class="filter-container">
            <div
                v-if="
                    !(scheduledEODDetails && scheduledEODDetails.isLoading) &&
                    showLabel &&
                    enableScheduleEOD &&
                    scheduledEODDetails.data
                "
                class="scheduled-cutoff-section"
            >
                <z-pop-over
                    class="scheduled-cutoff-section__popover"
                    v-bind="scheduledEODActionsPopOverConfig"
                    @activeChange="scheduledEODPopOverChange"
                >
                    <template #trigger-element>
                        <span class="scheduled-eod-text">
                            <Strong class="scheduled-eod-time">{{ $t('monitoringDashboard.phaseTimelines') }} </Strong>

                            <z-icon
                                class="scheduled-eod-icon"
                                v-if="currentCBU && currentCBU.status !== 'ACTIVE'"
                                type="is-primary"
                                icon="loading"
                            ></z-icon>

                            <z-icon
                                class="scheduled-eod-icon"
                                v-else-if="scheduledEODDetails.data.status === 'PAUSED'"
                                type="is-danger"
                                icon="pause-circle"
                            ></z-icon>
                            <z-icon
                                class="scheduled-eod-icon"
                                v-else-if="scheduledEODDetails.data.status === 'ACTIVE'"
                                type="is-primary"
                                icon="timer"
                            ></z-icon>

                            <span class="scheduled-eod-sub-text" v-if="currentCBU && currentCBU.status !== 'ACTIVE'">
                                {{ $t('monitoringDashboard.eodProgress') }}
                            </span>

                            <span class="scheduled-eod-sub-text" v-else>
                                {{ $t('monitoringDashboard.eodScheduled') }}
                                {{ scheduledEODDetails.data.scheduleTime }}</span
                            >

                            <!-- <z-icon class="scheduled-eod-clock"   type="is-primary" icon="timer"></z-icon> -->
                        </span>
                    </template>
                    <template #content>
                        <div
                            v-if="isScheduledEODPopoverOpen"
                            class="scheduled-cutoff-section__popover__content"
                            :class="popoverContentClass"
                        >
                            <section class="scheduled-cutoff-section__popover__content__section">
                                <span
                                    class="scheduled-cutoff-section__popover__content__section__text"
                                    v-if="scheduledEODDetails.data.status === 'PAUSED'"
                                    v-html="$t('monitoringDashboard.pauseScheduledEODescription', { scheduleEODTime })"
                                ></span>
                                <span
                                    class="scheduled-cutoff-section__popover__content__section__text"
                                    v-else-if="scheduledEODDetails.data.status === 'ACTIVE'"
                                    v-html="
                                        $t('monitoringDashboard.activeScheduledEODescription', {
                                            scheduleEODTime,
                                            currentBookDate,
                                        })
                                    "
                                ></span>
                            </section>
                            <footer class="scheduled-cutoff-section__popover__content__footer">
                                <div class="scheduled-cutoff-section__popover__content__footer__actions-primary">
                                    <z-button
                                        v-if="
                                            enableScheduleEOD &&
                                            scheduledEODDetails.data &&
                                            scheduledEODDetails.data.status === 'ACTIVE'
                                        "
                                        @click="() => scheduledEod('pause', scheduleEODTime)"
                                        icon-left="pause-circle-outline"
                                        type="primary"
                                        size="medium"
                                        >{{ $t('monitoringDashboard.pauseSchedule') }}</z-button
                                    >
                                    <z-button
                                        v-if="
                                            enableScheduleCutoff &&
                                            scheduledEODDetails.data &&
                                            scheduledEODDetails.data.status === 'PAUSED'
                                        "
                                        @click="() => scheduledEod('resume', scheduleEODTime)"
                                        icon-left="play-circle-outline"
                                        type="primary"
                                        >{{ $t('monitoringDashboard.resumeSchedule') }}</z-button
                                    >
                                </div>
                            </footer>
                        </div>
                    </template>
                </z-pop-over>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { Buefy, BUEFY_DEFAULT_CONFIG, ZTooltip } from '@zeta-design/galaxy/core';
import AuraFinanceCenter from '@/EODCenter';
import {
    DEFAULT_SELECTED_CALENDAR_COA_COUNT,
    DEFAULT_SELECTED_CALENDAR_COUNT,
    ROOT_STATE_CONST,
} from '../../../../store/types';
import {
    Calendar,
    Coa,
    DashboardActionListItem,
    SetCalendarCoAPhaseModel,
    SetCalendarStateModel,
    SetSelectedDateModel,
    GRAFANA_PARAMS,
    EODRunInfo,
    CBUResponse,
    TimePeriod,
} from '@/types';
import { context } from '@hercules/context';
import { gdsMapSelect, removeQueryParams } from '@/common/utils';
import { CalendarTypes } from '@zeta/service-clients/lib/tachyon/calendar-management/types';
import { zetaDesignGalaxy } from '@zeta-design/galaxy/index';
import {
    ACTION_SKIP_LEDGERS,
    ACTION_START_RUN,
    ACTION_VIEW_GRAFANA,
    ACTION_VIEW_PERIODS,
    ACTION_VIEW_CLEARING_STATUS,
    DASHBOARD_ACTIONS_DROPDOWN_LIST,
    ACTION_VIEW_WORKER_MASTER_LIST,
    USER_PREFERENCES_CONST,
    ACTIONS_POPOVER_CONFIG,
    ACTION_START_PHASE_RUN,
    ACTION_MANUAL_CUTOFF,
    ACTION_CREATE_CUTOFF_SCHEDULE,
    ACTION_DELETE_CUTOFF_SCHEDULE,
    ACTION_EDIT_CUTOFF_SCHEDULE,
    ACTION_PAUSE_CUTOFF_SCHEDULE,
    ACTION_RESUME_CUTOFF_SCHEDULE,
    CUTOFF_ACTIONS_POPOVER_CONFIG,
    SCHEDULED_EOD_ACTIONS_POPOVER_CONFIG,
} from '../constants';
import {
    DEFAULT_BOOK_DATE_FORMAT,
    STATUS,
    ACTIVE_PHASE,
    PHASES_START,
    DATE_FORMAT_YYYY_MM_DD,
    INACTIVE,
    CALENDAR_DATE_FORMAT,
} from '@/common/constants';
import i18n from '../../../../i18n';
import { ZToast } from '@zeta-design/galaxy/toast';
import { GrafanaDashboardParams } from '@/types/calendar';
import { GrafanaDashboardDefaultParameters } from '../../calendar/constants';
import { addQueryParams } from '../../calendar/utils';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { getDateFormatType } from '../utils';
import { ZIcon } from '@zeta-design/galaxy/core';
dayjs.extend(timezone);
dayjs.extend(utc);

Vue.use(zetaDesignGalaxy, Buefy, BUEFY_DEFAULT_CONFIG);
@Component({
    name: 'DashboardFilter',
    components: {
        ZToast,
        ZTooltip,
        ZIcon,
    },
})
export default class DashboardFilter extends AuraFinanceCenter {
    @Prop(Number) private eodRunStartDate!: number | null;
    @Prop(Date) private refreshTimestamp!: Date;
    @Prop(Number) private updatedBookDateOnEODCompletion!: number;
    private selectedDate: number | null = null;
    private showCoAPrompt = false;
    private autoCompleteName = '';
    actionsDropdownList: DashboardActionListItem[] = DASHBOARD_ACTIONS_DROPDOWN_LIST;
    actionsPopOverConfig = ACTIONS_POPOVER_CONFIG;
    cutoffActionsPopOverConfig = CUTOFF_ACTIONS_POPOVER_CONFIG;
    scheduledEODActionsPopOverConfig = SCHEDULED_EOD_ACTIONS_POPOVER_CONFIG;
    ACTION_MANUAL_CUTOFF = ACTION_MANUAL_CUTOFF;
    ACTION_CREATE_CUTOFF_SCHEDULE = ACTION_CREATE_CUTOFF_SCHEDULE;
    ACTION_DELETE_CUTOFF_SCHEDULE = ACTION_DELETE_CUTOFF_SCHEDULE;
    ACTION_EDIT_CUTOFF_SCHEDULE = ACTION_EDIT_CUTOFF_SCHEDULE;
    ACTION_PAUSE_CUTOFF_SCHEDULE = ACTION_PAUSE_CUTOFF_SCHEDULE;
    ACTION_RESUME_CUTOFF_SCHEDULE = ACTION_RESUME_CUTOFF_SCHEDULE;
    actionsSelectedItem: number[] | string[] = [];
    private calendarInputFocused = false;
    private refreshDate = new Date();
    // Grafana
    private grafanaDashboardParams: GrafanaDashboardParams | undefined = context.getAttribute(
        'viewConfig.modules.calendar.grafanaDashboardParams',
    );
    private grafanaParameters: {
        [key in GRAFANA_PARAMS]?: string;
    } = {};

    @Watch('refreshTimestamp')
    private updateRefreshTimestamp() {
        this.refreshDate = this.refreshTimestamp || new Date();
    }

    // Reverted changes for feature - Show EOD Completion UI #NothingMatters #WhatIsLife
    // @Watch('updatedBookDateOnEODCompletion')
    // private updateBookDateOnEODCompletion() {
    //     // When say from updateDashboard in MonitoringDashboad, the current cbu has moved to active start (EOD has been completed)
    //     // then we need selectedDate value here to be updated as well
    //     this.selectedDate = dayjs(this.updatedBookDateOnEODCompletion).valueOf();
    // }

    private get lastUpdatedTimestamp() {
        return i18n.d(this.refreshDate, 'long12h', this.locale as string);
    }

    isActionsOpen = false;
    isCutoffPopoverOpen = false;
    isScheduledEODPopoverOpen = false;
    private get nextDateButtonDisabled(): boolean {
        if (this.currentCBU?.startTime && this.selectedDate) {
            return PHASES_START.includes(this.currentCBU.status)
                ? this.currentCBU.startTime <= this.selectedDate
                : dayjs(this.currentCBU.startTime).subtract(1, 'day').valueOf() <= this.selectedDate;
        }
        return false;
    }
    private get getMaxDate(): Date {
        /*
         * TODO: Need to pass the max date keeping in check with the timezone.
         * z-date-picker does not by default enable max date as selectable from popover
         */
        if (this.currentCBU?.startTime) {
            return PHASES_START.includes(this.currentCBU?.status)
                ? dayjs(this.currentCBU.startTime).toDate()
                : dayjs(this.currentCBU.startTime).subtract(1, 'day').toDate();
        }
        return new Date();
    }
    private get getButtonLabel(): string {
        if (this.currentCBU?.startTime) {
            return PHASES_START.includes(this.currentCBU?.status)
                ? i18n.d(dayjs(this.currentCBU.startTime).toDate(), 'short', this.locale as string)
                : i18n.d(dayjs(this.currentCBU.startTime).subtract(1, 'day').toDate(), 'short', this.locale as string);
        }
        return '';
    }
    private get showLabel(): boolean {
        if (this.currentCBU?.status) {
            return PHASES_START.includes(this.currentCBU?.status)
                ? dayjs(this.currentCBU.startTime).format(DEFAULT_BOOK_DATE_FORMAT) ===
                      dayjs(this.selectedDate).format(DEFAULT_BOOK_DATE_FORMAT)
                : dayjs(this.currentCBU.startTime).subtract(1, 'day').format(DEFAULT_BOOK_DATE_FORMAT) ===
                      dayjs(this.selectedDate).format(DEFAULT_BOOK_DATE_FORMAT);
        }
        return false;
    }
    private get selectedCalendar(): Calendar | null {
        return this.getStateData(ROOT_STATE_CONST.SELECTED_CALENDAR);
    }
    private get isCalendarLoading(): boolean {
        return this.getStateData(ROOT_STATE_CONST.CALENDAR_LOADING);
    }
    private get calendarPhaseCoAs(): Coa[] {
        return this.getStateData(ROOT_STATE_CONST.CALENDAR_PHASE_COAS);
    }
    private get isCalendarCoALoading(): boolean {
        return this.getStateData(ROOT_STATE_CONST.CALENDAR_COA_LOADING);
    }
    private get allCoAs(): Coa[] {
        return this.getStateData(ROOT_STATE_CONST.ALL_COAS);
    }
    private get currentCBU(): (CalendarTypes.GetCurrentCBUResponse & { liveProcessingState: string }) | null {
        return this.getStateData(ROOT_STATE_CONST.CURRENT_CBU);
    }
    private get eodRunInfo(): EODRunInfo | null {
        return this.getStateData(ROOT_STATE_CONST.EOD_RUN_INFO);
    }
    private get selectedCoA(): Coa {
        return this.getStateData(ROOT_STATE_CONST.SELECTED_CALENDAR_COA);
    }
    private get isAdminRole(): boolean {
        const userRoles = this.getStateData(ROOT_STATE_CONST.USER_ROLES);
        return userRoles && userRoles.has('eodc_admin');
    }
    private get updatedDateCBU(): CBUResponse {
        return this.getStateData(ROOT_STATE_CONST.UPDATED_DATE_CBU);
    }

    private get periods(): TimePeriod[] {
        return this.getStateData(ROOT_STATE_CONST.PERIODS) || [];
    }

    private get filteredCalendars() {
        return this.allCalendars?.filter((calendar: Calendar) => {
            return calendar.name.toString().toLowerCase().indexOf(this.autoCompleteName?.toLowerCase()) >= 0;
        });
    }
    private get dateFormatter() {
        const date = this.getDate();
        if (date && date instanceof Date) {
            return getDateFormatType(i18n.d(date, 'short', this.locale as string));
        }
        return DEFAULT_BOOK_DATE_FORMAT;
    }
    // current book date of selected calendar
    public get currentBookDate() {
        return this.currentCBU ? dayjs(this.currentCBU.startTime).format(CALENDAR_DATE_FORMAT) : '';
    }

    // next book date of selected calendar
    public get nextBookDate() {
        return this.currentCBU ? dayjs(this.currentCBU.startTime).add(1, 'day').format(CALENDAR_DATE_FORMAT) : '';
    }

    private async updateDashboard() {
        /**
         * Event emit to update widgets
         * @event update_dashboard
         */
        this.$emit('update_dashboard', { invokeManualRefresh: true });
    }

    private previousDate() {
        this.setSelectedDate(dayjs(this.selectedDate).subtract(1, 'day').toDate());
    }

    private nextDate() {
        const newDate = dayjs(this.selectedDate).add(1, 'day').toDate();
        const currentDate = dayjs().toDate();
        if (newDate < currentDate) {
            this.setSelectedDate(newDate);
        }
    }

    private switchToCurrentBookDate() {
        this.currentCBU?.status && PHASES_START.includes(this.currentCBU?.status)
            ? this.setSelectedDate(dayjs(this.currentCBU?.startTime).toDate())
            : this.setSelectedDate(dayjs(this.currentCBU?.startTime).subtract(1, 'day').toDate());
    }

    private getDate() {
        // If calendar is selected, then show the selected date
        if (this.selectedDate) {
            return dayjs(this.selectedDate).toDate();
            /* If no calendar is selected,
             * then pick from currentCBU which shows the date when a fresh calendar is selected
             */
        } else if (this.currentCBU) {
            this.selectedDate = PHASES_START.includes(this.currentCBU.status)
                ? dayjs(this.currentCBU?.startTime).valueOf()
                : dayjs(this.currentCBU?.startTime).subtract(1, 'day').valueOf();
            return this.selectedDate;
        } else {
            return null;
        }
    }
    private async setSelectedDate(date: Date) {
        if (date) {
            /*
             *  This is done because the date timezone we get from the the z-date-picker is in current timezone
             *  and we need to convert it to the selected calendar timezone keeping in mind that the date should not be affected
             */
            const timezoneName = this.selectedCalendar?.timezone;
            dayjs.tz.setDefault(timezoneName);
            this.selectedDate = dayjs.tz(dayjs(date).format(DATE_FORMAT_YYYY_MM_DD)).valueOf();
            this.setActionsDropdown();
            /**
             * Set the selected date in store as timestamp
             */
            await this.setStateData({
                key: ROOT_STATE_CONST.SELECTED_CALENDAR_DATE,
                value: {
                    selectedDate: this.selectedDate,
                    selectedCalendar: this.selectedCalendar,
                    currentCBU: this.currentCBU,
                    auraUrl: `${this.auraUrl}`,
                    timezone: this.selectedCalendar?.timezone,
                } as SetSelectedDateModel,
            });
            /**
             * Event to update the widgets
             */
            this.$emit('update_dashboard', { invokeManualRefresh: true });
        }
    }

    private get selectedCalendarID() {
        return this.selectedCalendar?.id ?? null;
    }

    private get isCalendarDisabled() {
        if (this.allCalendars?.length === DEFAULT_SELECTED_CALENDAR_COUNT) {
            this.autoCompleteName = this.selectedCalendar?.name || '';
        }
        return this.isCalendarLoading || (this.allCalendars?.length === DEFAULT_SELECTED_CALENDAR_COUNT ? true : false);
    }

    private get isDatePickerDisabled() {
        return this.isCalendarLoading || this.isCalendarCoALoading || this.selectedCalendar === null;
    }

    get cutoffScheduleTime(): string | null {
        const { data, error, isLoading } = this.getStateData(ROOT_STATE_CONST.CUTOFF_SCHEDULE_DETAILS) || {};
        if (isLoading || error) {
            return null;
        } else {
            const { scheduleTime = '' } = data || {};
            return scheduleTime;
        }
    }

    get scheduleEODTime(): string | null {
        const { data, error, isLoading } = this.getStateData(ROOT_STATE_CONST.SCHEDULED_EOD_DETAILS) || {};
        if (isLoading || error) {
            return null;
        } else {
            const { scheduleTime } = data;
            return scheduleTime;
        }
    }
    get scheduledEODDetails() {
        const { isLoading = true, error, data } = this.getStateData(ROOT_STATE_CONST.SCHEDULED_EOD_DETAILS) || {};
        return { isLoading, error, data };
    }
    get isScheduleEODActive() {
        return this.scheduledEODDetails?.data?.state === 'ACTIVE';
    }
    get isScheduleEODPaused() {
        return this.scheduledEODDetails?.data?.state === 'PAUSED';
    }

    get cutoffScheduleDetails() {
        const { isLoading = true, error, data } = this.getStateData(ROOT_STATE_CONST.CUTOFF_SCHEDULE_DETAILS) || {};
        return { isLoading, error, data };
    }

    get popoverContentClass() {
        return {
            'scheduled-cutoff-section__popover__content--small':
                !this.cutoffScheduleDetails?.data && this.currentCBU?.liveProcessingState === 'OPEN',
            'scheduled-cutoff-section__popover__content--medium':
                this.cutoffScheduleDetails?.data ||
                (!this.cutoffScheduleDetails?.data && this.currentCBU?.liveProcessingState === 'CUTOFF'),
        };
    }

    get isCutoffScheduled() {
        return this.cutoffScheduleDetails?.data;
    }

    get isCutoffExecuted() {
        return this.currentCBU?.liveProcessingState === 'CUTOFF';
    }

    get isCutoffNotSet() {
        return !this.cutoffScheduleDetails?.data && this.currentCBU?.liveProcessingState === 'OPEN';
    }
    get isCutoffExecutedScheduleNotSet() {
        return !this.cutoffScheduleDetails?.data && this.currentCBU?.liveProcessingState === 'CUTOFF';
    }
    get isCutoffSetDescription() {
        return this.currentCBU?.liveProcessingState === 'OPEN' && this.cutoffScheduleDetails?.data?.state === 'ACTIVE';
    }
    get isCutoffExecutedScheduleActiveDescription() {
        return (
            this.currentCBU?.liveProcessingState === 'CUTOFF' && this.cutoffScheduleDetails?.data?.state === 'ACTIVE'
        );
    }
    get isCutoffExecutedSchedulePausedDescription() {
        return (
            this.currentCBU?.liveProcessingState === 'CUTOFF' && this.cutoffScheduleDetails?.data?.state === 'PAUSED'
        );
    }
    get isCutoffNotExecutedSchedulePausedDescription() {
        return this.currentCBU?.liveProcessingState === 'OPEN' && this.cutoffScheduleDetails?.data?.state === 'PAUSED';
    }
    get isCutoffActivePrimary() {
        return (
            this.currentCBU?.liveProcessingState === 'CUTOFF' && this.cutoffScheduleDetails?.data?.state === 'ACTIVE'
        );
    }
    get isCutoffPausedPrimary() {
        return (
            this.currentCBU?.liveProcessingState === 'CUTOFF' && this.cutoffScheduleDetails?.data?.state === 'PAUSED'
        );
    }
    get isLiveProcessingOpen() {
        return this.currentCBU?.liveProcessingState === 'OPEN';
    }
    get isLiveProcessingCutoff() {
        return this.currentCBU?.liveProcessingState === 'CUTOFF';
    }
    get isCutoffScheduleActive() {
        return this.cutoffScheduleDetails?.data?.state === 'ACTIVE';
    }
    get isCutoffSchedulePaused() {
        return this.cutoffScheduleDetails?.data?.state === 'PAUSED';
    }

    get selectedPeriodLiveProcessingState() {
        const isSelectedDateSameAsCurrentBookDate = this.showLabel;
        if(isSelectedDateSameAsCurrentBookDate) {
            return this.currentCBU?.liveProcessingState;
        }else{
            return this.periods.find(
                ({ startTime }) =>
                    dayjs(this.selectedDate).format(DEFAULT_BOOK_DATE_FORMAT) ===
                    dayjs(startTime).format(DEFAULT_BOOK_DATE_FORMAT),
            )?.liveProcessingState;
        }
    }

    private isSelectedDateSameAsCurrentBookDate() {
        return (
            this.currentCBU?.status &&
            PHASES_START.includes(this.currentCBU?.status) &&
            dayjs(this.selectedDate).format(DEFAULT_BOOK_DATE_FORMAT) ===
                dayjs(this.currentCBU?.startTime).format(DEFAULT_BOOK_DATE_FORMAT)
        );
    }

    private calendarInputFocus() {
        this.calendarInputFocused = true;
    }

    /**
     * Function to change the selected calendar state when
     * user changes the calendar in the dropdown filter
     * @param selectEvent calendar selected
     * @param initialLoadData object containing information on initial load, e.g. selected date
     */
    private async calendarSelected(
        selectEvent: Calendar,
        initialLoadData: { selectedDate: number | null } = { selectedDate: null },
    ) {
        this.calendarInputFocused = false;
        if (!selectEvent) {
            return;
        }
        this.showCoAPrompt = false;
        // Set calendar store (it turns calendar loader on and off as well)
        await this.setStateData({
            key: ROOT_STATE_CONST.SELECTED_CALENDAR,
            value: selectEvent,
        });
        // Set db entity
        await this.cachePreferences({
            calendarId: selectEvent.id,
        });
        await this.setCBUandPeriods(initialLoadData);
        await this.setCalendarCoAs();
    }

    /**
     * Function to set the currentCBU and periods state
     * @param initialLoadData object containing information on initial load, e.g. selected date
     */
    private async setCBUandPeriods(initialLoadData: { selectedDate: number | null } = { selectedDate: null }) {
        // Set calendar loader flag as false
        await this.setStateData({
            key: ROOT_STATE_CONST.CALENDAR_LOADING,
            value: true,
        });
        // Set cbu and periods value -> this in turn sets currentCBU, updatedDateCBU and updatedNextDateCBU state
        await this.setStateData({
            key: ROOT_STATE_CONST.CURRENT_CBU,
            value: {
                selectedCalendar: this.selectedCalendar,
                tenantId: this.$ifiId as string,
                auraUrl: `${this.auraUrl}`,
                ...(initialLoadData.selectedDate && { selectedDate: initialLoadData.selectedDate }),
            } as SetCalendarStateModel,
        });
        // Set calendar loader flag as false
        await this.setStateData({
            key: ROOT_STATE_CONST.CALENDAR_LOADING,
            value: false,
        });
        // To reset the date when calendar is changed
        // If route preferences were provided it means it is initial load route data and calendar is selected from route
        if (!initialLoadData.selectedDate) {
            this.selectedDate = null;
        }
    }

    /**
     * Function to set the CoAs associated with the selected
     * calendar when user selects one from the Calendar dropdown manually
     */
    private async setCalendarCoAs() {
        // Set the coa flag loader
        await this.setStateData({
            key: ROOT_STATE_CONST.CALENDAR_COA_LOADING,
            value: true,
        });
        // Set the COAs linked with selected calendar
        await this.setStateData({
            key: ROOT_STATE_CONST.CALENDAR_PHASE_COAS,
            value: {
                coaList: this.allCoAs,
                calendarId: this.currentCBU?.calendarID as string,
            } as SetCalendarCoAPhaseModel,
        });
        // Select the first coa by default irrespective of the the number of coas for a calendar
        if (this.calendarPhaseCoAs?.length) {
            await this.setStateData({
                key: ROOT_STATE_CONST.SELECTED_CALENDAR_COA,
                value: this.calendarPhaseCoAs[0],
            });
            this.setActionsDropdown();
            /**
             * Event to update the widgets
             */
            this.$emit('update_dashboard');
        }
        // Set the coa flag loader
        await this.setStateData({
            key: ROOT_STATE_CONST.CALENDAR_COA_LOADING,
            value: false,
        });
    }

    /**
     * Boolean function to identify if the selected date is current book date
     * so that start/retry EOD or start phase run should be enabled
     */
    private verifyEODRunOnCurrentBookDate() {
        let currentBookDateStart = null;
        let currentBookDateEnd = null;
        // selectedDate respresent date selected in date picker
        const selectedDate = dayjs(this.getDate());

        if (this.currentCBU?.status) {
            // As selectedDate which we see in the date picker is adjusted based on the current book date.
            // For e.g if book date is 14th August and current phase is in PHASES_START,
            // then selected date is 14th August.
            // But if current phase is not in PHASES_START,
            // then selected date is book date - 1 i.e 13th August.
            // Based on this logic, we can't compare selected date and currentCBU.startTime directly
            // Either we will have to adjust selected date by adding 1 day depending upon the phase
            // or adjust book date by subtracting 1 day.
            const adjustedBookDate = PHASES_START.includes(this.currentCBU?.status)
                ? dayjs(this.currentCBU.startTime)
                : dayjs(this.currentCBU.startTime).subtract(1, 'day');
            currentBookDateStart = dayjs(adjustedBookDate).startOf('day');
            currentBookDateEnd = dayjs(adjustedBookDate).endOf('day');
        }
        if (currentBookDateStart && currentBookDateEnd && selectedDate) {
            return selectedDate.isBefore(currentBookDateStart) || selectedDate.isAfter(currentBookDateEnd);
        }
        return false;
    }

    private isManualCutoffEnabledForCBU() {
        return this.currentCBU?.liveProcessingState === 'OPEN';
    }

    /*
        Sets action dropdown item disable property based on current coa phase status
        For e.g run eod will be disabled if the phase is in progress
        Skip ledgers will only be enabled when phase is in failed state
        Also Listen for the start run date time event being emitted from phase cycle component
        If we receive that, then enable the grafana dashboard option
     */
    @Watch('currentCBU')
    private setActionsDropdown() {
        // set actions dropdown list
        this.actionsDropdownList = DASHBOARD_ACTIONS_DROPDOWN_LIST.map((item) => {
            switch (item.action) {
                case ACTION_START_RUN:
                case ACTION_START_PHASE_RUN:
                case ACTION_MANUAL_CUTOFF:
                case ACTION_PAUSE_CUTOFF_SCHEDULE:
                case ACTION_RESUME_CUTOFF_SCHEDULE: {
                    const isPastFutureDateSelected = this.verifyEODRunOnCurrentBookDate();
                    return {
                        ...item,
                        disabled:
                            !this.isAdminRole ||
                            this.currentCBU?.status === INACTIVE ||
                            isPastFutureDateSelected ||
                            (item.action === ACTION_MANUAL_CUTOFF && !this.isManualCutoffEnabledForCBU()),
                    };
                }
                case ACTION_SKIP_LEDGERS:
                    if (this.eodRunInfo?.status === STATUS.FAILED) {
                        return { ...item, disabled: !this.isAdminRole };
                    }
                    return { ...item, disabled: true };
                case ACTION_VIEW_PERIODS:
                    return { ...item, disabled: !(this.selectedCalendar && this.currentCBU) };
                case ACTION_VIEW_GRAFANA:
                    return { ...item, disabled: !this.updatedDateCBU?.phaseTransitionHistory?.['ACTIVE']?.startTime };
                default:
                    return item;
            }
        });
    }

    /**
     * Method to handle dynamic disabling of
     * COA List Items
     * @param selectedCoAs coas selected manually by user
     */
    // TODO: Disabling this "unique" mechanism since we only have single coa to deal with
    // But use cases may arrive later so keeping it here
    // private disableAdditionalCoAs(selectedCoAs: Coa[]): Coa[] {
    //     // If the user selects one CoA in the dropdown, that should be disabled by default
    //     // All the other CoAs should be enbled for the user to select
    //     if (selectedCoAs.length === DEFAULT_SELECTED_CALENDAR_COA_COUNT) {
    //         return this.calendarPhaseCoAs.map((coa) => {
    //             return {
    //                 ...coa,
    //                 disabled: selectedCoAs[0].id === coa.id,
    //             };
    //         });
    //         // If the user selects the 'allowed count' of coas, (in our case - 3),
    //         // then disable all the other CoAs
    //     } else if (selectedCoAs.length === DEFAULT_SELECTED_CALENDAR_COA_COUNT) {
    //         const selectedIds = selectedCoAs.map((coa) => coa.id);
    //         return this.calendarPhaseCoAs.map((coa) => {
    //             return {
    //                 ...coa,
    //                 disabled: !selectedIds.includes(coa.id),
    //             };
    //         });
    //     }
    //     // If the user has selected coas less than 'allowed count' but more than 1,
    //     // then enable all the other CoAs
    //     return this.calendarPhaseCoAs.map((coa) => {
    //         return {
    //             ...coa,
    //             disabled: false,
    //         };
    //     });
    // }

    onCutoffActionClick(actionType: string) {
        this.openCutOff(actionType);
    }

    actionItemClickHandler(actionItem: DashboardActionListItem) {
        const { id } = actionItem;
        const selectedItem = this.actionsDropdownList.find((item) => item.id === id);
        if (selectedItem) {
            switch (selectedItem.action) {
                case ACTION_START_RUN:
                    this.startEodRun();
                    break;
                case ACTION_START_PHASE_RUN:
                    this.startPhaseRun();
                    break;
                case ACTION_MANUAL_CUTOFF:
                    this.openCutOff(selectedItem.action);
                    break;
                case ACTION_SKIP_LEDGERS:
                    this.openSkipLedgersForm();
                    break;
                case ACTION_VIEW_PERIODS:
                    this.$emit('view_all_periods');
                    break;
                case ACTION_VIEW_GRAFANA:
                    this.displayGrafanaDashboard();
                    break;
                case ACTION_VIEW_CLEARING_STATUS:
                    window.open(
                        `${this.grafanaClearingStatusUrl}&theme=${
                            this.grafanaDashboardParams?.theme || GrafanaDashboardDefaultParameters.theme
                        }`,
                        '_blank',
                    );
                    break;
                case ACTION_VIEW_WORKER_MASTER_LIST:
                    /**
                     * open_worker_master_list event
                     */
                    this.$emit('open_worker_master_list');
                    break;
                default:
                    break;
            }
            // resetting the actionsSelectedItem to [] will clear the selected highlighte item in z-list
            this.actionsSelectedItem = [];
        }
    }

    /**
     * Method to populate the filters required for
     * grafana dashboard and add it to the URL query params
     * for opening in a new tab
     */
    private displayGrafanaDashboard() {
        try {
            /**
             * Set Grafana Dashboard parameter values
             */
            this.grafanaParameters['from'] = `${this.updatedDateCBU?.phaseTransitionHistory?.['ACTIVE']?.startTime}`;
            if (this.eodRunInfo?.status) {
                // If EOD is in progress, set the to time as current time
                if (
                    this.eodRunInfo?.status === STATUS.PROGRESS ||
                    this.eodRunInfo?.status === STATUS.FAILED ||
                    this.eodRunInfo?.status === STATUS.FAILED_RETRIED
                ) {
                    this.grafanaParameters['to'] = `${Date.now()}`;
                } else {
                    // else set the to time as to time of the end phase
                    this.grafanaParameters['to'] = `${this.eodRunInfo?.endTime || Date.now()}`;
                }
            } else {
                // if current coa phase details not present, it means no eod run, hence backup date is today
                this.grafanaParameters['to'] = `${Date.now()}`;
            }
            this.grafanaParameters['var-tenant_id'] = `${this.$ifiId}`;
            this.grafanaParameters['var-coa_id'] = this.selectedCoA?.id || '';
            this.grafanaParameters['var-cbu_id'] = (this.currentCBU?.id as string) || '';
            this.grafanaParameters['var-period_id'] = (this.currentCBU?.id as string) || '';
            this.grafanaParameters['var-phase'] = GrafanaDashboardDefaultParameters.phase;
            this.grafanaParameters['var-cbu_start_date'] =
                dayjs(this.currentCBU?.startTime || Date.now()).format('YYYY-MM-DD') || '';
            this.grafanaParameters['var-limit'] = `${GrafanaDashboardDefaultParameters.limit}`;
            this.grafanaParameters['refresh'] =
                this.grafanaDashboardParams?.refreshInterval || GrafanaDashboardDefaultParameters.refreshInterval;
            this.grafanaParameters['var-orchestra_pg_datasource'] =
                this.grafanaDashboardParams?.orchestraPgDataSource ||
                GrafanaDashboardDefaultParameters.orchestraDataSource;
            const finalUrl = `${this.grafanaUrl}&${addQueryParams(this.grafanaParameters)}&theme=${
                this.grafanaDashboardParams?.theme || GrafanaDashboardDefaultParameters.theme
            }`;
            window.open(finalUrl, '_blank');
        } catch (error) {
            // TODO: Need to identify how to convey failure scenarios gracefully to user.
            console.log('There is an error in loading Grafana Dashboard URL', error);
        }
    }

    private startEodRun() {
        const phaseStatus = this.eodRunInfo?.status;
        /**
         * open_start_run event.
         * @property {object} phaseRunEvent { phaseStatus: string; runSinglePhase: boolean }
         */
        this.$emit('open_start_run', {
            phaseStatus,
            runSinglePhase: false,
        });
    }

    private startPhaseRun() {
        const phaseStatus = this.eodRunInfo?.status;
        /**
         * open_phase_run event.
         * @property {object} phaseRunEvent { phaseStatus: string; runSinglePhase: boolean }
         */
        this.$emit('open_phase_run', {
            phaseStatus,
            runSinglePhase: true,
        });
    }

    private openSkipLedgersForm() {
        const phaseStatus = this.eodRunInfo?.status;
        /**
         * open_skip_ledgers_form event.
         *  * @property {string} phase status
         */
        this.$emit('open_skip_ledgers_form', phaseStatus);
    }

    private openCutOff(actionType: string) {
        /**
         * open_cutoff event.
         */
        this.$emit('open_cutoff', actionType);
    }

    scheduledEod(actionType: string, scheduleEODTime: string | null) {
        this.$emit('open_eod_schedule_popover', actionType, scheduleEODTime);
    }

    /**
     * Function to validate if period date is provided in the route
     * params and if it is of correct format
     * @param routePeriodDate period date provided in the route params
     */
    private prefetchPeriodDate(routePeriodDate: string) {
        let periodDate!: number;
        if (routePeriodDate) {
            const decodedPeriodDate = dayjs(decodeURIComponent(routePeriodDate)).valueOf();
            // Always check if date value is correct
            if (!decodedPeriodDate || isNaN(decodedPeriodDate)) {
                removeQueryParams(USER_PREFERENCES_CONST.PERIOD);
            } else {
                periodDate = decodedPeriodDate;
            }
        }
        return periodDate;
    }

    /**
     * Function to pre select calendar value based on the below situations:
     * 1. if calendar list is only one, then select it by default
     * 2. if calendar id is provided in route params, then select it
     * 3. if calendar id is present in db, then select it
     * @param routeCalendarId calendar id provided in route params
     */
    private async prefetchCalendarId(routeCalendarId: string) {
        const calendarList = this.allCalendars as Calendar[];
        let selectedCalendar!: Calendar | undefined;
        // check if there is only one calendar
        if (calendarList?.length === 1) {
            selectedCalendar = calendarList[0];
        } else if (routeCalendarId) {
            // or if there is calendar id in query params
            selectedCalendar = calendarList?.find((calendar: Calendar) => calendar.id === routeCalendarId) as Calendar;
            if (!selectedCalendar) {
                removeQueryParams(USER_PREFERENCES_CONST.CALENDAR); // if route is provided but calendar is not present, remove the query param
            }
        } else {
            //if no, then check if present in existing db
            const cachedCalendarId = await this.fetchPreferencesFromCache(USER_PREFERENCES_CONST.CALENDAR);
            if (cachedCalendarId) {
                selectedCalendar = this.allCalendars?.find((calendar) => calendar.id === cachedCalendarId);
            }
        }
        return selectedCalendar;
    }

    /**
     * Function to validate route parameters and set dashboard filters state
     * accordingly
     */
    private async validateRoute(): Promise<{
        selectedCalendar: Calendar | undefined;
        selectedDate: number;
    }> {
        let selectedCalendar!: Calendar | undefined;
        let selectedDate!: number;
        try {
            const { calendarId, periodDate } = this.$route.query; // query params
            selectedDate = this.prefetchPeriodDate(periodDate as string) as number;
            selectedCalendar = await this.prefetchCalendarId(calendarId as string);
        } catch (error) {
            console.log(error);
        }
        return {
            selectedCalendar,
            selectedDate,
        };
    }

    async mounted() {
        const { selectedCalendar, selectedDate } = await this.validateRoute();
        if (selectedCalendar) {
            this.autoCompleteName = selectedCalendar.name;
            this.selectedDate = selectedDate;
            await this.calendarSelected(selectedCalendar, { selectedDate });
        }
    }

    actionsPopOverChange(status: boolean) {
        this.isActionsOpen = status;
    }
    cutoffActionsPopOverChange(status: boolean) {
        this.isCutoffPopoverOpen = status;
    }
    scheduledEODPopOverChange(status: boolean) {
        this.isScheduledEODPopoverOpen = status;
    }
}
</script>

<style scoped lang="scss">
@import '../../../../scss/base/_variables.scss';
.filter-container {
    padding: 1em 2em 0 2em;
    display: flex;
    .refresh-section {
        display: flex;
        align-items: center;
        margin-left: auto;
    }
    .scheduled-eod-time,
    .scheduled-eod-clock {
        padding-right: 0.3rem;
    }
    .scheduled-eod-icon {
        padding-right: 0.3rem;
    }
    .scheduled-eod-text {
        margin-bottom: 1rem;
        color: #0b52cc;
    }
    .scheduled-eod-sub-text {
        &:hover {
            cursor: pointer;
            border-bottom: 1px solid #0b52cc;
        }
    }
    .filter-container__actions {
        margin-left: 0;
        display: flex;
    }
    .coa-selector {
        margin-left: 16px;
    }
    .date-selector {
        margin-left: 16px;
        padding: 7px 0;
        .z-row {
            border-bottom: solid var(--inputfield-border-width, 1px) var(--inputfield-border-color);
            border-radius: 4px;
            .button.z-component.secondary {
                background-color: #e6e7f2;
                border: unset;
                &.previous {
                    border-top-right-radius: 0;
                    border-bottom-right-radius: 0;
                }
                &.next {
                    border-top-left-radius: 0;
                    border-bottom-left-radius: 0;
                }
            }
        }
    }
    .current-book-date-selector {
        margin-left: 16px;
        &__badge {
            margin-top: 12px;
        }
        &__button {
            margin-top: 7px;
        }
    }
    .scheduled-cutoff-section {
        display: flex;
        margin-left: 15px;
        .scheduled-cutoff-section__popover {
            &__content {
                display: flex;
                flex-direction: column;

                &__footer {
                    display: flex;
                    justify-content: space-between;
                    &.scheduled-cutoff-footer {
                        column-gap: 12px;
                        justify-content: start;
                    }
                    &__actions-primary,
                    &__actions-secondary {
                        display: flex;
                        column-gap: 10px;
                    }
                    padding: 20px;
                    border-top: 1px solid #d9d7d7;
                }
                &__section {
                    padding: 20px;
                    &__text {
                        display: inline !important;
                        .bold {
                            font-weight: 500;
                        }
                    }
                }
                &--small {
                    width: 350px;
                }
                &--medium {
                    width: 500px;
                }
            }
            &__trigger-element {
                border-bottom: 2px dashed #e2dfdf;
                display: flex;
                column-gap: 5px;
            }
        }
        &__execution-state {
            border-bottom: 2px dashed #e2dfdf;
            display: flex;
            column-gap: 5px;
            align-self: center;
            height: 22px;
            ::v-deep .gds-check-circle-outline {
                font-size: 20px;
            }
            &__text {
                color: black;
            }
        }
    }
    .z-component.z-popover .dropdown .dropdown-content {
        padding-left: 1em;
    }
    .coa-prompt-text {
        font-size: 12px;
        color: var(--text-neutral-1, $gds-text-color);
    }
    &__actions {
        margin-left: auto;
        width: 100px;
        .z-list {
            & > div {
                & > div {
                    &:nth-of-type(4) {
                        border-top: 1px solid $divider-color;
                    }
                }
            }
        }
        .z-button {
            & > span {
                display: flex;
                column-gap: 10px;
            }
        }
        .z-component .dropdown-menu {
            left: -93px !important;
        }
    }
}
</style>
