<template>
  <div class="custom-alert" :class="type">
    <div class="custom-alert__header" @click="toggleExpand">
      <div class="custom-alert__expand-icon">
        <div class="chevron" :class="{ 'is-expanded': isExpanded }"></div>
      </div>
      <div class="custom-alert__icon">
        <div class="alert-icon"></div>
      </div>
      <div class="custom-alert__content">
        <div class="custom-alert__title">{{ title }}</div>
        <transition name="slide">
          <div v-show="isExpanded" class="custom-alert__message">
            <slot></slot>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomAlert',
  props: {
    type: {
      type: String,
      default: 'error',
      validator: (value) => ['error', 'warning'].includes(value)
    },
    title: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      isExpanded: true
    };
  },
  methods: {
    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    }
  }
};
</script>

<style lang="scss" scoped>
.custom-alert {
  border-radius: 4px;
  margin-bottom: 16px;

  &__header {
    display: flex;
    align-items: flex-start;
    padding: 8px 12px;
    gap: 8px;
    cursor: pointer;

    &:hover {
      .chevron {
        border-color: #666666;
      }
      .custom-alert__title {
        color: #666666;
      }
    }
  }

  &__expand-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    padding-top: 2px;

    .chevron {
      width: 8px;
      height: 8px;
      border-right: 2px solid #434B79;
      border-bottom: 2px solid #434B79;
      transform: rotate(-45deg);
      transition: transform 0.3s ease, border-color 0.2s ease;
      transform-origin: 75% 75%;

      &.is-expanded {
        transform: rotate(45deg);
      }
    }
  }

  &__icon {
    display: flex;
    align-items: center;
    padding-top: 2px;

    .alert-icon {
      width: 20px;
      height: 20px;
      border-radius: 2px;
      background-color: #FF0000;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: bold;
      line-height: 1;
      transform: rotate(45deg);
      margin-right: 8px;
      margin-left: 4px;

      /* Counter-rotate the ! to keep it upright */
      &::before {
        content: '!';
        transform: rotate(-45deg);
        display: block;
      }
    }
  }

  &__content {
    flex: 1;
  }

  &__title {
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: #434B79;
    transition: color 0.2s ease;
    margin-bottom: 4px;
  }

  &__message {
    color: #434B79;
    font-size: 14px;
    line-height: 20px;

    :deep(.error-text) {
      color: #FF0000;
    }

    > div {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  &.error {
    background-color: #FFF2F2;
    border: 1px solid #FFE5E5;
  }

  &.warning {
    background-color: #FFF8E5;
    border: 1px solid #FFECC6;

    .alert-icon {
      background-color: #F94747;
    }
  }
}

// Slide animation
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
  max-height: 300px;
}

.slide-enter-from,
.slide-leave-to {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}
</style>
