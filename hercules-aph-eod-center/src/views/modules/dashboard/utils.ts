import { DEFAULT_BOOK_DATE_FORMAT, weekdays } from '@/common/constants';
import { context } from '@hercules/context';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);
/**
 * This is temporary untill GDS give us date formatter callback function
 * Datepicker on the dahboard requires a specific format type to be passed in order to parse the date string.
 * @param {string} dateString - The date string to analyze.
 * @returns {string} The format type of the date string, or "DEFAULT_BOOK_DATE_FORMAT" if the format type cannot be determined.
 */
export function getDateFormatType(dateString: string) {
    const formats = [
        { format: 'D MMM YYYY', regex: /^(\d{1,2})\s([A-Za-z]{3,})\s\d{4}$/ },
        { format: 'D MMM, YYYY', regex: /^(\d{1,2})\s([A-Za-z]{3,})\s?,\s?\d{4}$/ },
        { format: 'MMM D, YYYY', regex: /^([A-Za-z]{3,})\s\d{1,2},\s\d{4}$/ },
        { format: 'MMM D YYYY', regex: /^([A-Za-z]{3,})\s\d{1,2}\s\d{4}$/ },
    ];

    for (const format of formats) {
        if (format.regex.test(dateString)) {
            return format.format;
        }
    }

    return DEFAULT_BOOK_DATE_FORMAT;
}

export const convertCronToLocalTimeAndTimestamp = (
    cronExpression: string,
    sourceDateStr: string // e.g. '2025-03-29T00:00-07:00[America/Los_Angeles]'
  ): { timeString: string; timestamp: number } => {
    const parts = cronExpression.split(' ');
    const sourceTimezone = extractTimeZone(sourceDateStr);
    if (parts.length !== 6) {
      throw new Error('Invalid cron expression');
    }
    const second = parseInt(parts[0], 10);
    const minute = parseInt(parts[1], 10);
    const hour = parseInt(parts[2], 10);
    if (
      isNaN(hour) || hour < 0 || hour > 23 ||
      isNaN(minute) || minute < 0 || minute > 59 ||
      isNaN(second) || second < 0 || second > 59
    ) {
      throw new Error('Invalid time values in cron expression');
    }
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    // Remove the bracketed timezone (e.g., "[America/Los_Angeles]")
    const cleanedDateStr = sourceDateStr.replace(/\[.*\]$/, '');
    // Parse the source date in the given timezone
    const baseDate = dayjs.tz(cleanedDateStr, sourceTimezone);
    // Apply cron time to that date
    const sourceTime = baseDate
      .hour(hour)
      .minute(minute)
      .second(second)
      .millisecond(0);
    // Convert to user's local time
    const localTime = sourceTime.tz(userTimezone);
    return {
      timeString: localTime.format('h:mm:ss A'),
      timestamp: localTime.valueOf()
    };
  };
  

export function cronToHumanTime(cronExpression: string): string {
    const parts = cronExpression.split(' ');
    if (parts.length !== 6) return 'Invalid cron expression';

    const [seconds, minutes, hours, dayOfMonth, month, dayOfWeek] = parts;
    // Handling minute intervals (e.g., */5 means every 5 minutes)
    if (minutes.includes('*/')) {
        return `Every ${minutes.split('*/')[1]} minutes`;
    }

    // Handling hour intervals (e.g., */2 means every 2 hours)
    if (hours.includes('*/')) {
        return `Every ${hours.split('*/')[1]} hours`;
    }

    // Convert hours and minutes to 12-hour format
    const hourList = hours.split(',').map((h) => parseInt(h, 10));
    const minuteList = minutes.split(',').map((m) => parseInt(m, 10));

    const timeSlots = hourList.map((hour) => {
        const period = hour >= 12 ? 'PM' : 'AM';
        const formattedHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
        const formattedMinutes =
            minuteList.length > 1
                ? minuteList.map((m) => m.toString().padStart(2, '0')).join(',')
                : minuteList[0]?.toString().padStart(2, '0') || '00';
        return `${formattedHour}:${formattedMinutes} ${period}`;
    });

    // Day conditions
    let dayString = '';
    if (dayOfMonth === '*' && dayOfWeek === '*') {
        dayString = 'every day';
    } else if (dayOfMonth !== '*') {
        dayString = `on day ${dayOfMonth} of the month`;
    } else if (dayOfWeek !== '*') {
        const days = dayOfWeek
            .split(',')
            .map((d) => weekdays[parseInt(d, 10)])
            .join(', ');
        dayString = `on ${days}`;
    }

    return `${timeSlots.join(' and ')} ${dayString}`;
}

// Example usages:
// console.log(cronToHumanTime("0 */5 * * * *")); // Every 5 minutes
// console.log(cronToHumanTime("0 0 22 * * *")); // 10:00 PM every day
// console.log(cronToHumanTime("0 30 9,18 * * *")); // 9:30 AM and 6:30 PM every day
// console.log(cronToHumanTime("0 0 */2 * * *")); // Every 2 hours
// console.log(cronToHumanTime("0 45 14 * * 1,3,5")); // 2:45 PM on Monday, Wednesday, Friday
// console.log(cronToHumanTime("0 0 8 15 * *")); // 8:00 AM on day 15 of the month

export const pauseResumeCutoffContext = () => {
    return context?.getAttribute('viewConfig.dashboard.filters.enablePauseResumeCutoff') || false;
};
export const manualCutoffContext = () => {
    return context?.getAttribute('viewConfig.dashboard.filters.enableManualCutoff') || false;
};
export const showCutoffContext = () => {
    return context?.getAttribute('viewConfig.dashboard.filters.showCutoff') || false;
};

export const extractTimeZone = (str: string) => {
    const match = str.match(/\[(.*?)\]/);
    return match ? match[1] : '';
};
