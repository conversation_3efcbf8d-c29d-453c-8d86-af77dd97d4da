<template>
    <div class="app-container">
        <zwe-aph-header
            ref="headerRef"
            v-if="aphHeaderAttributes"
            :center-name="aphHeaderAttributes.appHeaderName"
            :tenant-id="$ifiId"
            :from-collection="aphHeaderAttributes.fromCollection"
            :app-switcher="true"
            :oms-base-url="aphHeaderAttributes.omsBaseUrl"
            :product-name="aphHeaderAttributes.productName"
            :hercules-service-base-url="aphHeaderAttributes.herculesServiceClientBaseUrl"
            :sandbox-id="aphHeaderAttributes.sandboxId"
            :cipher-base-url="aphHeaderAttributes.cipherBaseUrl"
            :show-profile-management-options="aphHeaderAttributes.showProfileManagementOptions"
            @title-logo-click="goHome"
            :home-btn-disabled="true"
            v-bind="optionalHeaderAttributes"
            :type="aphHeaderAttributes.theme"
        >
            <div slot="left-section-items">
                <z-menu-bar
                    ref="menu-bar"
                    :type="aphHeaderAttributes.theme"
                    :isMenuBarPopOverScrollable="false"
                    :options="menuList"
                    @clickedItem="menuClicked"
                     :activeId="activeId"
                    v-if="menuList.length > 0"
                >
                    <template v-for="menuItem in getMenuDropdowns" #[menuItem.slotName]>
                        <div :key="menuItem.id">
                            <!-- show search in calendar dropdown -->
                            <z-row v-if="menuItem.id === HEADER_MENU_IDS.CALENDARS" :gutterLess="true">
                                <z-column>
                                    <z-search
                                        :placeholder="$t('common.topHeader.search')"
                                        :value="searchText"
                                        @search-text-change="(text) => (searchText = text)"
                                        @close-search-click="searchText = ''"
                                    />
                                </z-column>
                            </z-row>
                            <ZList
                                @selectedItem="
                                    (data) => {
                                        selectedItem(menuItem, data);
                                    }
                                "
                                v-bind="menuItem.listObj"
                            ></ZList>
                        </div>
                    </template>
                </z-menu-bar>
            </div>
        </zwe-aph-header>
        <template v-if="!isLoading">
            <ZLayoutMain>
                <ZRow :gutterLess="true">
                    <ZColumn :gutterLess="true">
                        <router-view :key="$route.fullPath"></router-view>
                    </ZColumn>
                </ZRow>
            </ZLayoutMain>
        </template>
        <b-loading :active="isLoading" :is-full-page="false">
            <app-loading></app-loading>
        </b-loading>
    </div>
</template>

<script lang="ts">
import { Component, Watch } from 'vue-property-decorator';
import AuraFinanceCenter from '@/EODCenter';
import { COAServiceManager, calendarServiceManager } from './Services';
import { HEADER_MENU_IDS, MODULES_PATH } from './common/constants';
import { context, getCommonAssetUrl } from '@hercules/context';
import { AphroditeHeaderAttributes, AppHeaderMenuConfig, SetCalendarCoAPhaseModel } from './types';
import { ZMenuBar } from '@zeta-design/galaxy/menuBar';
import { ZList } from '@zeta-design/galaxy/dataInput';
import { ZToast } from '@zeta-design/galaxy/toast';
import { ZAction, ZActionGroup, ZUtilityFooter, ZButton } from '@zeta-design/galaxy/core';
import { ZRow, ZColumn, ZLayoutAside, ZLayoutMain } from '@zeta-design/galaxy/layout';
import { MenuList, MenuItem, Calendar, Coa } from 'src/types/index';
import { ROOT_STATE_CONST } from './store/types';
import { CalendarTypes } from '@zeta/service-clients/lib/tachyon/calendar-management/types';
const coaService = COAServiceManager();
const calendarService = calendarServiceManager();

@Component({
    components: {
        ZMenuBar,
        ZList,
        ZAction,
        ZActionGroup,
        ZUtilityFooter,
        ZRow,
        ZColumn,
        ZLayoutAside,
        ZLayoutMain,
        ZButton,
        ZToast,
    },
})
export default class App extends AuraFinanceCenter {
    private get selectedCalendar(): Calendar | null {
        return this.getStateData(ROOT_STATE_CONST.SELECTED_CALENDAR);
    }
    private get calendarPhaseCoAs(): Coa[] {
        return this.getStateData(ROOT_STATE_CONST.CALENDAR_PHASE_COAS);
    }
    private get currentCBU(): CalendarTypes.GetCurrentCBUResponse | null {
        return this.getStateData(ROOT_STATE_CONST.CURRENT_CBU);
    }
    private HEADER_MENU_IDS = HEADER_MENU_IDS;
    private coaList: Coa[] = [];
    private isLoading = false;
    private calendarList: Array<Calendar> = [];
    private menuList: Array<MenuList> = [];
    private aphHeaderAttributes!: AphroditeHeaderAttributes;
    private searchText = '';
    private optionalHeaderAttributes = {};
    // select coa by default on initial load
    private currentMenuItem = '';
    private activeId = 'dashboard';
    get getMenuDropdowns() {
        let menuItems: Array<MenuItem> = [];
        this.menuList.forEach((item: MenuList) => {
            if (item.listObj) {
                menuItems.push({
                    ...item,
                    slotName: `content-${item.id}`,
                    createBtn: {
                        name:
                            item.id === HEADER_MENU_IDS.CALENDARS
                                ? (this.$t('calendarManagement.createCalendar.title') as string)
                                : (this.$t('coa.createCOA.title') as string),
                    },
                });
            }
        });
        return menuItems;
    }

    private menuClicked(data: MenuList) {
        this.activeId = data.id;
        this.currentMenuItem = data.name;
        const liveDashboardLabel = this.headerConfig?.menuItems?.livedashboard?.label || this.$i18n.t('common.topHeader.livedashboard');
        if (this.currentMenuItem === 'Home') {
            this.goHome();
        } else if (this.currentMenuItem === this.$i18n.t('common.topHeader.requests')) {
            this.goToRequests();
        } else if (this.currentMenuItem === liveDashboardLabel) {
            window.open(this.appConfig?.endpoints?.liveDashboardUrl, '_blank');    
        }
    }

    private async selectedItem(menuItem: MenuList, item: Calendar | Coa) {
        /**
         * We need to access the popover component instead menu bar
         * and trigger the toggle button to close the popover when a value
         * is selected
         */
        const menuBarChildRef = this.$refs['menu-bar'] as any;
        menuBarChildRef.$refs[`${menuItem.id}popover`][0].togglePopOver();
        if (this.currentMenuItem === 'CoAs') {
            const path = `/${MODULES_PATH.COA}/id/${item.id}`;
            this.$router.push({ path });
        } else {
            // add the selected calendar and the coas related to it
            await this.setStateData({
                key: ROOT_STATE_CONST.SELECTED_CALENDAR_DETAILS,
                value: item,
            });
            await this.setStateData({
                key: ROOT_STATE_CONST.CALENDAR_COAS,
                value: {
                    calendarId: item.id,
                    coaList: this.coaList,
                },
            });
            // Clears the search text when a calendar is selected
            this.searchText = '';
            const path = `${MODULES_PATH.CALENDAR}/${item.id}/overview`;
            this.$router.push({ path });
        }
    }

    private goToRequests() {
        const path = MODULES_PATH.REQUESTS;
        this.$router.push({ path, query: {} });
    }

    private goHome() {
        this.activeId = 'dashboard';
        const path = MODULES_PATH.HOME;
        this.$router.push({
            path,
            query: {
                ...(this.selectedCalendar ? { calendarId: this.selectedCalendar?.id } : {}),
            },
        });
    }

    private async getCoaList() {
        const { data: response } = await coaService(String(this.auraUrl)).getAllCOAs({
            tenantId: this.$ifiId,
        });
        return response;
    }
    /**
     * Function to fetch the list of CoAs for the
     * provided tenant and set store variables
     */
    private async loadCOADetails() {
        return await this.getCoaList()
            .then((res) => {
                this.coaList = res;
                if (this.coaList?.length) {
                    // this.setAllCoAs(this.coaList);
                    this.setStateData({
                        key: ROOT_STATE_CONST.ALL_COAS,
                        value: this.coaList,
                    });
                    if (this.currentCBU) {
                        // Set the COAs linked with selected calendar
                        this.setStateData({
                            key: ROOT_STATE_CONST.CALENDAR_PHASE_COAS,
                            value: {
                                coaList: this.coaList,
                                calendarId: this.selectedCalendar?.id as string,
                            } as SetCalendarCoAPhaseModel,
                        });
                        // if coa list count is 1, select the one coa by default
                        if (this.calendarPhaseCoAs?.length === 1) {
                            this.setStateData({
                                key: ROOT_STATE_CONST.SELECTED_CALENDAR_COA,
                                value: this.calendarPhaseCoAs[0],
                            });
                        } // else part -> to manually prompt the user to select is not handled here
                    }
                }
                // if (this.menuList[2].listObj) {
                //     this.menuList[2].listObj.listItems = this.coaList;
                // }
            })
            .finally(() => {
                this.isLoading = false;
                this.setStateData({
                    key: ROOT_STATE_CONST.CALENDAR_COA_LOADING,
                    value: false,
                });
            });
    }

    /**
     * Function to set the attributes for zwe-aph-header web component
     */
    private setAphHeaderAttributes() {
        const attributes = context?.attributes;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (window as any).__zeta__ = { angelos: {} };
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (window as any).__zeta__.angelos.SERVICE_BASE_URL = this.angelosBaseUrl;

        this.aphHeaderAttributes = {
            appHeaderName: attributes?.system?.aphShell?.appHeaderName || 'EOD Center',
            productName: attributes?.system?.aphShell?.productName || 'aphrodite',
            showAppSwitcher:
                attributes?.system?.aphShell?.showAppSwitcher === undefined
                    ? true
                    : attributes?.aphShell?.showAppSwitcher,
            fromCollection: attributes.appShell?.fromCollection || false,
            omsBaseUrl: attributes?.omsBaseUrl,
            tenantLogoUrl: getCommonAssetUrl(attributes.viewConfig.header.tenantLogoUrl),
            herculesServiceClientBaseUrl: attributes?.herculesServiceClientBaseUrl,
            sandboxId: context?.sandboxId as string,
            cipherBaseUrl: attributes?.cipherBaseUrl,
            // By default Profile management options are hidden, we can enable it from tenant config.
            showProfileManagementOptions: attributes?.system?.aphShell?.showProfileManagementOptions || true,
            theme: attributes?.system?.aphShell?.theme || 'light',
        };
        this.optionalHeaderAttributes = {
            ...this.optionalHeaderAttributes,
            'tenant-logo-url-light': getCommonAssetUrl(attributes.viewConfig.header.tenantLogoUrl),
        };
    }
    async created() {
        this.setAphHeaderAttributes();
        this.setHeaderMenu();
        await this.setUserProfile();
        await this.setUserRoles();
        await this.loadCalendars();
        await this.loadCOADetails();
    }

    private async setUserRoles() {
        const attributes = context?.attributes;
        await this.setStateData({
            key: ROOT_STATE_CONST.USER_ROLES,
            value: {
                omsBaseUrl: attributes?.omsBaseUrl,
            },
        });
    }

    private async setUserProfile() {
        const attributes = context?.attributes;
        await this.setStateData({
            key: ROOT_STATE_CONST.USER_PROFILE,
            value: {
                omsBaseUrl: attributes?.omsBaseUrl,
            },
        });
    }
    
    private setHeaderMenu() {
        /**
         * If the menu item list is not present in the day zero config (headerConfig) then for backup show all the header options available. Dervied from the HEADER_MENU_IDS
         */
        const menuConfig = this.headerConfig?.menuItems ? this.headerConfig?.menuItems : Object.keys(HEADER_MENU_IDS).reduce((acc: AppHeaderMenuConfig, key: string) => {
            acc[key.toLowerCase()] = {
                show: true,
                label: this.$i18n.t(`common.topHeader.${HEADER_MENU_IDS[key]}`) as string,
            }
            return acc;
        }, {});
        this.menuList = [
            ...(menuConfig?.dashboard?.show ? [
                {
                    id: HEADER_MENU_IDS.DASHBOARD,
                    name: menuConfig.dashboard.label,
                },
            ]: []),
            ...(menuConfig?.calendars?.show
                ? [
                      {
                          id: HEADER_MENU_IDS.CALENDARS,
                          name: menuConfig.calendars.label,
                          icon: 'open-in-new',
                          listObj: {
                              listItems: [],
                          },
                      },
                  ]
                : []),
            ...(menuConfig?.requests?.show?[
                {
                    id: HEADER_MENU_IDS.REQUESTS,
                    name: menuConfig.requests.label,
                },
            ]: []),
            ...(menuConfig?.livedashboard?.show?[
                {
                    id: HEADER_MENU_IDS.LIVEDASHBOARD,
                    name: menuConfig.livedashboard.label,
                },
            ]: []),
        ];
    }

    private async getCalendarList() {
        const { data: response } = await calendarService(String(this.auraUrl)).getAllCalendars({
            tenantId: this.$ifiId,
        });
        return response;
    }

    /**
     * Function to fetch all calendars for the provided tenant
     * and set store variables
     */
    private async loadCalendars() {
        this.isLoading = true;
        await this.getCalendarList()
            .then(async (res) => {
                this.calendarList = res;
                if (this.calendarList) {
                    // Set All Calendars LIST state
                    await this.setStateData({
                        key: ROOT_STATE_CONST.ALL_CALENDARS,
                        value: this.calendarList,
                    });
                }
                if (this.menuList[1].listObj) {
                    this.menuList[1].listObj.listItems = this.calendarList;
                }
            })
            .catch((error) => {
                ZToast.open({
                    heading: this.$t('error.calendarDetailsFailure.header'),
                    description:
                        (error.response && error.response.data && error.response.data.message) ||
                        this.$t('error.calendarDetailsFailure.message'),
                    position: 'is-bottom-right',
                    type: 'danger',
                });
            })
            .finally(() => {
                // If more number of calendars present are more than 1
                // then hide the loaders and let the user select default calendar
                this.isLoading = false;
                this.setStateData({
                    key: ROOT_STATE_CONST.CALENDAR_LOADING,
                    value: false,
                });

                this.setStateData({
                    key: ROOT_STATE_CONST.CBU_LOADING,
                    value: false,
                });
            });
    }

    /**
     * Watches for changes in the searchText property and updates the calendar menu list accordingly.
     * If the searchText is not empty, filters the calendarList based on the search text and updates the listItems in the calendarMenuList.
     * If the searchText is empty, resets the listItems in the calendarMenuList to the original calendarList.
     */
    @Watch('searchText')
    private onSearchTextChange() {
        const calendarMenuList = this.menuList.find((item) => item.id === HEADER_MENU_IDS.CALENDARS);
        if (calendarMenuList?.listObj) {
            if (this.searchText.length > 0) {
                calendarMenuList.listObj.listItems = this.calendarList.filter((item) => {
                    return item.name.toLowerCase().includes(this.searchText.toLowerCase());
                });
            } else {
                calendarMenuList.listObj.listItems = this.calendarList;
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.calendar-header {
    --aph-header-text-size: 12px;
    --aph-header-bg-color: #0f1b59;
    --aph-header-text-color: #fff;
    --switcher-trigger-text-color: #fff;
}
.z-utility-footer {
    position: sticky;
    bottom: 0;
}

.menu-bar-link-container {
    .z-list {
        max-height: 335px;
        overflow-y: auto;
    }
}

.app-container {
    min-height: 100vh;
    height: 100%;
    padding-bottom: 0;
    position: relative;

    .menu-list .menu-seperator {
        color: red !important;
        border: 1px solid !important;
    }
    .selected-coa {
        height: var(--sidebar-primary-switcher-height, 56px);
        width: 100%;
        border: 1px solid transparent;
        color: var(--sidebar-primary-switcher-title-text-color);
        background: var(--sidebar-primary-switcher-bg-color);
        text-transform: capitalize;
        overflow-wrap: break-word;
        &-name {
            font-weight: 500;
            width: -webkit-fill-available;
        }
    }
}
</style>
