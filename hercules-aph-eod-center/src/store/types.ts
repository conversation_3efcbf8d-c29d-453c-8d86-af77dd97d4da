import {
    DashboardState,
    Calendar,
    Coa,
    UserProfile,
    EODRunInfo,
    Checklist,
    WorkerDetails,
    SystemLedger,
    Sandbox,
    PostingCategoriesTree,
    VoucherCodesData,
    CutoffScheduleDetails,
    EODRunDetails,
    TimePeriod,
} from '@/types';
import { CalendarTypes } from '@zeta/service-clients/lib/tachyon/calendar-management/types';
import { COATypes } from '@zeta/service-clients/lib/tachyon/coa/types';

// Add your state variable, for example 'coaId' to this interface
export interface RootState {
    selectedCalendar: Calendar | null;
    selectedCalendarDetails: Calendar | null;
    currentCBU: CalendarTypes.GetCurrentCBUResponse | null;
    updatedDateCBU: CalendarTypes.GetCurrentCBUResponse | null;
    updatedDateNextCBU: CalendarTypes.GetCurrentCBUResponse | null;
    calendarPhaseCoAs: Coa[] | null;
    calendarCoAs: Coa[] | null;
    tenantId: string | null;
    allCalendars: Calendar[] | null;
    allCoAs: Coa[] | null;
    calendarLoading: boolean | null;
    calendarCoALoading: boolean | null;
    cBULoading: boolean | null;
    dashboardState?: DashboardState | null;
    ledgerIdsToBeSkipped?: string[];
    selectedCalendarCoA: Coa | null;
    selectedCalendarDate: string | null;
    userRoles?: Set<string>;
    userProfile?: UserProfile | null;
    eodRunInfo?: EODRunInfo | null;
    checklistTableData?: Checklist[] | null;
    workerTableData?: WorkerDetails[] | null;
    atdChecklistTableData?: Checklist[] | null;
    coaTreeData?: COATypes.Node[] | null;
    postingCategoriesTreeData?: PostingCategoriesTree[] | null;
    voucherCodesData?: VoucherCodesData[] | null;
    systemLedgers?: SystemLedger[] | null;
    sandbox?: Sandbox | null;
    cutoffScheduleDetails?: CutoffScheduleDetails | null;
    scheduledEODDetails?: EODRunDetails | null;
    periods?: TimePeriod[] | null;
}
export type RootStateValues = RootState[keyof RootState];

// Add the constant name for the newly added state variable to this type
export type ROOT_STATE_CONST_KEYS =
    | 'SELECTED_CALENDAR'
    | 'SELECTED_CALENDAR_DETAILS'
    | 'CURRENT_CBU'
    | 'UPDATED_DATE_CBU'
    | 'UPDATED_DATE_NEXT_CBU'
    | 'CALENDAR_PHASE_COAS'
    | 'CALENDAR_COAS'
    | 'TENANT_ID'
    | 'ALL_CALENDARS'
    | 'ALL_COAS'
    | 'CALENDAR_LOADING'
    | 'CALENDAR_COA_LOADING'
    | 'CBU_LOADING'
    | 'DASHBOARD_STATE'
    | 'LEDGER_IDS_TO_BE_SKIPPED'
    | 'SELECTED_CALENDAR_COA'
    | 'SELECTED_CALENDAR_DATE'
    | 'USER_ROLES'
    | 'USER_PROFILE'
    | 'EOD_RUN_INFO'
    | 'CHECKLIST_TABLE_DATA'
    | 'WORKER_TABLE_DATA'
    | 'ATD_CHECKLIST_TABLE_DATA'
    | 'COA_TREE_DATA'
    | 'POSTING_CATEGORIES_TREE_DATA'
    | 'VOUCHER_CODES_DATA'
    | 'SYSTEM_LEDGERS'
    | 'SANDBOX'
    | 'CUTOFF_SCHEDULE_DETAILS'
    | 'SCHEDULED_EOD_DETAILS'
    | 'PERIODS';

export interface StatePayload {
    key: keyof RootState;
    value?: unknown;
}

// Getter constant
export const GET_STATE_DATA = 'getStateData';
// Setter constant
export const SET_STATE_DATA = 'setStateData';

// Add the mapping for the newly added state variable to a constant
export const ROOT_STATE_CONST: {
    [key in ROOT_STATE_CONST_KEYS]: keyof RootState;
} = {
    SELECTED_CALENDAR: 'selectedCalendar',
    SELECTED_CALENDAR_DETAILS: 'selectedCalendarDetails',
    CURRENT_CBU: 'currentCBU',
    UPDATED_DATE_CBU: 'updatedDateCBU',
    UPDATED_DATE_NEXT_CBU: 'updatedDateNextCBU',
    CALENDAR_PHASE_COAS: 'calendarPhaseCoAs',
    CALENDAR_COAS: 'calendarCoAs',
    TENANT_ID: 'tenantId',
    ALL_CALENDARS: 'allCalendars',
    ALL_COAS: 'allCoAs',
    CALENDAR_LOADING: 'calendarLoading',
    CALENDAR_COA_LOADING: 'calendarCoALoading',
    CBU_LOADING: 'cBULoading',
    DASHBOARD_STATE: 'dashboardState',
    LEDGER_IDS_TO_BE_SKIPPED: 'ledgerIdsToBeSkipped',
    SELECTED_CALENDAR_COA: 'selectedCalendarCoA',
    SELECTED_CALENDAR_DATE: 'selectedCalendarDate',
    USER_ROLES: 'userRoles',
    USER_PROFILE: 'userProfile',
    EOD_RUN_INFO: 'eodRunInfo',
    CHECKLIST_TABLE_DATA: 'checklistTableData',
    WORKER_TABLE_DATA: 'workerTableData',
    ATD_CHECKLIST_TABLE_DATA: 'atdChecklistTableData',
    COA_TREE_DATA: 'coaTreeData',
    POSTING_CATEGORIES_TREE_DATA: 'postingCategoriesTreeData',
    VOUCHER_CODES_DATA: 'voucherCodesData',
    SYSTEM_LEDGERS: 'systemLedgers',
    SANDBOX: 'sandbox',
    CUTOFF_SCHEDULE_DETAILS: 'cutoffScheduleDetails',
    SCHEDULED_EOD_DETAILS: 'scheduledEODDetails',
    PERIODS: 'periods',
};

// Constants for default selection
export const DEFAULT_SELECTED_CALENDAR_COUNT = 1;
export const DEFAULT_SELECTED_CALENDAR_COA_COUNT = 1;
