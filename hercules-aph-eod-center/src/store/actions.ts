import i18n from '../i18n';
import { RootState, StatePayload, ROOT_STATE_CONST, SET_STATE_DATA } from './types';
import {
    AccessManagementServiceManager,
    OrchestraEODService,
    calendarServiceManager,
    kaleServiceManager,
} from '@/Services';
import {
    clusterSpec,
    DATE_FORMAT_WITH_TIMEZONE,
    DATE_FORMAT_YYYY_MM_DD,
    emptyStateDashboardUrl,
    errorStateNotFoundUrl,
} from '@/common/constants';
import {
    DashboardState,
    SetCalendarCoAPhaseModel,
    SetCalendarStateModel,
    SetSelectedDateModel,
    SetUserRolesModel,
    UserProfile,
    GetAllPeriodsEODResponse,
    Calendar,
    CutoffScheduleDetails,
    EODRunDetails,
} from '@/types';
import { USER_PREFERENCES_CONST } from '@/views/modules/dashboard/constants';
import { AccessManagementTypes } from '@zeta/service-clients/lib/oms/sandbox/types';
import dayJs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { ActionTree } from 'vuex';
import { CalendarTypes } from '@zeta/service-clients/lib/tachyon/calendar-management/types';
import { convertCronToLocalTimeAndTimestamp, cronToHumanTime } from '@/views/modules/dashboard/utils';


dayJs.extend(timezone);
dayJs.extend(utc);

const accessManagementService = AccessManagementServiceManager();
const calendarService = calendarServiceManager();
const kaleService = kaleServiceManager();

const actions: ActionTree<RootState, RootState> = {
    async [SET_STATE_DATA](
        {
            commit,
            state
        }: {
            commit: {
                (type: string, payload?: StatePayload): void;
            };
            state: RootState;
        },
        payload: StatePayload,
    ) {
        switch (payload.key) {
            // Set Dashboard Calendar State
            case ROOT_STATE_CONST.SELECTED_CALENDAR: {
                const selectedCalendar = payload.value as Calendar;
                commit(SET_STATE_DATA, {
                    key: ROOT_STATE_CONST.SELECTED_CALENDAR,
                    value: selectedCalendar,
                });
                // Update route for calendar id
                const params = new URLSearchParams(window.location.search);
                params.set(USER_PREFERENCES_CONST.CALENDAR, selectedCalendar.id);
                window.history.replaceState(
                    {},
                    '',
                    decodeURIComponent(`${window.location.href.split('?')[0]}?${params}`),
                );
                break;
            }
            // Set Current CBU and period details state
            case ROOT_STATE_CONST.CURRENT_CBU: {
                try {
                    const payloadValue = payload.value as SetCalendarStateModel;
                    const orchestraEODService = new OrchestraEODService();
                    // Set CBU details
                    // Show CBU loading state
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.CBU_LOADING,
                        value: true,
                    });
                    const periodsResponse = await orchestraEODService.fetchCurrentCBUForCalendar(payloadValue);
                    const { cbuPeriod, startPeriod, endPeriod, periods = [] } = periodsResponse as GetAllPeriodsEODResponse;
                    if (payloadValue.updateCBUPeriodOnly) {
                        // Update the current CBU date state
                        commit(SET_STATE_DATA, {
                            key: ROOT_STATE_CONST.CURRENT_CBU,
                            value: cbuPeriod,
                        });
                    } else {
                        const periodStartTimeMillis = startPeriod?.startTime;
                        if (periodStartTimeMillis) {
                            // Set Query Params
                            const params = new URLSearchParams(window.location.search);
                            params.set(
                                USER_PREFERENCES_CONST.PERIOD,
                                encodeURIComponent(
                                    dayJs(periodStartTimeMillis)
                                        .tz(payloadValue.selectedCalendar.timezone)
                                        .format(DATE_FORMAT_WITH_TIMEZONE),
                                ),
                            );
                            window.history.replaceState(
                                {},
                                '',
                                decodeURIComponent(`${window.location.href.split('?')[0]}?${params}`),
                            );
                        }

                        // Set periods
                        commit(SET_STATE_DATA, {
                            key: ROOT_STATE_CONST.PERIODS,
                            value: periods,
                        });
                        // Update the current CBU date state
                        commit(SET_STATE_DATA, {
                            key: ROOT_STATE_CONST.CURRENT_CBU,
                            value: cbuPeriod,
                        });
                        /**
                         * When calendar is selected and cbu is fetched, assign the UPDATED_DATE_CBU same as CURRENT_CBU
                         * and UPDATED_DATE_NEXT_CBU with next periodId.
                         */
                        commit(SET_STATE_DATA, {
                            key: ROOT_STATE_CONST.UPDATED_DATE_CBU,
                            value: startPeriod,
                        });
                        commit(SET_STATE_DATA, {
                            key: ROOT_STATE_CONST.UPDATED_DATE_NEXT_CBU,
                            value: endPeriod,
                        });
                        // Set Dashboard State to original
                        commit(SET_STATE_DATA, {
                            key: ROOT_STATE_CONST.DASHBOARD_STATE,
                            value: {
                                imgSrc: emptyStateDashboardUrl,
                                title: i18n.t('monitoringDashboard.states.emptyStateTitle'),
                                description: i18n.t('monitoringDashboard.states.emptyStateDescription'),
                            } as DashboardState,
                        });
                    }
                } catch (error) {
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.CURRENT_CBU,
                        value: null,
                    });
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.UPDATED_DATE_CBU,
                        value: null,
                    });
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.UPDATED_DATE_NEXT_CBU,
                        value: null,
                    });
                    // Set Dashboard State
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.DASHBOARD_STATE,
                        value: {
                            imgSrc: errorStateNotFoundUrl,
                            title: i18n.t('monitoringDashboard.states.noClockTitle'),
                            description: i18n.t('monitoringDashboard.states.noClockDescription'),
                        } as DashboardState,
                    });
                } finally {
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.CBU_LOADING,
                        value: false,
                    });
                }
                break;
            }
            // Set Calendar  CoAs for Details screen
            case ROOT_STATE_CONST.CALENDAR_COAS: {
                const payloadValue = payload.value as SetCalendarCoAPhaseModel;
                if (payloadValue.calendarId) {
                    // Filter the CoAs associated with the selected calendar
                    const filteredCoAs = payloadValue.coaList?.filter(
                        (coa) => coa.calendarID === payloadValue.calendarId,
                    );
                    const value = filteredCoAs?.length ? filteredCoAs : null;
                    // if associated CoAs found, set in store
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.CALENDAR_COAS,
                        value,
                    });
                }
                break;
            }
            // Set Calendar Phase CoAs
            case ROOT_STATE_CONST.CALENDAR_PHASE_COAS: {
                const payloadValue = payload.value as SetCalendarCoAPhaseModel;
                if (payloadValue.calendarId) {
                    // Filter the CoAs associated with the selected calendar
                    const filteredCoAs = payloadValue.coaList?.filter(
                        (coa) => coa.calendarID === payloadValue.calendarId,
                    );
                    const value = filteredCoAs?.length ? filteredCoAs : null;
                    // if associated CoAs found, set in store
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.CALENDAR_PHASE_COAS,
                        value,
                    });
                    if (!value) {
                        // Set Dashboard State if coas not found for the given calendar
                        commit(SET_STATE_DATA, {
                            key: ROOT_STATE_CONST.DASHBOARD_STATE,
                            value: {
                                imgSrc: errorStateNotFoundUrl,
                                title: i18n.t('monitoringDashboard.states.noCoAsTitle'),
                                description: i18n.t('monitoringDashboard.states.noCoAsDescription'),
                            } as DashboardState,
                        });
                    } else {
                        // Set Dashboard State to original
                        commit(SET_STATE_DATA, {
                            key: ROOT_STATE_CONST.DASHBOARD_STATE,
                            value: {
                                imgSrc: emptyStateDashboardUrl,
                                title: i18n.t('monitoringDashboard.states.emptyStateTitle'),
                                description: i18n.t('monitoringDashboard.states.emptyStateDescription'),
                            } as DashboardState,
                        });
                    }
                } else {
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.CALENDAR_PHASE_COAS,
                        value: null,
                    });
                }
                break;
            }
            // when new date is selected update UPDATED_DATE_CBU and UPDATED_DATE_NEXT_CBU
            case ROOT_STATE_CONST.SELECTED_CALENDAR_DATE: {
                const payloadValue = payload.value as SetSelectedDateModel;
                commit(SET_STATE_DATA, {
                    key: ROOT_STATE_CONST.SELECTED_CALENDAR_DATE,
                    value: payloadValue.selectedDate,
                });
                // Set period details of the selected date and next date
                commit(SET_STATE_DATA, {
                    key: ROOT_STATE_CONST.CALENDAR_LOADING,
                    value: true,
                });
                try {
                    const timezoneName = payloadValue.timezone;
                    dayJs.tz.setDefault(timezoneName);
                    const orchestraEODService = new OrchestraEODService();
                    const selectedDateStr = dayJs(payloadValue.selectedDate).format(DATE_FORMAT_YYYY_MM_DD);
                    const periodsResponse = await orchestraEODService.fetchPeriodsForCalendar({
                        tenantId: payloadValue.selectedCalendar?.tenantID,
                        timezone: payloadValue.timezone,
                        calendarId: payloadValue.selectedCalendar?.id,
                        auraUrl: payloadValue.auraUrl,
                        clockId: payloadValue.currentCBU?.clockID as string,
                        cycleId: payloadValue.currentCBU?.cycleID as string,
                        periodStartDate: payloadValue.selectedDate,
                        periodEndDate: dayJs(payloadValue.selectedDate).add(1, 'day').valueOf(),
                        isSelectedDateNotCBUDate:
                            dayJs.tz(selectedDateStr).valueOf() < dayJs(payloadValue.currentCBU?.startTime).valueOf()
                                ? true
                                : false,
                    });
                    const { startPeriod, endPeriod, periods = []} = periodsResponse as GetAllPeriodsEODResponse;
                    // Set Query Params
                    const periodStartTimeMillis = startPeriod?.startTime;
                    if (periodStartTimeMillis) {
                        // Set Query Params
                        const params = new URLSearchParams(window.location.search);
                        params.set(
                            USER_PREFERENCES_CONST.PERIOD,
                            encodeURIComponent(
                                dayJs(periodStartTimeMillis)
                                    .tz(payloadValue.selectedCalendar.timezone)
                                    .format(DATE_FORMAT_WITH_TIMEZONE),
                            ),
                        );
                        window.history.replaceState(
                            {},
                            '',
                            decodeURIComponent(`${window.location.href.split('?')[0]}?${params}`),
                        );
                    }
                    dayJs.tz.setDefault();
                    
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.PERIODS,
                        value: periods,
                    });

                    /**
                     * Find the start and end period data from the array instead of depending
                     * on the indexing of the array
                     */
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.UPDATED_DATE_CBU,
                        value: startPeriod,
                    });
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.UPDATED_DATE_NEXT_CBU,
                        value: endPeriod,
                    });
                } catch (error) {
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.UPDATED_DATE_CBU,
                        value: null,
                    });
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.UPDATED_DATE_NEXT_CBU,
                        value: null,
                    });
                    // Set Dashboard State
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.DASHBOARD_STATE,
                        value: {
                            imgSrc: errorStateNotFoundUrl,
                            title: i18n.t('monitoringDashboard.states.noClockTitle'),
                            description: i18n.t('monitoringDashboard.states.noClockDescription'),
                        } as DashboardState,
                    });
                } finally {
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.CALENDAR_LOADING,
                        value: false,
                    });
                }
                break;
            }

            case ROOT_STATE_CONST.USER_ROLES: {
                let rolesInfo;
                try {
                    const payloadValue = payload.value as SetUserRolesModel;
                    const { data } = await accessManagementService(payloadValue.omsBaseUrl).getRoles();
                    rolesInfo = data.roles.reduce(
                        (prev: Set<string>, curr: AccessManagementTypes.Role) => prev.add(curr.roleName),
                        new Set(),
                    );
                } catch (error) {
                    rolesInfo = new Set();
                } finally {
                    commit(SET_STATE_DATA, { key: ROOT_STATE_CONST.USER_ROLES, value: rolesInfo });
                }

                break;
            }

            case ROOT_STATE_CONST.USER_PROFILE: {
                let userProfile: UserProfile | null = { authProfileId: '', name: '' };
                try {
                    const payloadValue = payload.value as SetUserRolesModel;
                    const { data: authData } = await accessManagementService(
                        payloadValue.omsBaseUrl,
                    ).getEnrichedContext({
                        objectJID: 'user.zeta.in',
                    });
                    if (authData) {
                        userProfile = { authProfileId: authData.subject.authProfileId, name: authData.subject.name };
                    }
                } catch (error) {
                    userProfile = null;
                } finally {
                    commit(SET_STATE_DATA, { key: ROOT_STATE_CONST.USER_PROFILE, value: userProfile });
                }

                break;
            }

            case ROOT_STATE_CONST.CUTOFF_SCHEDULE_DETAILS: {
                const scheduleValue: CutoffScheduleDetails = {
                    data: null,
                    error: null,
                    isLoading: true,
                };
                try {
                    const { serviceUrl, ...payloadValue } = payload.value as CalendarTypes.GetCutoffScheduleRequest & {
                        serviceUrl: string;
                    };

                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.CUTOFF_SCHEDULE_DETAILS,
                        value: scheduleValue,
                    });
                    const { data: scheduleResponse } = await calendarService(serviceUrl).getScheduleDetails(
                        payloadValue,
                    );
                    if (scheduleResponse.length) {
                        const scheduleDetails = scheduleResponse[0];
                        const { cronExpression } = scheduleDetails;
                        const currentCBU = state[ROOT_STATE_CONST.CURRENT_CBU] as CalendarTypes.GetCurrentCBUResponse;
                        const { timeString, timestamp } = convertCronToLocalTimeAndTimestamp(cronExpression, currentCBU?.formattedStartTime as string);
                        scheduleValue.data = {
                            ...scheduleDetails,
                            scheduleTime: timeString,
                            scheduleTimestamp: timestamp,
                        };
                    }
                } catch (error) {
                    scheduleValue.error = error;
                } finally {
                    scheduleValue.isLoading = false;
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.CUTOFF_SCHEDULE_DETAILS,
                        value: scheduleValue,
                    });
                }
                break;
            }
            case ROOT_STATE_CONST.SCHEDULED_EOD_DETAILS: {
                const scheduleValue: EODRunDetails = {
                    data: null,
                    error: null,
                    isLoading: true,
                };
                try {
                    const { serviceUrl, ...payloadValue } = payload.value as any;

                    const tenantId = payloadValue.tenantId;

                    const jobId = `${payloadValue.tenantCode}.${clusterSpec}.${payloadValue.calendarId}`;

                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.SCHEDULED_EOD_DETAILS,
                        value: scheduleValue,
                    });

                    const requestBody = {
                        serviceName: 'KALE_SERVICE',
                        path: `v1/tenants/${tenantId}/jobs/${jobId}`,
                        method: 'GET',
                        tenantId: tenantId,
                    };
                    const response = await kaleService(serviceUrl).executeRequest(requestBody);
                    const { status, cronExpression, id } = response?.data;

                    scheduleValue.data = {
                        id,
                        status,
                        scheduleTime: cronToHumanTime(cronExpression),
                    };
                } catch (error) {
                    scheduleValue.error = error;
                } finally {
                    scheduleValue.isLoading = false;
                    commit(SET_STATE_DATA, {
                        key: ROOT_STATE_CONST.SCHEDULED_EOD_DETAILS,
                        value: scheduleValue,
                    });
                }
                break;
            }
            // If the payload is not a special case, set the state directly
            // For all other cases
            default: {
                commit(SET_STATE_DATA, payload);
            }
        }
    },
};

export default actions;
