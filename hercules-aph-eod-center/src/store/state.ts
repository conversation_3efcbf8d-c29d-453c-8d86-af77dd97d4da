import { emptyStateUrl } from '../common/constants';
import i18n from '../i18n';
import { RootState } from './types';

const state: RootState = {
    allCoAs: null,
    allCalendars: null,
    calendarPhaseCoAs: null,
    calendarCoAs: null,
    selectedCalendar: null,
    selectedCalendarDetails: null,
    tenantId: null,
    calendarLoading: true,
    calendarCoALoading: true,
    currentCBU: null,
    updatedDateCBU: null,
    updatedDateNextCBU: null,
    cBULoading: true,
    dashboardState: {
        imgSrc: emptyStateUrl,
        title: i18n.t('monitoringDashboard.states.emptyStateTitle') as string,
        description: i18n.t('monitoringDashboard.states.emptyStateDescription') as string,
    },
    ledgerIdsToBeSkipped: [],
    selectedCalendarCoA: null,
    selectedCalendarDate: null,
    userRoles: new Set(),
    userProfile: {},
    eodRunInfo: null,
    checklistTableData: null,
    workerTableData: null,
    cutoffScheduleDetails: null,
    scheduledEODDetails: null,
    periods: []
};

export default state;
