// import dependencies
import './router/hooks';
import './scss/gdsoverrides.scss';
import './scss/main.scss';
import { installGlobalPlugins } from './setup';
import AphroditeCoreRegistry from '@zeta-business/aphrodite-core/registerComponents';
import CalendarManagementRegistry from '@zeta-business/calendar-management/registerComponents';
import CalendarRegistry from '@zeta-business/calendar-components/registerComponents';
import  RealtimeWorkerRegistry from "@zeta-business/worker-list/registerComponents";
import ChartOfAccountsRegistry from '@zeta-business/chart-of-accounts/registerComponents';
import LedgerRegistry from '@zeta-business/ledger/registerComponents';
import OrchestraObservabilityRegistry from '@zeta-business/orchestra-observability/registerComponents';
import ServiceRequestsComponentRegistry from '@zeta-business/service-requests/registerComponents';
import TransactionPoliciesRegistry from '@zeta-business/transaction-policies/registerComponents';
import CoaManagementRegistry from '@zeta-business/coa-management/registerComponents';
import { BUEFY_DEFAULT_CONFIG, Buefy } from '@zeta-design/galaxy/core';
import Vue from 'vue';

export default function beforeMount() {
    //Things to do before application instance is to be mounted on DOM
    installGlobalPlugins();
    AphroditeCoreRegistry.load('AphHeader');
    ChartOfAccountsRegistry.load('ChartOfAccountsBalances');
    ChartOfAccountsRegistry.load('ChartOfAccountsView');
    ChartOfAccountsRegistry.load('ChartOfAccountsTreeView');
    OrchestraObservabilityRegistry.load('CalendarOverview');
    OrchestraObservabilityRegistry.load('CalendarPeriodView');
    OrchestraObservabilityRegistry.load('CoaBatchRunList');
    OrchestraObservabilityRegistry.load('CoaBatchWorkerDetailList');
    OrchestraObservabilityRegistry.load('CoaTrackerList');
    OrchestraObservabilityRegistry.load('FailedLedgerTable');
    OrchestraObservabilityRegistry.load('WorkerDetails');
    OrchestraObservabilityRegistry.load('PeriodsList');
    OrchestraObservabilityRegistry.load('SkipLedgersForm');
    OrchestraObservabilityRegistry.load('SkippedLedgers');
    OrchestraObservabilityRegistry.load('RunEodProcessModal');
    OrchestraObservabilityRegistry.load('PhaseCycle');
    OrchestraObservabilityRegistry.load('DashboardOverviewTiles');
    OrchestraObservabilityRegistry.load('RequestHistory');
    OrchestraObservabilityRegistry.load('ChecklistTasks');
    CalendarManagementRegistry.load('CalendarView');
    CalendarManagementRegistry.load('ClocksList');
    CalendarRegistry.load('CalendarDetails');
    CalendarRegistry.load('ClocksListV2');
    CalendarRegistry.load('ClockDetails');
    CalendarRegistry.load('CyclesListV2');
    CalendarRegistry.load('PeriodsListV2');
    RealtimeWorkerRegistry.load('WorkerList')
    LedgerRegistry.load('LedgerList');
    LedgerRegistry.load('LedgerView');
    LedgerRegistry.load('LedgerVectorsList');
    LedgerRegistry.load('LedgerTagsList');
    LedgerRegistry.load('PostingCategorySummaries');
    OrchestraObservabilityRegistry.load('CutoffModal');
    ServiceRequestsComponentRegistry.load('BusinessRequestListing');
    ServiceRequestsComponentRegistry.load('BusinessRequestDetails');
    TransactionPoliciesRegistry.load('FilteredTransactionPoliciesList');
    CoaManagementRegistry.load('BalanceExplorer');
    CoaManagementRegistry.load('SettingsPanel');
    Vue.use(Buefy, BUEFY_DEFAULT_CONFIG);
    //Things to do before application instance is to be mounted on DOM
    import('@zeta-design/galaxy/styles').then((gdsDesign: any) => {
        gdsDesign.load({
            root: document.head,
            version: '0.14.10',
        });
    });
}
