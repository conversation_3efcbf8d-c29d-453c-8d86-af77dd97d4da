{"common": {"appTitle": "EOD Center", "topHeader": {"requests": "Requests", "dashboard": "Dashboard", "calendars": "Calendars", "livedashboard": "Live Dashboard", "search": "Search"}, "bookDate": "Book Date", "notFound": {"title": "Page Not Found", "message": "The page you are looking for doesn't exist.", "closeLabel": "Close message"}, "apiErrorMessage": "Oops! Something Went Wrong", "soonMessage": "Coming soon!", "buttons": {"view": "View", "edit": "Edit", "delete": "Delete", "save": "Save", "create": "Create", "cancel": "Cancel"}, "currency": {"INR": "India Rupee", "ALL": "Albania Lek", "AFN": "Afghanistan Afghani", "ARS": "Argentina Peso", "AWG": "Aruba Guilder", "AUD": "Australia Dollar", "AZN": "Azerbaijan Manat", "BSD": "Bahamas Dollar", "BBD": "Barbados Dollar", "BYN": "Belarus Ruble", "BZD": "Belize Dollar", "BMD": "Bermuda Dollar", "BOB": "Bolivia Bolíviano", "BAM": "Bosnia and Herzegovina Convertible Mark", "BWP": "Botswana Pula", "BGN": "Bulgaria Lev", "BRL": "Brazil Real", "BND": "Brunei Darussalam Dollar", "KHR": "Cambodia Riel", "CAD": "Canada Dollar", "KYD": "Cayman Islands Dollar", "CLP": "Chile Peso", "CNY": "China Yuan Renminbi", "COP": "Colombia Peso", "CRC": "Costa Rica Colon", "HRK": "Croatia Kuna", "CUP": "Cuba Peso", "CZK": "Czech Republic Koruna", "DKK": "Denmark Krone", "DOP": "Dominican Republic Peso", "XCD": "East Caribbean Dollar", "EGP": "Egypt Pound", "SVC": "El Salvador Colon", "EUR": "Euro Member Countries", "FKP": "Falkland Islands (Malvinas) Pound", "FJD": "Fiji Dollar", "GHS": "Ghana Cedi", "GIP": "Gibraltar Pound", "GTQ": "Guatemala Quetzal", "GGP": "Guernsey Pound", "GYD": "Guyana Dollar", "HNL": "Honduras Lempira", "HKD": "Hong Kong Dollar", "HUF": "Hungary Forint", "ISK": "Iceland Krona", "IDR": "Indonesia Rupiah", "IRR": "Iran Rial", "IMP": "Isle of Man Pound", "ILS": "Israel Shekel", "JMD": "Jamaica Dollar", "JPY": "Japan Yen", "JEP": "Jersey Pound", "KZT": "Kazakhstan Tenge", "KPW": "Korea (North) Won", "KRW": "Korea (South) Won", "KGS": "Kyrgyzstan Som", "LAK": "Laos Kip", "LBP": "Lebanon Pound", "LRD": "Liberia Dollar", "MKD": "Macedonia Denar", "MYR": "Malaysia Ringgit", "MUR": "Mauritius Rupee", "MXN": "Mexico Peso", "MNT": "Mongolia Tughrik", "MZN": "Mozambique Metical", "NAD": "Namibia Dollar", "NPR": "Nepal Rupee", "ANG": "Netherlands Antilles Guilder", "NZD": "New Zealand Dollar", "NIO": "Nicaragua Cordoba", "NGN": "Nigeria Naira", "NOK": "Norway Krone", "OMR": "Oman Rial", "PKR": "Pakistan Rupee", "PAB": "Panama Balboa", "PYG": "Paraguay Guarani", "PEN": "Peru Sol", "PHP": "Philippines Peso", "PLN": "Poland Zloty", "QAR": "Qatar Riyal", "RON": "Romania Leu", "RUB": "Russia Ruble", "SHP": "<PERSON>", "SAR": "Saudi Arabia Riyal", "RSD": "Serbia Dinar", "SCR": "Seychelles Rupee", "SGD": "Singapore Dollar", "SBD": "Solomon Islands Dollar", "SOS": "Somalia Shilling", "ZAR": "South Africa Rand", "LKR": "Sri Lanka Rupee", "SEK": "Sweden Krona", "CHF": "Switzerland Franc", "SRD": "Suriname Dollar", "SYP": "Syria Pound", "TWD": "Taiwan New Dollar", "THB": "Thailand Baht", "TTD": "Trinidad and Tobago Dollar", "TRY": "Turkey Lira", "TVD": "Tuvalu Dollar", "UAH": "Ukraine Hryvnia", "GBP": "United Kingdom Pound", "USD": "United States Dollar", "UYU": "Uruguay Peso", "UZS": "Uzbekistan Som", "VEF": "Venezuela Bolívar", "VND": "Viet Nam Dong", "YER": "Yemen Rial", "ZWD": "Zimbabwe Dollar"}}, "calendarManagement": {"calendar": "Calendar", "calendarID": "Calendar ID", "calendarCode": "Calendar Code", "cycleCode": "Cycle Code", "cycles": "Cycles", "listOfPeriods": "List of Periods in Cycle", "periodsForCBU": "Periods for CBU", "cbuPeriodsInCycle": "CBU Periods in Cycle", "viewPeriod": "View Period", "settings": "Settings", "cbd": "CBD", "overview": {"title": "Overview"}, "clockStartTime": "Clock Start Time", "clockCode": "Code", "timeformat": {"am": "am", "pm": "pm"}, "clockName": "Clock Name", "cbuPeriods": "CBU Periods", "coa": {"title": "Chart Of Accounts", "nodes": {"title": "Nodes"}, "overview": {"title": "Overview"}, "internalLedgers": {"title": "System Ledgers"}, "accountHolderLedgers": {"title": "Account <PERSON><PERSON>"}, "workers": {"title": "Workers", "subTitle": "List of All workers and their details, for the CoA"}, "viewLedger": {"title": "Ledger Details", "menuHeader": "ON THIS PAGE", "menuItems": {"vectors": "Vectors", "transactionPolicies": "Transaction Policies", "tags": "Tags", "postingCategorySummaries": "Posting Category Summaries"}}}, "workersList": {"title": "Workers List", "description": "A list of all the workers with their periodicities"}, "failedLedgers": {"title": "Failed Ledgers"}, "createCalendar": {"title": "Create Calendar", "subTitle": "this is subtitle", "name": {"title": "Calendar Name", "placeholder": "Enter Calendar Name..."}, "code": {"title": "Code", "placeholder": "Enter Code..."}, "timezone": {"title": "Timezone", "placeholder": "Select Timezone"}, "day": {"title": "Start of Day", "placeholder": "Select Day"}, "week": {"title": "Start of Week", "placeholder": "Select week"}, "year": {"title": "Start of Year", "placeholder": "Select Year"}, "ctaButton": "Create", "editCTAButton": "EDIT", "goBack": "Cancel", "ctaOkay": "Okay", "successMessage": "\"<b>{calendarName}</b>\" Created", "editSuccessMessage": "Calendar details updated successfully.", "errorMessage": "Calendar was not created due to an unknown error. Please try again later."}, "list": {"title": "Calendar Management", "subTitle": "Each CoA belongs to a Calendar maintained by the Organization. A Calendar belongs to a specific Time Zone.", "create": "Create Calendar", "edit": "Edit Calendar", "showAll": "Show All", "showLess": "Show Less", "emptyListMessage": "There’s no data at the moment. Once you create, the relevant data will be displayed here. Start creating!"}, "lastUpdated": "Last updated at", "pageRefreshed": "Page Refreshed", "currentBookDate": "Current Book Date", "lastClosedBookDate": "Last EOP Book Date", "coaPhaseDetail": {"batchRun": "Batch Run Details", "trackerInfo": "Tracker Information"}, "workerDetail": {"phase": "Phase", "title": "Excecution ID :", "workerInfo": "Worker Information", "workerDashboard": "Worker Dashboard", "pageRefreshed": "Worker Details has been updated", "failedLedgers": {"title": "Failed ledgers:", "description": "Interest Calculation Daily Worker"}}, "batchRun": {"executionId": "EXECUTION ID", "startTime": "START TIME", "lastUpdatedTime": "LAST UPDATED TIME", "status": "STATUS"}}, "error": {"generic": {"header": "An error occurred", "message": "The information you are looking for is not available right now."}, "noData": {"header": "No results found", "message": "There are no results at the moment."}, "unauthorized": {"header": "Not authorized", "message": "Either you don't have access to see this information or your session has expired."}, "calendarDetailsFailure": {"header": "Something went wrong", "message": "Something went wrong while getting the Calendar Details"}, "periodFailure": {"header": "Something went wrong", "success": "Phase Change triggered successfully.", "failure": "There was an error in triggering phase change."}, "coaFailure": {"header": "Something went wrong", "message": "Something went wrong while fetching COA Details"}, "cantStartEoD": {"header": "Can't Start EoD Run!", "message": "There's an active schedule that cuts off the book date {scheduleTime}. Please pause this schedule before starting the EOD run."}, "cantStartPhase": {"header": "Can't Start Phase Run!", "message": "There's an active schedule that cuts off the book date {scheduleTime}. Please pause this schedule before starting the Phase run."}}, "alertsDashboard": {"title": "Monitoring Dashboard", "pageRefreshed": "Dashboard  has been updated"}, "phaseRuns": {"title": "All Phase Runs", "cbd": "CBD", "header": "Phase Runs"}, "monitoringDashboard": {"title": "Monitoring Dashboard", "dashboard": "Dashboard", "currentBookDate": "Current Book Date", "alerts": {"phaseBlocked": "EoD Run Blocked - {phase} phase has unresolved issues"}, "currentBookDateBadge": "Current Book Date (CBD)", "lastEOPBookDate": "Last EOP Book Date", "dashboardUpdated": "Dashboard updated!", "dashboardUpdateFailed": "Failed to update dashboard", "coAs": "CoAs", "selectCoA": "Please select atleast one CoA", "coaName": "CoA Name", "checklistItem": "checklist item | checklist items", "allRuns": "All Runs", "workersFailedMessage": "{failedWorkersCount} worker failed", "notScheduled": "not scheduled", "cutoff": "Cutoff", "notSet": "Not Set", "executed": "Executed", "scheduleCutoff": "Schedule Cutoff", "cutoffNow": "Cut off now", "pauseSchedule": "Pause Schedule", "resumeSchedule": "Resume Schedule", "editSchedule": "Edit Schedule", "deleteSchedule": "Delete Schedule", "phaseTimelines": "Phase Timelines", "eodProgress": "EoD Run: In progresss", "eodScheduled": "EoD Run: scheduled at", "scheduledAt": "Scheduled at", "at": "at", "activeScheduledEODescription": "The EoD run for the current book date, <strong>{currentBookDate}</strong>, will be executed at the scheduled time of <strong>{scheduleEODTime}</strong>.", "pauseScheduledEODescription": "The schedule for executing the EoD run for the current book date at <strong>{scheduleEODTime}</strong> is currently paused. The EoD run will need to be started manually.", "cutoffNotSetDescription": "The current book date <strong>{currentBookDate}</strong> will be cut off during the EOD run. You can create a schedule or cut off the book date now.", "cutoffExecutedScheduleNotSet": "Cutoff time has not been scheduled for the next book date. You can create a new schedule or cut off the next book date when it becomes active after the EOD run.", "scheduleActiveCutOffPendingText": "The current book date, <strong>{currentBookDate}</strong> will be cut off at the scheduled time of <strong>{scheduleTime}</strong>. After that, live transactions will be posted to the next book date, <strong>{nextBookDate}</strong>.", "schedulePausedCutOffPendingText": "The schedule for cutting off current book date at <strong>{scheduleTime}</strong> is currently paused. Transactions will have to be manually cut off. Alternatively, they will implicitly cut off when eod run is triggered.", "scheduleActiveCutOffExecutedText": "The next book date <strong>{nextBookDate}</strong> will be cut-off at the scheduled time of <strong>{scheduleTime}</strong>.", "schedulePausedCutOffExecutedText": "The schedule for cutting off current book date at <strong>{scheduleTime}</strong> is currently paused.", "cutoffExecutedSchedulePausedDescription": "Cutoff scheduled at <strong>{scheduleTime}</strong> is currently on pause. The next book date <strong>{nextBookDate}</strong> will be cut off during the EOD run.", "cutoffNotExecutedSchedulePausedDescription": "Cutoff scheduled at <strong>{scheduleTime}</strong> is currently on pause. You can cut off the book date now, or the EOD run will trigger an implicit cutoff.", "workersForCoa": {"title": "Workers For CoA", "description": "List of All workers and their details, for the CoA"}, "actionsTitle": "Actions", "states": {"noClockTitle": "Failed to fetch clock", "noClockDescription": "Either multiple or no clocks are present in the selected calendar.", "noCoAsTitle": "No CoAs found", "noCoAsDescription": "There are no CoAs associated with this calendar.", "emptyStateTitle": "Start by selecting a Calendar and a CoA", "emptyStateDescription": "Make a selection to monitor the EoD process for the Calendar and the CoA relevant to you"}, "selectCalendar": "Select a Calendar", "coaBalances": "Balances", "dashboardTabs": {"overview": "Overview", "realTimeWorkers": "Real-time Workers", "workers": "Workers", "checklist": "EoD Checklist", "atdChecklist": "ATD Checklist", "supervisors": "Supervisors", "observations": "Observations", "exceptions": "Exceptions", "requestHistory": "Request History"}, "workerTabs": {"failedLedgers": "Failed Ledgers", "phaseRuns": "Phase Runs"}, "checklist": {"allow": "Allow", "forceAllow": "Force Allow", "actionType": "Allow Checklist Item", "ruleReasonCodeMandatory": "Reason Code is mandatory to fill", "reasonCodePlaceholder": "Enter Reason Code", "additionalCommentsPlaceholder": "Enter Reason", "nonBlocker": "(Non Blocker)", "reasonCode": {"failedReasonCode": "<PERSON><PERSON> moved to Deny State as 1 or more tasks have failed", "allowedReasonCode": "<PERSON><PERSON> moved to Allow State as all tasks have been completed"}, "modal": {"description": "All incomplete tasks will be marked as ", "skipped": "skipped", "complete": "complete", "reasonCode": "Reason Code", "additionalComments": "Additional Comments", "goToTasks": "Go to Tasks"}, "sidesheet": {"tooltip": {"allowManual": "Allowing will mark all incomplete tasks as completed and the checklist item will be allowed", "allowAutomated": "Force allowing will mark all incomplete tasks as skipped and the checklist item will be force allowed"}}, "error": {"header": "Something went wrong", "description": "An error occured while updating the checklist item"}, "success": {"checklistUpdated": "Checklist Updated", "tasksCompleted": "Workflow created to complete tasks & allow checklist", "allowedChecklist": "Allowed Checklist Item"}}, "actions": {"startEodRun": "Start EoD Run", "retryEodRun": "Retry EoD Run", "startPhaseRun": "Start Phase Run", "skipLedgers": "<PERSON><PERSON>gers", "viewPeriods": "View Periods", "viewPhaseLogs": "View Phase Logs", "viewGrafana": "View EoD Status", "viewClearingStatus": "View Clearing Status", "workersMasterList": "Workers Master List", "manualCutOff": "Manual Cut-off", "createCutoffSchedule": "Create Schedule", "deleteCutoffSchedule": "Delete Schedule", "editCutoffSchedule": "Edit Schedule", "pauseCutoffSchedule": "Pause Schedule", "resumeCutoffSchedule": "Resume Schedule", "cutoff": "Scheduled Cut-off"}, "viewCalendar": {"pageRefreshed": "Calendar Phase Details have been updated", "calendarOptions": {"workersList": "Workers List"}}, "batchRunDetails": {"pageRefreshed": "Batch Run Details have been updated"}, "headings": {"basicDetails": "Basic Details", "ledgerDetails": "Ledger Details", "accountDetails": "Account Details", "balance": "Balance", "accountHolder": "Account Holder", "productDetails": "Product Details", "transactionPolicies": "Transaction Policies", "otherAttributes": "Other Attributes", "attributes": "Attributes", "onThisPage": "On this page"}, "skipLedgers": {"toast": {"title": "Ledgers will be skipped!", "description": "Check more details on Ski<PERSON> Ledgers"}, "routes": {"skippedRoutesLabel": "Skipped Ledgers", "failedLedgersLabel": "Failed ledgers"}}, "scheduledEODModel": {"resumeTitle": "Resume Schedule?", "pauseTitle": "Pause Schedule?", "requestIdMessage": "<a href='/requests/{requestId}'> RequestID : {requestId} </a>", "submit": "Submit Request", "cancel": "Cancel", "resumeDescription": "The EoD run will be executed at the scheduled time of <strong>{scheduleEODTime}</strong> for the current book date.", "pauseDescription": "Pause the schedule for executing the EoD run for the current book date at <strong>{scheduleEODTime}</strong>.", "resumeSuccess": "Schedule resumed successfully", "pauseSuccess": "Schedule paused successfully", "notStarted": {"TextOnComponent": "EoD Run: Scheduled at 07:00 PM", "PopupText": "The EoD run for the current book date, January 25, 2025, is scheduled to execute at 07:00 PM.", "ActionPopupText": "The EoD schedule for 07:00 PM will be paused indefinitely. You will need to manually trigger the EoD run thereafter."}}, "eodSuccess": {"title": "EoD completed successfully!", "description": "End of Day has been completed successfully for the book date: {{bookDate}}", "toCurrentBookDate": "Take me to Current Book Date", "stayOnThisPage": "Stay on this page"}}, "requests": {"businessRequests": "Business Requests"}, "balanceExplorer": {"error": {"title": "Balances not yet generated for current book date", "description": "Balances get calculated a little while post EOP phase completion"}}}