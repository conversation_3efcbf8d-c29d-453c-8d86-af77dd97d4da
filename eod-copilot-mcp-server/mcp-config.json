{"mcpServers": {"eod-copilot": {"command": "node", "args": ["dist/index.js"], "cwd": "/Users/<USER>/Desktop/eod/eod-copilot-mcp-server", "env": {"NODE_ENV": "production", "BASE_URL": "https://sb1-god-aura.mum1-pp.zetaapps.in", "TENANT_ID": "600309", "OPENAI_ENDPOINT": "https://zeta-merlin-openai-eastus2.openai.azure.com/openai/deployments/gpt-4o-mini-sre-home/chat/completions", "OPENAI_API_KEY": "********************************", "ENABLE_AI_ENHANCEMENT": "true", "LOG_LEVEL": "info"}}}}