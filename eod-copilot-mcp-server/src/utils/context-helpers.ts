/**
 * Context Helper Utilities
 * Shared utilities for creating request contexts and related objects
 */

import { CalendarInfo } from '../types/index.js';
import { serverConfig } from '../config/index.js';

/**
 * Create a CalendarInfo object with proper type safety
 */
export function createCalendarInfo(id: string, name: string = 'Selected Calendar'): CalendarInfo {
  return {
    id,
    name,
    timezone: 'UTC',
    status: 'ACTIVE',
    tenantId: serverConfig.tenantId,
  };
}

/**
 * Create a CalendarInfo object conditionally (returns undefined if id is not provided)
 */
export function createCalendarInfoOptional(id?: string, name: string = 'Selected Calendar'): CalendarInfo | undefined {
  return id ? createCalendarInfo(id, name) : undefined;
}
