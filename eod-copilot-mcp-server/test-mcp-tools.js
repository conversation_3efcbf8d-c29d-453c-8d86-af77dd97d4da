#!/usr/bin/env node

/**
 * Test MCP Tool Registration
 * Verifies that all tools are properly registered and accessible
 */

// Set up environment
process.env.BASE_URL = 'https://sb1-god-aura.mum1-pp.zetaapps.in';
process.env.TENANT_ID = '600309';
process.env.NODE_ENV = 'development';

async function testToolRegistration() {
  console.log('🧪 Testing MCP Tool Registration...\n');

  try {
    // Import the tools registry
    const { allTools, toolMetadata } = await import('./dist/tools/index.js');
    
    console.log('✅ Successfully imported tools registry');
    console.log(`📋 Total tools registered: ${allTools.length}`);
    
    // Check for new tools
    const newToolNames = [
      'get_complete_eod_status',
      'get_eod_flow_data', 
      'check_eod_completion',
      'process_natural_language_query',
      'explain_eod_status',
      'troubleshoot_eod_issue'
    ];

    console.log('\n🔍 Checking for new tools...');
    const foundTools = [];
    const missingTools = [];

    newToolNames.forEach(toolName => {
      const tool = allTools.find(t => t.name === toolName);
      if (tool) {
        foundTools.push(toolName);
        console.log(`✅ ${toolName} - Found`);
      } else {
        missingTools.push(toolName);
        console.log(`❌ ${toolName} - Missing`);
      }
    });

    console.log('\n📊 New Tools Summary:');
    console.log(`✅ Found: ${foundTools.length}/${newToolNames.length}`);
    console.log(`❌ Missing: ${missingTools.length}/${newToolNames.length}`);

    // Check tool categories
    console.log('\n🗂️ Tool Categories:');
    toolMetadata.categories.forEach(category => {
      console.log(`📁 ${category.name}: ${category.tools.length} tools`);
      console.log(`   Description: ${category.description}`);
    });

    // Test tool schemas
    console.log('\n🔧 Testing Tool Schemas...');
    const newTools = allTools.filter(tool => newToolNames.includes(tool.name));
    
    newTools.forEach(tool => {
      console.log(`\n🛠️ ${tool.name}:`);
      console.log(`   Description: ${tool.description}`);
      console.log(`   Has Handler: ${typeof tool.handler === 'function'}`);
      console.log(`   Has Schema: ${!!tool.inputSchema}`);
      console.log(`   Required Params: ${tool.inputSchema?.required?.join(', ') || 'None'}`);
    });

    // Test a simple tool call
    console.log('\n🧪 Testing Tool Execution...');
    
    const flowDataTool = allTools.find(t => t.name === 'get_eod_flow_data');
    if (flowDataTool && typeof flowDataTool.handler === 'function') {
      try {
        console.log('🔄 Executing get_eod_flow_data...');
        const result = await flowDataTool.handler({
          tenantId: '600309'
        });
        
        console.log('📋 Execution result:', {
          success: result.success,
          hasData: !!result.data,
          hasMessage: !!result.message,
          errorType: result.error ? 'Expected (no auth)' : 'None'
        });
        
      } catch (error) {
        console.log('📋 Expected error (no authentication):', error.message.substring(0, 100));
      }
    }

    console.log('\n🎉 Tool registration test completed successfully!');
    
    return {
      totalTools: allTools.length,
      newToolsFound: foundTools.length,
      newToolsExpected: newToolNames.length,
      categoriesCount: toolMetadata.categories.length,
      allNewToolsPresent: foundTools.length === newToolNames.length
    };

  } catch (error) {
    console.error('❌ Tool registration test failed:', error.message);
    throw error;
  }
}

async function testServerMetadata() {
  console.log('\n🧪 Testing Server Metadata...');
  
  try {
    const { toolMetadata } = await import('./dist/tools/index.js');

    if (toolMetadata) {
      console.log('✅ Server metadata retrieved');
      console.log('📋 Metadata:', {
        version: toolMetadata.version,
        totalTools: toolMetadata.totalTools,
        categories: toolMetadata.categories.length
      });

      // Check if new categories are present
      const hasEoDFlow = toolMetadata.categories.some(cat => cat.name === 'EoD Flow Operations');
      const hasIntelligent = toolMetadata.categories.some(cat => cat.name === 'Intelligent Query Processing');

      console.log('📋 New categories present:', {
        eodFlow: hasEoDFlow,
        intelligentQuery: hasIntelligent
      });

    } else {
      console.log('⚠️ toolMetadata not found');
    }
    
  } catch (error) {
    console.log('📋 Metadata test error:', error.message);
  }
}

async function main() {
  try {
    const results = await testToolRegistration();
    await testServerMetadata();
    
    console.log('\n🎯 Final Test Results:');
    console.log('='.repeat(50));
    console.log(`✅ Total Tools: ${results.totalTools}`);
    console.log(`✅ New Tools Found: ${results.newToolsFound}/${results.newToolsExpected}`);
    console.log(`✅ Categories: ${results.categoriesCount}`);
    console.log(`✅ All New Tools Present: ${results.allNewToolsPresent ? 'YES' : 'NO'}`);
    
    if (results.allNewToolsPresent) {
      console.log('\n🎉 SUCCESS: All new tools are properly registered and functional!');
      console.log('🚀 The EoD Center MCP Server is ready for production use.');
    } else {
      console.log('\n⚠️ WARNING: Some new tools are missing from registration.');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

main();
