#!/usr/bin/env node

/**
 * Test MCP Client for EoD Center MCP Server
 * Tests the new functionality including EoD flow and intelligent queries
 */

import { spawn } from 'child_process';
import { createInterface } from 'readline';

class MCPTestClient {
  constructor() {
    this.requestId = 1;
    this.server = null;
    this.rl = null;
  }

  async start() {
    console.log('🚀 Starting EoD Center MCP Server Test Client...\n');
    
    // Start the MCP server
    this.server = spawn('npm', ['run', 'dev'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: process.cwd()
    });

    this.server.stderr.on('data', (data) => {
      // Filter out npm and build messages, only show server logs
      const message = data.toString();
      if (message.includes('info:') || message.includes('error:') || message.includes('warn:')) {
        console.log('📋 Server:', message.trim());
      }
    });

    // Wait for server to initialize
    await this.waitForServerReady();
    
    console.log('✅ Server is ready! Starting tests...\n');
    
    // Run tests
    await this.runTests();
    
    // Cleanup
    this.cleanup();
  }

  async waitForServerReady() {
    return new Promise((resolve) => {
      const checkReady = (data) => {
        if (data.toString().includes('Server is ready')) {
          this.server.stderr.removeListener('data', checkReady);
          setTimeout(resolve, 1000); // Give it a moment to fully initialize
        }
      };
      this.server.stderr.on('data', checkReady);
    });
  }

  async sendRequest(method, params = {}) {
    const request = {
      jsonrpc: '2.0',
      id: this.requestId++,
      method,
      params
    };

    console.log(`📤 Sending: ${method}`);
    console.log(`   Params:`, JSON.stringify(params, null, 2));

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const handleResponse = (data) => {
        try {
          const response = JSON.parse(data.toString());
          if (response.id === request.id - 1) {
            clearTimeout(timeout);
            this.server.stdout.removeListener('data', handleResponse);
            resolve(response);
          }
        } catch (e) {
          // Ignore parsing errors, might be partial data
        }
      };

      this.server.stdout.on('data', handleResponse);
      this.server.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async runTests() {
    const tests = [
      () => this.testInitialize(),
      () => this.testListTools(),
      () => this.testEoDFlowData(),
      () => this.testCompleteEoDStatus(),
      () => this.testNaturalLanguageQuery(),
      () => this.testExplainEoDStatus(),
      () => this.testTroubleshootIssue(),
      () => this.testCheckEoDCompletion(),
    ];

    for (let i = 0; i < tests.length; i++) {
      try {
        console.log(`\n🧪 Test ${i + 1}/${tests.length}:`);
        await tests[i]();
        console.log('✅ Test passed');
      } catch (error) {
        console.log('❌ Test failed:', error.message);
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  async testInitialize() {
    console.log('Testing MCP initialization...');
    const response = await this.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {
        tools: {}
      },
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    });

    if (response.error) {
      throw new Error(`Initialize failed: ${response.error.message}`);
    }

    console.log('📋 Server capabilities:', Object.keys(response.result.capabilities));
  }

  async testListTools() {
    console.log('Testing tools/list...');
    const response = await this.sendRequest('tools/list');

    if (response.error) {
      throw new Error(`List tools failed: ${response.error.message}`);
    }

    const tools = response.result.tools;
    console.log(`📋 Found ${tools.length} tools`);
    
    // Check for new tools
    const newTools = [
      'get_complete_eod_status',
      'get_eod_flow_data', 
      'check_eod_completion',
      'process_natural_language_query',
      'explain_eod_status',
      'troubleshoot_eod_issue'
    ];

    const foundNewTools = newTools.filter(tool => 
      tools.some(t => t.name === tool)
    );

    console.log(`📋 New tools found: ${foundNewTools.join(', ')}`);
    
    if (foundNewTools.length !== newTools.length) {
      throw new Error(`Missing new tools. Expected ${newTools.length}, found ${foundNewTools.length}`);
    }
  }

  async testEoDFlowData() {
    console.log('Testing get_eod_flow_data...');
    const response = await this.sendRequest('tools/call', {
      name: 'get_eod_flow_data',
      arguments: {
        tenantId: '600309'
      }
    });

    if (response.error) {
      throw new Error(`EoD flow data failed: ${response.error.message}`);
    }

    const result = response.result;
    console.log('📋 EoD Flow Data Result:', {
      success: result.content?.[0]?.text?.includes('success'),
      hasFlowSteps: result.content?.[0]?.text?.includes('flowSteps'),
      hasCalendars: result.content?.[0]?.text?.includes('calendars')
    });
  }

  async testCompleteEoDStatus() {
    console.log('Testing get_complete_eod_status...');
    const response = await this.sendRequest('tools/call', {
      name: 'get_complete_eod_status',
      arguments: {
        tenantId: '600309',
        useAI: false // Disable AI for testing to avoid API calls
      }
    });

    if (response.error) {
      console.log('📋 Expected error (no real API connection):', response.error.message);
    } else {
      console.log('📋 Complete EoD Status executed successfully');
    }
  }

  async testNaturalLanguageQuery() {
    console.log('Testing process_natural_language_query...');
    const response = await this.sendRequest('tools/call', {
      name: 'process_natural_language_query',
      arguments: {
        query: 'What is the current EoD status?',
        tenantId: '600309',
        includeContext: false // Disable context gathering for testing
      }
    });

    if (response.error) {
      console.log('📋 Expected error (no real API connection):', response.error.message);
    } else {
      console.log('📋 Natural language query executed successfully');
    }
  }

  async testExplainEoDStatus() {
    console.log('Testing explain_eod_status...');
    const response = await this.sendRequest('tools/call', {
      name: 'explain_eod_status',
      arguments: {
        tenantId: '600309',
        focus: 'overall'
      }
    });

    if (response.error) {
      console.log('📋 Expected error (no real API connection):', response.error.message);
    } else {
      console.log('📋 Explain EoD status executed successfully');
    }
  }

  async testTroubleshootIssue() {
    console.log('Testing troubleshoot_eod_issue...');
    const response = await this.sendRequest('tools/call', {
      name: 'troubleshoot_eod_issue',
      arguments: {
        issue: 'EoD is stuck in EOFI phase',
        tenantId: '600309'
      }
    });

    if (response.error) {
      console.log('📋 Expected error (no real API connection):', response.error.message);
    } else {
      console.log('📋 Troubleshoot issue executed successfully');
    }
  }

  async testCheckEoDCompletion() {
    console.log('Testing check_eod_completion...');
    const response = await this.sendRequest('tools/call', {
      name: 'check_eod_completion',
      arguments: {
        tenantId: '600309'
      }
    });

    if (response.error) {
      console.log('📋 Expected error (no real API connection):', response.error.message);
    } else {
      console.log('📋 Check EoD completion executed successfully');
    }
  }

  cleanup() {
    console.log('\n🧹 Cleaning up...');
    if (this.server) {
      this.server.kill();
    }
    if (this.rl) {
      this.rl.close();
    }
  }
}

// Run the test client
const client = new MCPTestClient();
client.start().catch(console.error);

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down test client...');
  process.exit(0);
});
