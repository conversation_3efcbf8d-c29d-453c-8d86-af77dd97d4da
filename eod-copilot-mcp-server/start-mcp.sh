#!/bin/bash

# EoD Copilot MCP Server Startup Script
# This script starts the MCP server for GitHub Copilot Chat integration

echo "🚀 Starting EoD Copilot MCP Server..."

# Set environment variables
export NODE_ENV=production
export BASE_URL=https://sb1-god-aura.mum1-pp.zetaapps.in
export TENANT_ID=600309
export OPENAI_ENDPOINT=https://zeta-merlin-openai-eastus2.openai.azure.com/openai/deployments/gpt-4o-mini-sre-home/chat/completions
export OPENAI_API_KEY=********************************
export ENABLE_AI_ENHANCEMENT=true
export LOG_LEVEL=info

# Change to the correct directory
cd "$(dirname "$0")"

# Build the project if needed
if [ ! -d "dist" ]; then
    echo "📦 Building project..."
    npm run build
fi

# Start the MCP server
echo "✅ Starting MCP server..."
node dist/index.js
