#!/usr/bin/env node

/**
 * HTTP Wrapper for EoD Copilot MCP Server
 * Provides REST API access for GitHub Copilot Chat integration
 */

import express from 'express';
import cors from 'cors';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.HTTP_PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Store MCP server process
let mcpServer = null;

// Start MCP server
function startMCPServer() {
  if (mcpServer) {
    mcpServer.kill();
  }

  mcpServer = spawn('node', ['dist/index.js'], {
    cwd: __dirname,
    stdio: ['pipe', 'pipe', 'pipe']
  });

  mcpServer.stderr.on('data', (data) => {
    console.log('MCP Server:', data.toString());
  });

  return mcpServer;
}

// Send MCP request
async function sendMCPRequest(method, params = {}) {
  return new Promise((resolve, reject) => {
    const request = {
      jsonrpc: '2.0',
      id: Date.now(),
      method,
      params
    };

    const timeout = setTimeout(() => {
      reject(new Error('Request timeout'));
    }, 30000);

    const handleResponse = (data) => {
      try {
        const response = JSON.parse(data.toString());
        if (response.id === request.id) {
          clearTimeout(timeout);
          mcpServer.stdout.removeListener('data', handleResponse);
          resolve(response);
        }
      } catch (e) {
        // Ignore parsing errors
      }
    };

    mcpServer.stdout.on('data', handleResponse);
    mcpServer.stdin.write(JSON.stringify(request) + '\n');
  });
}

// Routes

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    mcpServer: mcpServer ? 'running' : 'stopped',
    timestamp: new Date().toISOString()
  });
});

// List available tools
app.get('/tools', async (req, res) => {
  try {
    const response = await sendMCPRequest('tools/list');
    res.json({
      success: true,
      tools: response.result?.tools || [],
      count: response.result?.tools?.length || 0
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Execute tool
app.post('/tools/:toolName', async (req, res) => {
  try {
    const { toolName } = req.params;
    const { arguments: toolArgs = {} } = req.body;

    const response = await sendMCPRequest('tools/call', {
      name: toolName,
      arguments: toolArgs
    });

    if (response.error) {
      return res.status(400).json({
        success: false,
        error: response.error.message,
        code: response.error.code
      });
    }

    res.json({
      success: true,
      result: response.result,
      tool: toolName,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Natural language query endpoint
app.post('/query', async (req, res) => {
  try {
    const { query, includeContext = true } = req.body;

    if (!query) {
      return res.status(400).json({
        success: false,
        error: 'Query is required'
      });
    }

    const response = await sendMCPRequest('tools/call', {
      name: 'process_natural_language_query',
      arguments: {
        query,
        tenantId: '600309',
        includeContext
      }
    });

    if (response.error) {
      return res.status(400).json({
        success: false,
        error: response.error.message
      });
    }

    res.json({
      success: true,
      query,
      response: response.result?.content?.[0]?.text || 'No response',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// EoD status endpoint
app.get('/eod/status', async (req, res) => {
  try {
    const { useAI = true } = req.query;

    const response = await sendMCPRequest('tools/call', {
      name: 'get_complete_eod_status',
      arguments: {
        tenantId: '600309',
        useAI: useAI === 'true'
      }
    });

    if (response.error) {
      return res.status(400).json({
        success: false,
        error: response.error.message
      });
    }

    res.json({
      success: true,
      status: response.result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Troubleshooting endpoint
app.post('/troubleshoot', async (req, res) => {
  try {
    const { issue, phase } = req.body;

    if (!issue) {
      return res.status(400).json({
        success: false,
        error: 'Issue description is required'
      });
    }

    const response = await sendMCPRequest('tools/call', {
      name: 'troubleshoot_eod_issue',
      arguments: {
        issue,
        tenantId: '600309',
        phase
      }
    });

    if (response.error) {
      return res.status(400).json({
        success: false,
        error: response.error.message
      });
    }

    res.json({
      success: true,
      issue,
      troubleshooting: response.result?.content?.[0]?.text || 'No suggestions',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API documentation
app.get('/', (req, res) => {
  res.json({
    name: 'EoD Copilot MCP HTTP Wrapper',
    version: '1.0.0',
    description: 'HTTP API wrapper for EoD Copilot MCP Server',
    endpoints: {
      'GET /health': 'Health check',
      'GET /tools': 'List available tools',
      'POST /tools/:toolName': 'Execute specific tool',
      'POST /query': 'Natural language query',
      'GET /eod/status': 'Get EoD status',
      'POST /troubleshoot': 'Get troubleshooting help'
    },
    examples: {
      query: 'POST /query with {"query": "What is the current EoD status?"}',
      status: 'GET /eod/status?useAI=true',
      troubleshoot: 'POST /troubleshoot with {"issue": "EoD stuck in EOFI"}'
    }
  });
});

// Start server
async function startServer() {
  try {
    console.log('🚀 Starting EoD Copilot HTTP Wrapper...');
    
    // Start MCP server
    startMCPServer();
    
    // Wait for MCP server to initialize
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Start HTTP server
    app.listen(PORT, () => {
      console.log(`✅ HTTP Wrapper running on http://localhost:${PORT}`);
      console.log(`📋 API Documentation: http://localhost:${PORT}`);
      console.log(`🔍 Health Check: http://localhost:${PORT}/health`);
      console.log(`🛠️ Tools List: http://localhost:${PORT}/tools`);
      console.log('');
      console.log('Example usage:');
      console.log(`curl -X POST http://localhost:${PORT}/query -H "Content-Type: application/json" -d '{"query":"What is the EoD status?"}'`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
}

// Handle shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down...');
  if (mcpServer) {
    mcpServer.kill();
  }
  process.exit(0);
});

startServer();
