#!/bin/bash

# EoD Copilot MCP Server Startup Script for VS Code
# This script ensures the server runs from the correct directory with proper environment

# Change to the correct directory
cd "/Users/<USER>/Desktop/eod/eod-copilot-mcp-server"

# Set environment variables
export NODE_ENV=production
export BASE_URL=https://sb1-god-aura.mum1-pp.zetaapps.in
export TENANT_ID=600309
export OPENAI_ENDPOINT=https://zeta-merlin-openai-eastus2.openai.azure.com/openai/deployments/gpt-4o-mini-sre-home/chat/completions
export OPENAI_API_KEY=********************************
export ENABLE_AI_ENHANCEMENT=true
export LOG_LEVEL=info

# Start the MCP server
exec node dist/index.js
