
## 🧠 Prompt: Build MCP Server for EoD Center with API Integration and Full Responses

---

### 🌟 Objective

Create an intelligent **MCP (Modular Command Processor)** Server to:

* Answer natural language questions about the **EoD status**
* Retrieve and correlate **calendar, CoA, periods, CBU, workers, verifiers**
* Trigger user actions (e.g., retry run, force allow)
* Present data in chat-friendly format

---

### ⚙️ Environment Variables

```env
TENANT_ID=600309
BASE_URL=https://sb1-god-aura.mum1-pp.zetaapps.in
```

---

### 📅 MCP Flow Logic with API Calls & Responses

#### 1. ✅ **Fetch Calendars**

**Endpoint**:
`GET /tachyon/calendar/tenants/${TENANT_ID}/calendars`

**Response**:

```json
[
  {
    "id": 6410287133937805564,
    "tenantID": "600309",
    "code": "PCALIN006013",
    "name": "default_calendar",
    "timezone": "Asia/Kolkata",
    "description": "Pearl Calendar 6013",
    "status": "ENABLED"
  }
]
```

🧠 **Action**: Ask user to pick a calendar by name/code. Store `calendar.id`.

---

#### 2. ✅ **Fetch CoAs for Tenant**

**Endpoint**:
`GET /tachyon/coa/tenants/${TENANT_ID}/coas`

**Response**:

```json
[
  {
    "id": 9031959912888385000,
    "tenantID": "600309",
    "calendarID": 6410287133937805564,
    "code": "COAIN001011",
    "name": "1011 COA COAIN001011",
    "status": "ENABLED"
  }
]
```

🧠 **Action**: Filter by `calendarID` and `status === "ENABLED"`, store `coa.id` and `coa.code`.

---

#### 3. ✅ **Fetch Current CBU**

**Endpoint**:
`GET /tachyon/calendar/tenants/${TENANT_ID}/calendars/${calendarId}/currentCBU`

**Response**:

```json
{
  "status": "ACTIVE",
  "liveProcessingState": "CUTOFF",
  "calendarID": 6410287133937805564,
  "clockID": 2763186914397031795,
  "cycleID": 2459641089868266568,
  "startTime": 1215068400000,
  "nextPeriodStartTime": 1215154800000,
  "formattedStartTime": "2008-07-03T00:00-07:00[America/Los_Angeles]",
  "formattedNextPeriodStartTime": "2008-07-04T00:00-07:00[America/Los_Angeles]",
  "phaseTransitionHistory": {
    "ACTIVE": { "startTime": 1750198717948 },
    "BOFI": { "startTime": 1750197357658, "endTime": 1750198717948 }
  }
}
```

🧠 **Action**: Determine current **EoD phase** and **CUTOFF** state. Store CBU metadata.

---

#### 4. ✅ **Fetch Periods**

**Endpoint**:

```http
GET /tachyon/calendar/tenants/${TENANT_ID}/calendars/${calendarId}/clocks/${clockId}/cycles/${cycleId}/periods
    ?startDate=${formattedStartTime}
    &endDate=${formattedNextPeriodStartTime+1d}
```

**Response**:

```json
[
  { "id": 5414727110234611702, "status": "ACTIVE" },
  { "id": 4758217489771286437, "status": "INACTIVE" }
]
```

🧠 **Action**: Store `startPeriodId = periods[0].id`, `endPeriodId = periods[1].id`

---

#### 5. ✅ **Fetch Workers**

**Endpoint**:

```http
GET /atalanta/orchestra/api/v1/tenants/${TENANT_ID}/coas/${coaId}/workers-list?startPeriodId=${startPeriodId}&endPeriodId=${endPeriodId}
```

**Response**:

```json
[
  {
    "workerId": "dataPreparationWorker-DAILY-1",
    "workerName": "dataPreparationWorker",
    "phase": "EOPI",
    "status": "Successful"
  }
]
```

🧠 **Action**: Show per-phase status. If `status !== Successful`, flag it.

---

#### 6. ✅ **Fetch Status Verifiers**

**Endpoint**:

```http
GET /tachyon/coa/tenants/${TENANT_ID}/coas/${coaCode}/cbus/${formattedStartTime}/statusVerificationReports
```

**Response**:

```json
{
  "contents": [
    {
      "phase": "BOFI",
      "report": {
        "status": { "value": "ALLOW" },
        "tasks": [
          { "name": "Activate Scra Status", "status": { "value": "SUCCESS" } }
        ]
      }
    }
  ]
}
```

🧠 **Action**: Track if `status.value !== "ALLOW"` or `"FORCE ALLOW"`

---

### 🛍️ EoD Phase Completion Rule

> Consider EoD as **completed** only if next day's `BOPI` and `BOFI` are marked completed in `phaseTransitionHistory`.

---

Core Capabilities to Implement
Functionality	Description
getCalendars()	Fetch list of calendars and ask user to choose
getEnabledCoA(calendarId)	Get CoA matching calendarId with status "ENABLED"
getCurrentCBU(calendarId)	Get current book date + phase + state
getPeriods(clockId, cycleId)	Get start & end period
getWorkers(coaId)	List workers with their phase and status
getVerifiers(coaCode, startTime)	List verifiers per phase and status
getEodPhaseStatus()	Return current EoD phase and status (from currentCBU)
getFailedWorkers()	Return workers with status !== "Successful"
getPendingVerifiers()	Return verifiers with status !== "ALLOW" or "FORCE ALLOW"
isEodCompleted()	Check if next-day BOPI and BOFI phases completed

This prompt serves as a full specification for creating an intelligent,
 API-driven EoD MCP server capable of answering business queries and initiating actions.
  (Node.js/NestJS/Vue frontend, etc.).